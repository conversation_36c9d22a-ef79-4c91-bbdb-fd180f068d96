package com.github.tvbox.osc.ui.dialog;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.github.tvbox.osc.R;

/**
 * 加载中对话框
 */
public class LoadingDialog extends Dialog {
    
    private TextView tvMessage;
    
    public LoadingDialog(@NonNull Context context) {
        super(context, R.style.CustomDialogStyle);
        init();
    }
    
    private void init() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.dialog_loading, null);
        tvMessage = view.findViewById(R.id.tvMessage);
        setContentView(view);
        
        setCancelable(false);
        setCanceledOnTouchOutside(false);
    }
    
    public void setMessage(String message) {
        if (tvMessage != null) {
            tvMessage.setText(message);
        }
    }
} 