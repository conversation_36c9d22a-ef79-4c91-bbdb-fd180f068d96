package com.github.tvbox.osc.ui.dialog;

import android.content.Context;
import android.content.DialogInterface;
import android.view.View;

import androidx.annotation.NonNull;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.github.tvbox.osc.R;
import com.github.tvbox.osc.bean.Movie;
import com.github.tvbox.osc.event.RefreshEvent;
import com.github.tvbox.osc.ui.adapter.QuickSearchAdapter;
import com.github.tvbox.osc.ui.adapter.SearchWordAdapter;
import com.github.tvbox.osc.util.LOG;
import com.owen.tvrecyclerview.widget.TvRecyclerView;
import com.owen.tvrecyclerview.widget.V7LinearLayoutManager;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

public class QuickSearchDialog extends BaseDialog {
    private SearchWordAdapter searchWordAdapter;
    private QuickSearchAdapter searchAdapter;
    private TvRecyclerView mGridView;
    private TvRecyclerView mGridViewWord;

    public QuickSearchDialog(@NonNull @NotNull Context context) {
        super(context, R.style.CustomDialogStyleDim);
        setCanceledOnTouchOutside(false);
        setCancelable(true);
        setContentView(R.layout.dialog_quick_search);
        init(context);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void refresh(RefreshEvent event) {
        try {
            if (event.type == RefreshEvent.TYPE_QUICK_SEARCH) {
                if (event.obj != null) {
                    List<Movie.Video> data = (List<Movie.Video>) event.obj;
                    LOG.i("快速搜索接收到数据：" + data.size() + "条");
                    searchAdapter.addData(data);
                }
            } else if (event.type == RefreshEvent.TYPE_QUICK_SEARCH_WORD) {
                if (event.obj != null) {
                    List<String> data = (List<String>) event.obj;
                    LOG.i("快速搜索接收到关键词：" + data.size() + "条");
                    searchWordAdapter.setNewData(data);
                }
            }
        } catch (Exception e) {
            LOG.e("快速搜索刷新错误: " + e.getMessage());
        }
    }

    private void init(Context context) {
        try {
            // 注册EventBus
            if (!EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().register(this);
            }
            
            // 设置对话框关闭监听
            setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    try {
                        // 解除注册EventBus
                        if (EventBus.getDefault().isRegistered(QuickSearchDialog.this)) {
                            EventBus.getDefault().unregister(QuickSearchDialog.this);
                        }
                    } catch (Exception e) {
                        LOG.e("快速搜索对话框关闭错误: " + e.getMessage());
                    }
                }
            });
            
            // 初始化视图
            mGridView = findViewById(R.id.mGridView);
            searchAdapter = new QuickSearchAdapter();
            mGridView.setHasFixedSize(true);
            // lite
            mGridView.setLayoutManager(new V7LinearLayoutManager(getContext(), 1, false));
            mGridView.setAdapter(searchAdapter);
            
            // 设置点击监听
            searchAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                    try {
                        if (position < searchAdapter.getData().size()) {
                            Movie.Video video = searchAdapter.getData().get(position);
                            LOG.i("快速搜索选择了：" + video.name);
                            EventBus.getDefault().post(new RefreshEvent(RefreshEvent.TYPE_QUICK_SEARCH_SELECT, video));
                            dismiss();
                        }
                    } catch (Exception e) {
                        LOG.e("快速搜索选择错误: " + e.getMessage());
                    }
                }
            });
            searchAdapter.setNewData(new ArrayList<>());
            
            // 初始化关键词视图
            searchWordAdapter = new SearchWordAdapter();
            mGridViewWord = findViewById(R.id.mGridViewWord);
            mGridViewWord.setAdapter(searchWordAdapter);
            mGridViewWord.setLayoutManager(new V7LinearLayoutManager(context, 0, false));
            
            // 设置关键词点击监听
            searchWordAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                    try {
                        if (position < searchWordAdapter.getData().size()) {
                            // 清空现有搜索结果
                            searchAdapter.getData().clear();
                            searchAdapter.notifyDataSetChanged();
                            
                            // 发送关键词更改事件
                            String word = searchWordAdapter.getData().get(position);
                            LOG.i("快速搜索切换关键词：" + word);
                            EventBus.getDefault().post(new RefreshEvent(RefreshEvent.TYPE_QUICK_SEARCH_WORD_CHANGE, word));
                        }
                    } catch (Exception e) {
                        LOG.e("快速搜索关键词选择错误: " + e.getMessage());
                    }
                }
            });
            searchWordAdapter.setNewData(new ArrayList<>());
        } catch (Exception e) {
            LOG.e("快速搜索对话框初始化错误: " + e.getMessage());
        }
    }
}