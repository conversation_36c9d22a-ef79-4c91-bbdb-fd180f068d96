package com.github.tvbox.osc.ui.dialog;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.widget.TextView;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;

import com.github.tvbox.osc.R;
import com.github.tvbox.osc.ui.adapter.SelectDialogAdapter;
import com.github.tvbox.osc.util.DebugHelper;
import com.owen.tvrecyclerview.widget.TvRecyclerView;

import org.jetbrains.annotations.NotNull;

import java.util.List;

public class SelectDialog<T> extends BaseDialog {
    private static final String TAG = "SelectDialog";
    
    public SelectDialog(@NonNull @NotNull Context context) {
        super(context);
        setContentView(R.layout.dialog_select);
    }

    public SelectDialog(@NonNull @NotNull Context context, int resId) {
        super(context);
        setContentView(resId);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        DebugHelper.logDebug("SelectDialog onCreate开始");
        try {
            // 确保窗口存在且参数可以设置
            if (getWindow() != null) {
                WindowManager.LayoutParams params = getWindow().getAttributes();
                params.width = WindowManager.LayoutParams.MATCH_PARENT;
                params.height = WindowManager.LayoutParams.MATCH_PARENT;
                getWindow().setAttributes(params);
                getWindow().setBackgroundDrawableResource(R.color.color_99000000);
            }
            
            DebugHelper.logDebug("SelectDialog onCreate完成");
        } catch (Exception e) {
            Log.e(TAG, "创建SelectDialog出错: " + e.getMessage());
            DebugHelper.logError("创建SelectDialog出错", e);
        }
    }

    public void setTip(String tip) {
        try {
            TextView textView = findViewById(R.id.title);
            if (textView != null) {
                textView.setText(tip);
            } else {
                DebugHelper.logError("SelectDialog的title视图为空", null);
            }
        } catch (Exception e) {
            DebugHelper.logError("设置SelectDialog提示出错", e);
        }
    }

    public void setAdapter(SelectDialogAdapter.SelectDialogInterface<T> sourceBeanSelectDialogInterface, DiffUtil.ItemCallback<T> sourceBeanItemCallback, List<T> data, int select) {
        try {
            DebugHelper.logDebug("SelectDialog setAdapter开始，数据项数: " + (data != null ? data.size() : 0));
            
            SelectDialogAdapter<T> adapter = new SelectDialogAdapter<>(sourceBeanSelectDialogInterface, sourceBeanItemCallback);
            adapter.setData(data, select);
            
            TvRecyclerView tvRecyclerView = findViewById(R.id.list);
            if (tvRecyclerView != null) {
                tvRecyclerView.setAdapter(adapter);
                tvRecyclerView.setSelectedPosition(select);
                tvRecyclerView.post(() -> {
                    try {
                        tvRecyclerView.scrollToPosition(select);
                        DebugHelper.logDebug("SelectDialog滚动到选中项: " + select);
                    } catch (Exception e) {
                        DebugHelper.logError("SelectDialog滚动到选中项失败", e);
                    }
                });
                
                DebugHelper.logDebug("SelectDialog设置适配器完成");
            } else {
                DebugHelper.logError("SelectDialog的RecyclerView为空", null);
            }
        } catch (Exception e) {
            DebugHelper.logError("设置SelectDialog适配器出错", e);
        }
    }
    
    @Override
    public void show() {
        try {
            DebugHelper.logDebug("准备显示SelectDialog");
            super.show();
            DebugHelper.logDebug("SelectDialog已显示");
        } catch (Exception e) {
            DebugHelper.logError("显示SelectDialog对话框出错", e);
        }
    }
}
