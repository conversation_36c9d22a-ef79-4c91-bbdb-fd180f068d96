{"spider": "https://example.com/spider.jar", "wallpaper": "https://example.com/wallpaper.jpg", "sites": [{"key": "sample_site", "name": "示例站点", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": {"token": "sample_token"}}], "parses": [{"name": "示例解析", "url": "https://example.com/parse?url=", "type": 1, "ext": {"flag": ["qiyi", "qq", "youku"]}}], "flags": ["qiyi", "qq", "youku", "mgtv"], "ijk": [{"group": "软解码", "options": [{"category": 4, "name": "opensles", "value": "0"}, {"category": 4, "name": "overlay-format", "value": "842225234"}]}], "ads": ["mimg.0c1q0l.cn", "www.googletagmanager.com"]}