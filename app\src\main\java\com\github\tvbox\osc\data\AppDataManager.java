package com.github.tvbox.osc.data;

import android.database.Cursor;
import android.database.sqlite.SQLiteException;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.github.tvbox.osc.base.App;
import com.github.tvbox.osc.util.FileUtils;

import java.io.File;
import java.io.IOException;


/**
 * 数据管理器
 * 负责数据库的初始化、升级和备份恢复
 */
public class AppDataManager {
    private static final int DB_FILE_VERSION = 7; // 更新数据库文件版本
    private static final String DB_NAME = "tvbox";
    private static AppDataManager manager;
    private static AppDataBase dbInstance;

    private AppDataManager() {
    }

    public static void init() {
        if (manager == null) {
            synchronized (AppDataManager.class) {
                if (manager == null) {
                    manager = new AppDataManager();
                }
            }
        }
    }

    static final Migration MIGRATION_1_2 = new Migration(1, 2) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            try {
                database.execSQL("ALTER TABLE sourceState ADD COLUMN tidSort TEXT");
            } catch (SQLiteException e) {
                e.printStackTrace();
            }
        }
    };

    static final Migration MIGRATION_2_3 = new Migration(2, 3) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            database.execSQL("CREATE TABLE IF NOT EXISTS `vodRecordTmp` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `vodId` TEXT, `updateTime` INTEGER NOT NULL, `sourceKey` TEXT, `data` BLOB, `dataJson` TEXT, `testMigration` INTEGER NOT NULL)");

            // Read every thing from the former Expense table
            Cursor cursor = database.query("SELECT * FROM vodRecord");

            int id;
            int vodId;
            long updateTime;
            String sourceKey;
            String dataJson;

            while (cursor.moveToNext()) {
                id = cursor.getInt(cursor.getColumnIndex("id"));
                vodId = cursor.getInt(cursor.getColumnIndex("vodId"));
                updateTime = cursor.getLong(cursor.getColumnIndex("updateTime"));
                sourceKey = cursor.getString(cursor.getColumnIndex("sourceKey"));
                dataJson = cursor.getString(cursor.getColumnIndex("dataJson"));
                database.execSQL("INSERT INTO vodRecordTmp (id, vodId, updateTime, sourceKey, dataJson, testMigration) VALUES" +
                        " ('" + id + "', '" + vodId + "', '" + updateTime + "', '" + sourceKey + "', '" + dataJson + "',0  )");
            }


            // Delete the former table
            database.execSQL("DROP TABLE vodRecord");
            // Rename the current table to the former table name so that all other code continues to work
            database.execSQL("ALTER TABLE vodRecordTmp RENAME TO vodRecord");
        }
    };

    static final Migration MIGRATION_3_4 = new Migration(3, 4) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            try {
                database.execSQL("ALTER TABLE vodRecord ADD COLUMN dataJson TEXT");
            } catch (SQLiteException e) {
                e.printStackTrace();
            }
        }
    };

    static final Migration MIGRATION_4_5 = new Migration(4, 5) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            try {
                database.execSQL("ALTER TABLE localSource ADD COLUMN type INTEGER NOT NULL DEFAULT 0");
            } catch (SQLiteException e) {
                e.printStackTrace();
            }
        }
    };
    
    // 添加用于升级缓存表的迁移
    static final Migration MIGRATION_5_6 = new Migration(5, 6) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            try {
                // 创建新的缓存表
                database.execSQL("CREATE TABLE IF NOT EXISTS `cache_new` (" +
                        "`key` TEXT NOT NULL, " +
                        "`data` BLOB, " +
                        "`size` INTEGER NOT NULL DEFAULT 0, " +
                        "`createTime` INTEGER NOT NULL DEFAULT 0, " +
                        "`accessTime` INTEGER NOT NULL DEFAULT 0, " +
                        "`expireTime` INTEGER NOT NULL DEFAULT 0, " +
                        "`type` INTEGER NOT NULL DEFAULT 0, " +
                        "`priority` INTEGER NOT NULL DEFAULT 0, " +
                        "`accessCount` INTEGER NOT NULL DEFAULT 0, " +
                        "PRIMARY KEY(`key`))");
                
                // 创建索引
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_cache_new_expireTime` ON `cache_new` (`expireTime`)");
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_cache_new_accessTime` ON `cache_new` (`accessTime`)");
                
                // 将旧表数据迁移到新表（设置默认值）
                database.execSQL("INSERT INTO `cache_new` (`key`, `data`, `size`, `createTime`, `accessTime`) " +
                                "SELECT `key`, `data`, LENGTH(`data`), " + System.currentTimeMillis() + ", " + System.currentTimeMillis() +
                                " FROM `cache`");
                
                // 删除旧表
                database.execSQL("DROP TABLE IF EXISTS `cache`");
                
                // 重命名新表
                database.execSQL("ALTER TABLE `cache_new` RENAME TO `cache`");
            } catch (SQLiteException e) {
                e.printStackTrace();
            }
        }
    };

    // 添加用于修复缓存表的lastUsedTime字段的迁移
    static final Migration MIGRATION_6_7 = new Migration(6, 7) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            try {
                Log.d("AppDataManager", "开始执行数据库迁移6-7: 确保lastUsedTime字段存在");
                
                // 检查lastUsedTime字段是否存在
                boolean lastUsedTimeExists = false;
                Cursor cursor = database.query("PRAGMA table_info(cache)");
                while (cursor.moveToNext()) {
                    String columnName = cursor.getString(cursor.getColumnIndex("name"));
                    if ("lastUsedTime".equals(columnName)) {
                        lastUsedTimeExists = true;
                        break;
                    }
                }
                cursor.close();
                
                if (!lastUsedTimeExists) {
                    Log.d("AppDataManager", "lastUsedTime字段不存在，添加此字段");
                    
                    // 确保lastUsedTime字段存在
                    try {
                        database.execSQL("ALTER TABLE cache ADD COLUMN lastUsedTime INTEGER NOT NULL DEFAULT " + System.currentTimeMillis());
                        Log.d("AppDataManager", "成功添加lastUsedTime字段");
                    } catch (SQLiteException e) {
                        Log.e("AppDataManager", "添加lastUsedTime字段失败", e);
                        
                        // 如果字段添加失败，创建新表并迁移数据
                        Log.d("AppDataManager", "尝试通过表重建来修复");
                        database.execSQL("CREATE TABLE IF NOT EXISTS `cache_new` (" +
                                "`key` TEXT NOT NULL, " +
                                "`data` BLOB, " +
                                "`size` INTEGER NOT NULL DEFAULT 0, " +
                                "`createTime` INTEGER NOT NULL DEFAULT 0, " +
                                "`accessTime` INTEGER NOT NULL DEFAULT 0, " +
                                "`lastUsedTime` INTEGER NOT NULL DEFAULT 0, " +
                                "`expireTime` INTEGER NOT NULL DEFAULT 0, " +
                                "`type` INTEGER NOT NULL DEFAULT 0, " +
                                "`priority` INTEGER NOT NULL DEFAULT 0, " +
                                "`accessCount` INTEGER NOT NULL DEFAULT 0, " +
                                "PRIMARY KEY(`key`))");
                        
                        // 迁移数据
                        long currentTime = System.currentTimeMillis();
                        database.execSQL("INSERT INTO `cache_new` SELECT `key`, `data`, `size`, `createTime`, `accessTime`, " +
                                currentTime + " as lastUsedTime, `expireTime`, `type`, `priority`, `accessCount` FROM `cache`");
                        
                        // 删除旧表
                        database.execSQL("DROP TABLE `cache`");
                        
                        // 重命名新表
                        database.execSQL("ALTER TABLE `cache_new` RENAME TO `cache`");
                        
                        // 创建索引
                        database.execSQL("CREATE INDEX IF NOT EXISTS `index_cache_expireTime` ON `cache` (`expireTime`)");
                        database.execSQL("CREATE INDEX IF NOT EXISTS `index_cache_accessTime` ON `cache` (`accessTime`)");
                        database.execSQL("CREATE INDEX IF NOT EXISTS `index_cache_lastUsedTime` ON `cache` (`lastUsedTime`)");
                        
                        Log.d("AppDataManager", "通过表重建完成修复");
                    }
                } else {
                    Log.d("AppDataManager", "lastUsedTime字段已存在，不需要修复");
                }
            } catch (Exception e) {
                Log.e("AppDataManager", "数据库迁移6-7执行出错", e);
            }
        }
    };

    static String dbPath() {
        return DB_NAME + ".v" + DB_FILE_VERSION + ".db";
    }

    public static AppDataBase get() {
        if (manager == null) {
            throw new RuntimeException("AppDataManager is no init");
        }
        if (dbInstance == null)
            dbInstance = Room.databaseBuilder(App.getInstance(), AppDataBase.class, dbPath())
                    .setJournalMode(RoomDatabase.JournalMode.TRUNCATE)
                    .addMigrations(MIGRATION_1_2)
                    .addMigrations(MIGRATION_2_3)
                    .addMigrations(MIGRATION_3_4)
                    .addMigrations(MIGRATION_4_5)
                    .addMigrations(MIGRATION_5_6)
                    .addMigrations(MIGRATION_6_7) // 添加新的迁移
                    .addCallback(new RoomDatabase.Callback() {
                        @Override
                        public void onCreate(@NonNull SupportSQLiteDatabase db) {
                            super.onCreate(db);
                            Log.d("AppDataManager", "数据库创建");
                        }

                        @Override
                        public void onOpen(@NonNull SupportSQLiteDatabase db) {
                            super.onOpen(db);
                            Log.d("AppDataManager", "数据库打开");
                        }
                    }).allowMainThreadQueries()
                    .build();
        return dbInstance;
    }

    public static boolean backup(File path) throws IOException {
        if (dbInstance != null && dbInstance.isOpen()) {
            dbInstance.close();
        }
        File db = App.getInstance().getDatabasePath(dbPath());
        if (db.exists()) {
            FileUtils.copyFile(db, path);
            return true;
        } else {
            return false;
        }
    }

    public static boolean restore(File path) throws IOException {
        if (dbInstance != null && dbInstance.isOpen()) {
            dbInstance.close();
        }
        File db = App.getInstance().getDatabasePath(dbPath());
        if (db.exists()) {
            db.delete();
        }
        if (!db.getParentFile().exists())
            db.getParentFile().mkdirs();
        FileUtils.copyFile(path, db);
        return true;
    }
}