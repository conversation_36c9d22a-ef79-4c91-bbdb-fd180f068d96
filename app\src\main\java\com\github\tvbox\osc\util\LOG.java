package com.github.tvbox.osc.util;

import android.util.Log;
import com.github.tvbox.osc.BuildConfig;

/**
 * <AUTHOR>
 * @date :2020/12/18
 * @description:
 */
public class LOG {
    private static String TAG = "TVBox";

    public static void e(String msg) {
        if (BuildConfig.DEBUG) {
            Log.e(TAG, "" + msg);
        }
    }
    
    public static void e(String msg, Throwable e) {
        Log.e(TAG, "" + msg, e);
    }

    public static void i(String msg) {
        Log.i(TAG, "" + msg);
    }

    public static void w(String msg) {
        if (BuildConfig.DEBUG) {
            Log.w(TAG, "" + msg);
        }
    }

    public static void d(String msg) {
        if (BuildConfig.DEBUG) {
            Log.d(TAG, "" + msg);
        }
    }
}