package com.github.tvbox.osc.ui.dialog;

import android.content.Context;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.github.tvbox.osc.R;
import com.github.tvbox.osc.ui.dialog.BaseDialog;
import com.github.tvbox.osc.util.FastClickCheckUtil;
import com.github.tvbox.osc.util.LocalConfigManager;
import com.github.tvbox.osc.util.LOG;
import android.content.Intent;
import android.net.Uri;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 本地配置管理对话框
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
public class LocalConfigDialog extends BaseDialog {
    private RecyclerView recyclerView;
    private LocalConfigAdapter adapter;
    private OnConfigSelectedListener listener;
    
    public interface OnConfigSelectedListener {
        void onConfigSelected(String configUrl);
    }
    
    public LocalConfigDialog(@NonNull Context context) {
        super(context);
        setContentView(R.layout.dialog_local_config);
        setCanceledOnTouchOutside(true);
        initViews();
        loadConfigs();
    }
    
    private void initViews() {
        recyclerView = findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        
        adapter = new LocalConfigAdapter();
        recyclerView.setAdapter(adapter);
        
        // 添加本地文件按钮
        findViewById(R.id.btnAddFromFile).setOnClickListener(v -> {
            if (FastClickCheckUtil.check(v)) {
                openFileChooser();
            }
        });
        
        // 从文本创建按钮
        findViewById(R.id.btnCreateFromText).setOnClickListener(v -> {
            if (FastClickCheckUtil.check(v)) {
                showCreateFromTextDialog();
            }
        });
        
        // 刷新按钮
        findViewById(R.id.btnRefresh).setOnClickListener(v -> {
            if (FastClickCheckUtil.check(v)) {
                loadConfigs();
            }
        });
        
        adapter.setOnItemClickListener((adapter, view, position) -> {
            LocalConfigManager.LocalConfigInfo config = (LocalConfigManager.LocalConfigInfo) adapter.getItem(position);
            if (config != null) {
                String configUrl = LocalConfigManager.useLocalConfig(config);
                if (configUrl != null && listener != null) {
                    listener.onConfigSelected(configUrl);
                    dismiss();
                } else {
                    Toast.makeText(getContext(), "配置文件无效", Toast.LENGTH_SHORT).show();
                }
            }
        });
        
        adapter.setOnItemLongClickListener((adapter, view, position) -> {
            LocalConfigManager.LocalConfigInfo config = (LocalConfigManager.LocalConfigInfo) adapter.getItem(position);
            if (config != null) {
                showConfigOptionsDialog(config);
            }
            return true;
        });
    }
    
    private void loadConfigs() {
        List<LocalConfigManager.LocalConfigInfo> configs = LocalConfigManager.getLocalConfigList();
        adapter.setNewData(configs);
        
        TextView emptyView = findViewById(R.id.tvEmpty);
        if (configs.isEmpty()) {
            emptyView.setVisibility(View.VISIBLE);
            emptyView.setText("暂无本地配置文件\n点击下方按钮添加配置");
        } else {
            emptyView.setVisibility(View.GONE);
        }
    }
    
    private void openFileChooser() {
        // 使用简单的路径输入方式
        android.widget.EditText etPath = new android.widget.EditText(getContext());
        etPath.setHint("请输入配置文件完整路径，如：/storage/emulated/0/Download/config.json");
        etPath.setMinLines(2);

        android.widget.LinearLayout layout = new android.widget.LinearLayout(getContext());
        layout.setOrientation(android.widget.LinearLayout.VERTICAL);
        layout.setPadding(50, 20, 50, 20);
        layout.addView(etPath);

        new androidx.appcompat.app.AlertDialog.Builder(getContext())
                .setTitle("选择配置文件")
                .setMessage("请输入配置文件的完整路径：")
                .setView(layout)
                .setPositiveButton("确定", (dialog, which) -> {
                    String path = etPath.getText().toString().trim();
                    if (!path.isEmpty()) {
                        File file = new File(path);
                        if (file.exists() && file.isFile()) {
                            LOG.i("选择的配置文件: " + path);
                            showAddConfigDialog(file);
                        } else {
                            Toast.makeText(getContext(), "文件不存在或路径无效", Toast.LENGTH_SHORT).show();
                        }
                    }
                })
                .setNegativeButton("取消", null)
                .show();
    }
    
    private void showAddConfigDialog(File file) {
        EditText etName = new EditText(getContext());
        etName.setHint("配置名称");
        etName.setText(file.getName().replace(".json", "").replace(".txt", ""));
        
        EditText etDesc = new EditText(getContext());
        etDesc.setHint("配置描述（可选）");
        
        new androidx.appcompat.app.AlertDialog.Builder(getContext())
                .setTitle("添加本地配置")
                .setMessage("文件: " + file.getName() + "\n大小: " + formatFileSize(file.length()))
                .setView(createInputLayout(etName, etDesc))
                .setPositiveButton("添加", (dialog, which) -> {
                    String name = etName.getText().toString().trim();
                    String desc = etDesc.getText().toString().trim();
                    
                    if (name.isEmpty()) {
                        Toast.makeText(getContext(), "请输入配置名称", Toast.LENGTH_SHORT).show();
                        return;
                    }
                    
                    if (LocalConfigManager.addLocalConfig(file, name, desc)) {
                        Toast.makeText(getContext(), "配置添加成功", Toast.LENGTH_SHORT).show();
                        loadConfigs();
                    } else {
                        Toast.makeText(getContext(), "配置添加失败", Toast.LENGTH_SHORT).show();
                    }
                })
                .setNegativeButton("取消", null)
                .show();
    }
    
    private void showCreateFromTextDialog() {
        EditText etName = new EditText(getContext());
        etName.setHint("配置名称");
        
        EditText etDesc = new EditText(getContext());
        etDesc.setHint("配置描述（可选）");
        
        EditText etContent = new EditText(getContext());
        etContent.setHint("粘贴JSON配置内容...");
        etContent.setMinLines(5);
        etContent.setMaxLines(10);
        
        new androidx.appcompat.app.AlertDialog.Builder(getContext())
                .setTitle("从文本创建配置")
                .setView(createInputLayout(etName, etDesc, etContent))
                .setPositiveButton("创建", (dialog, which) -> {
                    String name = etName.getText().toString().trim();
                    String desc = etDesc.getText().toString().trim();
                    String content = etContent.getText().toString().trim();
                    
                    if (name.isEmpty() || content.isEmpty()) {
                        Toast.makeText(getContext(), "请输入配置名称和内容", Toast.LENGTH_SHORT).show();
                        return;
                    }
                    
                    if (LocalConfigManager.createLocalConfigFromText(content, name, desc)) {
                        Toast.makeText(getContext(), "配置创建成功", Toast.LENGTH_SHORT).show();
                        loadConfigs();
                    } else {
                        Toast.makeText(getContext(), "配置创建失败，请检查JSON格式", Toast.LENGTH_SHORT).show();
                    }
                })
                .setNegativeButton("取消", null)
                .show();
    }
    
    private void showConfigOptionsDialog(LocalConfigManager.LocalConfigInfo config) {
        new androidx.appcompat.app.AlertDialog.Builder(getContext())
                .setTitle(config.name)
                .setMessage("文件大小: " + formatFileSize(config.fileSize) + 
                           "\n创建时间: " + formatTime(config.createTime) +
                           "\n描述: " + (config.description != null ? config.description : "无"))
                .setPositiveButton("使用", (dialog, which) -> {
                    String configUrl = LocalConfigManager.useLocalConfig(config);
                    if (configUrl != null && listener != null) {
                        listener.onConfigSelected(configUrl);
                        dismiss();
                    }
                })
                .setNegativeButton("删除", (dialog, which) -> {
                    if (LocalConfigManager.deleteLocalConfig(config)) {
                        Toast.makeText(getContext(), "配置已删除", Toast.LENGTH_SHORT).show();
                        loadConfigs();
                    }
                })
                .setNeutralButton("取消", null)
                .show();
    }
    
    private android.widget.LinearLayout createInputLayout(EditText... editTexts) {
        android.widget.LinearLayout layout = new android.widget.LinearLayout(getContext());
        layout.setOrientation(android.widget.LinearLayout.VERTICAL);
        layout.setPadding(50, 20, 50, 20);
        
        for (EditText et : editTexts) {
            android.widget.LinearLayout.LayoutParams params = new android.widget.LinearLayout.LayoutParams(
                    android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                    android.widget.LinearLayout.LayoutParams.WRAP_CONTENT);
            params.setMargins(0, 10, 0, 10);
            et.setLayoutParams(params);
            layout.addView(et);
        }
        
        return layout;
    }
    
    private String formatFileSize(long size) {
        if (size < 1024) return size + " B";
        if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
        return String.format("%.1f MB", size / (1024.0 * 1024.0));
    }
    
    private String formatTime(long time) {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(new Date(time));
    }
    
    public void setOnConfigSelectedListener(OnConfigSelectedListener listener) {
        this.listener = listener;
    }
    
    /**
     * 本地配置适配器
     */
    private static class LocalConfigAdapter extends BaseQuickAdapter<LocalConfigManager.LocalConfigInfo, BaseViewHolder> {
        
        public LocalConfigAdapter() {
            super(R.layout.item_local_config);
        }
        
        @Override
        protected void convert(BaseViewHolder helper, LocalConfigManager.LocalConfigInfo item) {
            helper.setText(R.id.tvName, item.name)
                  .setText(R.id.tvDescription, item.description != null ? item.description : "无描述")
                  .setText(R.id.tvFileSize, formatFileSize(item.fileSize))
                  .setText(R.id.tvCreateTime, formatTime(item.createTime))
                  .setVisible(R.id.ivStatus, item.isValid)
                  .setImageResource(R.id.ivStatus, item.isValid ? R.drawable.ic_check : R.drawable.ic_error);
        }
        
        private String formatFileSize(long size) {
            if (size < 1024) return size + " B";
            if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
        
        private String formatTime(long time) {
            return new SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()).format(new Date(time));
        }
    }
}
