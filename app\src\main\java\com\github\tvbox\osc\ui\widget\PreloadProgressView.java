package com.github.tvbox.osc.ui.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.github.tvbox.osc.util.HawkConfig;
import com.orhanobut.hawk.Hawk;

/**
 * 预加载进度视图
 * 显示视频预加载的进度，以及当前预加载的集数信息
 */
public class PreloadProgressView extends View {
    private static final String TAG = "PreloadProgressView";
    
    // 进度相关
    private float progress = 0f;
    private String progressText = "0%";
    private String episodeName = "";
    
    // 绘制相关
    private Paint progressPaint;
    private Paint bgPaint;
    private TextPaint textPaint;
    private TextPaint namePaint;
    private RectF progressRect;
    private RectF bgRect;
    
    // 颜色配置
    private int progressColor = Color.parseColor("#4CAF50"); // 绿色
    private int bgColor = Color.parseColor("#33FFFFFF");    // 半透明白色
    private int textColor = Color.WHITE;
    
    // 尺寸配置
    private int progressHeight = 6; // dp
    
    // 是否显示
    private boolean isVisible = false;
    
    public PreloadProgressView(Context context) {
        super(context);
        init();
    }
    
    public PreloadProgressView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    
    public PreloadProgressView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    private void init() {
        // 初始化画笔
        progressPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        progressPaint.setColor(progressColor);
        progressPaint.setStyle(Paint.Style.FILL);
        
        bgPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        bgPaint.setColor(bgColor);
        bgPaint.setStyle(Paint.Style.FILL);
        
        textPaint = new TextPaint(Paint.ANTI_ALIAS_FLAG);
        textPaint.setColor(textColor);
        textPaint.setTextSize(dpToPx(14));
        textPaint.setTextAlign(Paint.Align.RIGHT);
        
        namePaint = new TextPaint(Paint.ANTI_ALIAS_FLAG);
        namePaint.setColor(textColor);
        namePaint.setTextSize(dpToPx(14));
        namePaint.setTextAlign(Paint.Align.LEFT);
        
        // 初始化矩形
        progressRect = new RectF();
        bgRect = new RectF();
        
        // 根据配置设置是否可见
        updateVisibility();
    }
    
    /**
     * 根据用户配置更新可见性
     */
    public void updateVisibility() {
        boolean showPreloadProgress = Hawk.get(HawkConfig.SHOW_PRELOAD_PROGRESS, true);
        setVisibility(showPreloadProgress ? VISIBLE : GONE);
    }
    
    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        
        // 设置进度条和背景的矩形区域
        int progressHeightPx = dpToPx(progressHeight);
        int padding = dpToPx(16);
        
        bgRect.set(padding, h - progressHeightPx - padding,
                   w - padding, h - padding);
        
        updateProgressRect();
    }
    
    private void updateProgressRect() {
        // 根据进度更新进度条矩形区域
        progressRect.set(bgRect.left, bgRect.top,
                        bgRect.left + (bgRect.width() * progress), bgRect.bottom);
    }
    
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        
        if (!isVisible) return;
        
        // 绘制背景
        canvas.drawRoundRect(bgRect, dpToPx(3), dpToPx(3), bgPaint);
        
        // 绘制进度条
        canvas.drawRoundRect(progressRect, dpToPx(3), dpToPx(3), progressPaint);
        
        // 绘制进度文本
        float textY = bgRect.top - dpToPx(8);
        canvas.drawText(progressText, bgRect.right, textY, textPaint);
        
        // 绘制集数名称
        if (episodeName != null && !episodeName.isEmpty()) {
            canvas.drawText(episodeName, bgRect.left, textY, namePaint);
        }
    }
    
    /**
     * 设置进度值(0-1)
     */
    public void setProgress(float progress) {
        if (progress < 0) progress = 0;
        if (progress > 1) progress = 1;
        
        this.progress = progress;
        this.progressText = (int)(progress * 100) + "%";
        
        updateProgressRect();
        invalidate();
    }
    
    /**
     * 设置预加载集数名称
     */
    public void setEpisodeName(String name) {
        this.episodeName = name;
        invalidate();
    }
    
    /**
     * 显示预加载进度
     */
    public void show() {
        if (getVisibility() != VISIBLE) {
            setVisibility(VISIBLE);
        }
        this.isVisible = true;
        invalidate();
    }
    
    /**
     * 隐藏预加载进度
     */
    public void hide() {
        this.isVisible = false;
        invalidate();
        
        // 延迟500ms后真正隐藏视图
        postDelayed(() -> {
            if (!isVisible) {
                setVisibility(GONE);
            }
        }, 500);
    }
    
    /**
     * dp转px
     */
    private int dpToPx(int dp) {
        float density = getContext().getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }
} 