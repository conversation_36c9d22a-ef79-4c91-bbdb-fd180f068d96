<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true" android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="8dp" />
            <solid android:color="@color/color_theme_80" />
            <stroke android:width="1dp" android:color="@color/white" />
        </shape>
    </item>
    <item android:state_focused="false" android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="8dp" />
            <solid android:color="@color/color_theme_40" />
            <stroke android:width="1dp" android:color="@color/color_FFFFFF_50" />
        </shape>
    </item>
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <corners android:radius="8dp" />
            <solid android:color="@color/color_theme_50" />
            <stroke android:width="1.5dp" android:color="@color/white" />
        </shape>
    </item>
    <item android:state_focused="false" android:state_pressed="false">
        <shape android:shape="rectangle">
            <corners android:radius="8dp" />
            <solid android:color="@color/color_3D3D3D_50" />
            <stroke android:width="1dp" android:color="@color/color_FFFFFF_15" />
        </shape>
    </item>
</selector> 