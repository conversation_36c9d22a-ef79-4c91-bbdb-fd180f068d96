package com.github.tvbox.osc.ui.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.github.tvbox.osc.R;
import com.github.tvbox.osc.base.BaseActivity;
import com.github.tvbox.osc.bean.Shop;
import com.github.tvbox.osc.ui.adapter.ExchangeAdapter;
import com.github.tvbox.osc.beanry.ExchangeBean;
import com.github.tvbox.osc.beanry.ReUserBean;
import com.github.tvbox.osc.ui.tv.widget.AlwaysMarqueeTextView;
import com.github.tvbox.osc.util.BaseR;
import com.github.tvbox.osc.util.MMkvUtils;
import com.github.tvbox.osc.util.ShopConverter;
import com.github.tvbox.osc.util.ToolUtils;
import com.google.gson.Gson;
import com.lzy.okgo.OkGo;
import com.lzy.okgo.callback.AbsCallback;
import com.lzy.okgo.model.Response;

import org.json.JSONException;
import org.json.JSONObject;


/**
 * @茶茶QQ205888578
 * @date :2023/9/25
 * 积分商城界面
 */
public class ExchangeActivity extends BaseActivity {

    // private AlwaysMarqueeTextView gongGao;

    private ExchangeAdapter mExchangeAdapter;
    private RecyclerView message_list;
    private TextView userNameTv;
    private TextView userPointsTv;
    private ReUserBean userBean;

    @Override
    protected int getLayoutResID() {
        return R.layout.activity_exchange;
    }

    @Override
    protected void init() {
        message_list = findViewById(R.id.tvShopList);
        userNameTv = findViewById(R.id.user_name_text);
        userPointsTv = findViewById(R.id.user_points_text);
        
        // 加载用户信息
        loadUserInfo();
        
        // 加载商品列表
        getShopList();
    }
    
    @SuppressLint("SetTextI18n")
    private void loadUserInfo() {
        // 获取已保存的用户信息
        userBean = MMkvUtils.loadReUserBean("");
        if (userBean != null && userBean.msg != null && userBean.msg.info != null) {
            // 用户已登录，显示用户信息
            String displayName = "";
            // 首先尝试使用保存的账号
            String savedUser = MMkvUtils.loadUser();
            if (savedUser != null && !savedUser.isEmpty()) {
                displayName = savedUser;
            } 
            // 其次使用返回的用户名，避免使用默认名称
            else if (userBean.msg.info.name != null && !userBean.msg.info.name.isEmpty() && 
                    !userBean.msg.info.name.equals("这个人没有名字!")) {
                displayName = userBean.msg.info.name;
            } else {
                displayName = "未知用户";
            }
            
            // 设置用户名和积分信息
            if (userNameTv != null) {
                userNameTv.setText("用户：" + displayName);
                userNameTv.setVisibility(View.VISIBLE);
            }
            
            if (userPointsTv != null) {
                userPointsTv.setText("当前积分：" + (userBean.msg.info.fen != null ? userBean.msg.info.fen : 0));
                userPointsTv.setVisibility(View.VISIBLE);
            }
            
            if (userBean.msg.token != null && !userBean.msg.token.isEmpty()) {
                // 从服务器获取最新用户信息(包括积分)
                getUserInfo(userBean.msg.token);
            }
        } else {
            // 用户未登录，显示提示信息
            if (userNameTv != null) {
                userNameTv.setText("请先登录");
                userNameTv.setVisibility(View.VISIBLE);
            }
            
            if (userPointsTv != null) {
                userPointsTv.setText("登录后查看积分");
                userPointsTv.setVisibility(View.VISIBLE);
            }
        }
    }
    
    // 获取用户最新信息(积分等)
    private void getUserInfo(String token) {
        String apiUrl = ToolUtils.setApi("get_info");
        if (apiUrl == null || apiUrl.isEmpty()) {
            // API URL为空，显示错误信息
            Toast.makeText(ExchangeActivity.this, "服务器连接失败，请检查网络设置", Toast.LENGTH_SHORT).show();
            return;
        }
        
        OkGo.<String>post(apiUrl)
                .params("token", token)
                .params("t", System.currentTimeMillis() / 1000)
                .params("sign", ToolUtils.setSign("token=" + token))
                .execute(new AbsCallback<String>() {
                    @Override
                    public void onSuccess(Response<String> response) {
                        runOnUiThread(new Runnable() {
                            @SuppressLint("SetTextI18n")
                            @Override
                            public void run() {
                                try {
                                    JSONObject jo = new JSONObject(BaseR.decry_R(response.body()));
                                    if (jo.getInt("code") == 200) {
                                        // 更新用户信息
                                        if (userBean != null && userBean.msg != null && userBean.msg.info != null) {
                                            // 获取最新积分
                                            int points = jo.getJSONObject("msg").getInt("fen");
                                            userBean.msg.info.fen = points;
                                            MMkvUtils.saveReUserBean(userBean);
                                            
                                            // 更新UI显示
                                            if (userPointsTv != null) {
                                                userPointsTv.setText("当前积分：" + points);
                                            }
                                        }
                                    } else {
                                        // 用户信息获取失败
                                        Toast.makeText(ExchangeActivity.this, "获取用户信息失败", Toast.LENGTH_SHORT).show();
                                    }
                                } catch (JSONException e) {
                                    Toast.makeText(ExchangeActivity.this, "数据解析错误", Toast.LENGTH_SHORT).show();
                                    e.printStackTrace();
                                }
                            }
                        });
                    }

                    @Override
                    public String convertResponse(okhttp3.Response response) throws Throwable {
                        assert response.body() != null;
                        return response.body().string();
                    }
                });
    }

    private boolean isNotice = false;
    // 获取积分商城商品列表
    private void getShopList() {
        Log.d("tang", "getShopList");
        String apiUrl = ToolUtils.setApi("get_fenAll");
        if (apiUrl == null || apiUrl.isEmpty()) {
            // API URL为空，显示错误信息
            Toast.makeText(ExchangeActivity.this, "服务器连接失败，请检查网络设置", Toast.LENGTH_SHORT).show();
            return;
        }
        
        OkGo.<String>post(apiUrl)
                .params("t", System.currentTimeMillis() / 1000)
                .params("sign", ToolUtils.setSign("null"))
                .execute(new AbsCallback<String>() {
                    @Override
                    public void onSuccess(Response<String> response) {
                        if (ToolUtils.iniData(response, mContext)) {
                            ExchangeBean noticeData = new Gson().fromJson(BaseR.decry_R(response.body()), ExchangeBean.class);
                            if (noticeData != null && noticeData.msg.size() > 0) {
                                runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        // 将API返回的数据转换为Shop列表
                                        mExchangeAdapter = new ExchangeAdapter(ShopConverter.convertToShopList(noticeData.msg));

                                        // 设置商品兑换事件监听
                                        mExchangeAdapter.setOnItemClickListener(new OnItemClickListener() {
                                            @Override
                                            public void onItemClick(View view, int position) {
                                                // 处理兑换商品点击
                                                handleExchange(position);
                                            }
                                        });

                                        // 修改布局为横向布局
                                        // 使用普通的LinearLayoutManager即可
                                        LinearLayoutManager layoutManager = new LinearLayoutManager(
                                                ExchangeActivity.this, 
                                                LinearLayoutManager.HORIZONTAL, 
                                                false);
                                        message_list.setLayoutManager(layoutManager);
                                        
                                        // 确保列表可以获取焦点
                                        message_list.setFocusable(true);
                                        message_list.setAdapter(mExchangeAdapter);
                                    }
                                });
                            } else {
                                runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        Toast.makeText(ExchangeActivity.this, "暂无可兑换商品", Toast.LENGTH_SHORT).show();
                                    }
                                });
                            }
                        }
                    }

                    @Override
                    public String convertResponse(okhttp3.Response response) throws Throwable {
                        assert response.body() != null;
                        return response.body().string();
                    }
                });
    }

    // 处理兑换点击事件
    private void handleExchange(int position) {
        // 获取用户信息
        ReUserBean userBean = MMkvUtils.loadReUserBean("");
        if (userBean != null && ToolUtils.getIsEmpty(userBean.msg.token)) {
            // 获取商品ID
            Shop shop = mExchangeAdapter.getItem(position);
            if (shop != null) {
                String itemId = shop.getId();
                // 调用兑换API
                recHarGe(userBean.msg.token, itemId, ExchangeActivity.this);
            } else {
                Toast.makeText(this, "商品信息错误", Toast.LENGTH_SHORT).show();
            }
        } else {
            Toast.makeText(this, "请先登录哦", Toast.LENGTH_SHORT).show();
        }
    }
    
    // 定义OnItemClickListener接口
    public interface OnItemClickListener {
        void onItemClick(View view, int position);
    }
    
    //积分兑换处理
    private void recHarGe(String token, String id, Context context) {
        Log.d("token", "recHarGe: " + token);
        Log.d("id", "recHarGe: " + id);
        new Thread(() -> {
            String apiUrl = ToolUtils.setApi("get_fen");
            if (apiUrl == null || apiUrl.isEmpty()) {
                // API URL为空，显示错误信息
                if (context != null) {
                    runOnUiThread(() -> {
                        Toast.makeText(context, "服务器连接失败，请检查网络设置", Toast.LENGTH_SHORT).show();
                    });
                }
                return;
            }
            
            OkGo.<String>post(apiUrl)
                    .params("token", token)
                    .params("fid", id)  // 商品ID
                    .params("t", System.currentTimeMillis() / 1000)
                    .params("sign", ToolUtils.setSign("token=" + token + "&fid="+id))
                    .execute(new AbsCallback<String>() {
                        @Override
                        public void onSuccess(Response<String> response) {
                            try {
                                JSONObject jo = new JSONObject(BaseR.decry_R(response.body()));
                                final boolean success = jo.getInt("code") == 200;
                                final String message = jo.has("msg") ? jo.getString("msg") : (success ? "兑换成功" : "兑换失败");
                                
                                runOnUiThread(() -> {
                                    // 显示提示
                                    if (success) {
                                        ToolUtils.showToast(context, message, R.drawable.toast_smile);
                                        // 兑换成功，刷新用户积分
                                        if (userBean != null && userBean.msg != null && userBean.msg.token != null) {
                                            getUserInfo(userBean.msg.token);
                                        }
                                    } else {
                                        ToolUtils.showToast(context, message, R.drawable.toast_err);
                                    }
                                });
                            } catch (JSONException e) {
                                e.printStackTrace();
                                runOnUiThread(() -> {
                                    Toast.makeText(context, "数据解析错误", Toast.LENGTH_SHORT).show();
                                });
                            }
                        }

                        @Override
                        public void onError(Response<String> error) {
                            runOnUiThread(() -> {
                                Toast.makeText(context, "网络请求错误", Toast.LENGTH_SHORT).show();
                            });
                        }

                        @Override
                        public String convertResponse(okhttp3.Response response) throws Throwable {
                            assert response.body() != null;
                            return response.body().string();
                        }
                    });
        }).start();
    }
}