{"formatVersion": 1, "database": {"version": 6, "identityHash": "3298f62392273516a1d052bc51806427", "entities": [{"tableName": "cache", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`key` TEXT NOT NULL, `data` BLOB, `size` INTEGER NOT NULL, `createTime` INTEGER NOT NULL, `accessTime` INTEGER NOT NULL, `lastUsedTime` INTEGER NOT NULL, `expireTime` INTEGER NOT NULL, `type` INTEGER NOT NULL, `priority` INTEGER NOT NULL, `accessCount` INTEGER NOT NULL, PRIMARY KEY(`key`))", "fields": [{"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "data", "columnName": "data", "affinity": "BLOB", "notNull": false}, {"fieldPath": "size", "columnName": "size", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createTime", "columnName": "createTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "accessTime", "columnName": "accessTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastUsedTime", "columnName": "lastUsedTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "expireTime", "columnName": "expireTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "priority", "columnName": "priority", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "accessCount", "columnName": "accessCount", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["key"]}, "indices": [{"name": "index_cache_expireTime", "unique": false, "columnNames": ["expireTime"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_cache_expireTime` ON `${TABLE_NAME}` (`expireTime`)"}, {"name": "index_cache_accessTime", "unique": false, "columnNames": ["accessTime"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_cache_accessTime` ON `${TABLE_NAME}` (`accessTime`)"}], "foreignKeys": []}, {"tableName": "vodRecord", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `vodId` TEXT, `updateTime` INTEGER NOT NULL, `sourceKey` TEXT, `dataJson` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "vodId", "columnName": "vodId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "updateTime", "columnName": "updateTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sourceKey", "columnName": "sourceKey", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dataJson", "columnName": "dataJson", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "vodCollect", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `vodId` TEXT, `updateTime` INTEGER NOT NULL, `sourceKey` TEXT, `name` TEXT, `pic` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "vodId", "columnName": "vodId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "updateTime", "columnName": "updateTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sourceKey", "columnName": "sourceKey", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "pic", "columnName": "pic", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '3298f62392273516a1d052bc51806427')"]}}