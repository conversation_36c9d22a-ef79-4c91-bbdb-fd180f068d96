package com.github.tvbox.osc.bean;

/**
 * 积分商城商品模型类
 */
public class Shop {
    private String id;        // 商品ID
    private String title;     // 商品标题
    private String price;     // 商品价格（积分）
    private String remarks;   // 商品描述
    private boolean isVip;    // 是否为VIP商品

    public Shop() {
    }

    public Shop(String id, String title, String price, String remarks) {
        this.id = id;
        this.title = title;
        this.price = price;
        this.remarks = remarks;
        this.isVip = title.contains("VIP") || title.contains("会员") || title.contains("vip");
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
        // 更新VIP状态
        this.isVip = title.contains("VIP") || title.contains("会员") || title.contains("vip");
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public boolean isVip() {
        return isVip;
    }

    public void setVip(boolean vip) {
        isVip = vip;
    }
} 