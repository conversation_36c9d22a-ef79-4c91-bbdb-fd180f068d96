package com.github.tvbox.osc.ui.activity;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.viewpager.widget.ViewPager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.github.tvbox.osc.R;
import com.github.tvbox.osc.api.ApiConfig;
import com.github.tvbox.osc.base.BaseActivity;
import com.github.tvbox.osc.base.BaseLazyFragment;
import com.github.tvbox.osc.beanry.ReLevelBean;
import com.github.tvbox.osc.ui.adapter.SettingMenuAdapter;
import com.github.tvbox.osc.ui.adapter.SettingPageAdapter;
import com.github.tvbox.osc.ui.fragment.ModelSettingFragment;
import com.github.tvbox.osc.util.AppManager;
import com.github.tvbox.osc.util.HawkConfig;
import com.github.tvbox.osc.util.LOG;
import com.github.tvbox.osc.util.SettingTestHelper;
import com.github.tvbox.osc.util.MMkvUtils;
import com.orhanobut.hawk.Hawk;
import com.owen.tvrecyclerview.widget.TvRecyclerView;
import com.owen.tvrecyclerview.widget.V7LinearLayoutManager;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date :2020/12/23
 * @description:
 */
public class SettingActivity extends BaseActivity {
    private TvRecyclerView mGridView;
    private ViewPager mViewPager;
    private SettingMenuAdapter sortAdapter;
    private SettingPageAdapter pageAdapter;
    private List<BaseLazyFragment> fragments = new ArrayList<>();
    private boolean sortChange = false;
    private int defaultSelected = 0;
    private int sortFocused = 0;
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private String homeSourceKey;
    private String currentApi;
    private int homeRec;
    private int dnsOpt;
    private boolean isInitialized = false;
    private boolean isFirstEnter = true;
    private int enterCount = 0;

    @Override
    protected int getLayoutResID() {
        return R.layout.activity_setting;
    }

    @Override
    protected void init() {
        try {
            LOG.i("SettingActivity: 开始初始化设置页面");
            showLoading();

            // 运行设置页面测试（仅在调试模式下）
            SettingTestHelper.runSettingTest(this);

            // 检查ApiConfig是否已初始化
            if (!checkApiConfigReady()) {
                LOG.w("SettingActivity: ApiConfig未准备就绪，尝试初始化");
                initApiConfigIfNeeded();
            }

            initView();
            initData();
            hideLoading();
            isInitialized = true;
            LOG.i("SettingActivity: 设置页面初始化完成");
        } catch (Exception e) {
            LOG.e("设置页面初始化失败: " + e.getMessage());
            e.printStackTrace();
            hideLoading();
            showErrorAndExit();
        }
    }

    /**
     * 检查ApiConfig是否准备就绪
     */
    private boolean checkApiConfigReady() {
        try {
            return ApiConfig.get() != null &&
                   ApiConfig.get().getSourceBeanList() != null &&
                   !ApiConfig.get().getSourceBeanList().isEmpty();
        } catch (Exception e) {
            LOG.e("检查ApiConfig状态失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 如果需要，初始化ApiConfig
     */
    private void initApiConfigIfNeeded() {
        try {
            // 尝试从缓存加载配置
            String cachedApi = Hawk.get(HawkConfig.API_URL, "");
            if (!cachedApi.isEmpty()) {
                LOG.i("SettingActivity: 尝试从缓存加载API配置");
                // 这里可以添加从缓存加载配置的逻辑
            }
        } catch (Exception e) {
            LOG.e("初始化ApiConfig失败: " + e.getMessage());
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (!isInitialized) {
            showErrorAndExit();
        }
    }

    protected void showLoading() {
        try {
            // 获取或创建加载视图
            View loadingView = findViewById(R.id.loadingView);
            if (loadingView != null) {
                loadingView.setVisibility(View.VISIBLE);
            }
        } catch (Exception e) {
            LOG.e("显示加载失败: " + e.getMessage());
        }
    }

    protected void hideLoading() {
        try {
            // 隐藏加载视图
            View loadingView = findViewById(R.id.loadingView);
            if (loadingView != null) {
                loadingView.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            LOG.e("隐藏加载失败: " + e.getMessage());
        }
    }

    private void showErrorAndExit() {
        try {
            Toast.makeText(this, "加载设置页面失败，正在返回首页", Toast.LENGTH_SHORT).show();
            if (!isFinishing()) {
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            Bundle bundle = new Bundle();
                            bundle.putBoolean("useCache", true);
                            jumpActivity(HomeActivity.class, bundle);
                            finish();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }, 2000);
            }
        } catch (Exception e) {
            e.printStackTrace();
            finish();
        }
    }

    private void initView() {
        try {
            mGridView = findViewById(R.id.mGridView);
            mViewPager = findViewById(R.id.mViewPager);
            if (mGridView == null || mViewPager == null) {
                LOG.e("设置页面控件初始化失败, 找不到必要控件");
                showErrorAndExit();
                return;
            }
            
            sortAdapter = new SettingMenuAdapter();
            mGridView.setAdapter(sortAdapter);
            mGridView.setLayoutManager(new V7LinearLayoutManager(this.mContext, 1, false));
            sortAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    if (view.getId() == R.id.tvName) {
                        if (view.getParent() != null) {
                            ((ViewGroup) view.getParent()).requestFocus();
                            sortFocused = position;
                            if (sortFocused != defaultSelected) {
                                defaultSelected = sortFocused;
                                if (mViewPager != null) {
                                    mViewPager.setCurrentItem(sortFocused, false);
                                }
                            }
                        }
                    }
                }
            });
            mGridView.setOnItemListener(new TvRecyclerView.OnItemListener() {
                @Override
                public void onItemPreSelected(TvRecyclerView parent, View itemView, int position) {
                    if (itemView != null) {
                        TextView tvName = itemView.findViewById(R.id.tvName);
                        if (tvName != null) {
                            tvName.setTextColor(getResources().getColor(R.color.color_CCFFFFFF));
                        }
                    }
                }

                @Override
                public void onItemSelected(TvRecyclerView parent, View itemView, int position) {
                    if (itemView != null) {
                        sortChange = true;
                        sortFocused = position;
                        TextView tvName = itemView.findViewById(R.id.tvName);
                        if (tvName != null) {
                            tvName.setTextColor(Color.WHITE);
                        }
                    }
                }

                @Override
                public void onItemClick(TvRecyclerView parent, View itemView, int position) {
                    if (position != defaultSelected) {
                        defaultSelected = position;
                        if (mViewPager != null) {
                            mViewPager.setCurrentItem(position, false);
                        }
                    }
                }
            });
            
            // 初始设置第一个选中
            mGridView.post(new Runnable() {
                @Override
                public void run() {
                    mGridView.setSelection(0);
                    if (mGridView.getChildAt(0) != null) {
                        mGridView.getChildAt(0).requestFocus();
                    }
                }
            });
        } catch (Exception e) {
            LOG.e("设置页面视图初始化失败: " + e.getMessage());
            e.printStackTrace();
            showErrorAndExit();
        }
    }

    private void initData() {
        try {
            LOG.d("SettingActivity: 开始初始化数据");

            currentApi = Hawk.get(HawkConfig.API_URL, "");

            // 安全获取homeSourceKey
            try {
                if (ApiConfig.get() != null && ApiConfig.get().getHomeSourceBean() != null) {
                    homeSourceKey = ApiConfig.get().getHomeSourceBean().getKey();
                    LOG.d("SettingActivity: 成功获取homeSourceKey: " + homeSourceKey);
                } else {
                    homeSourceKey = "";
                    LOG.w("SettingActivity: ApiConfig或HomeSourceBean为null，使用默认值");
                }
            } catch (Exception e) {
                homeSourceKey = "";
                LOG.e("SettingActivity: 获取homeSourceKey失败: " + e.getMessage(), e);
            }

            homeRec = Hawk.get(HawkConfig.HOME_REC, 0);
            dnsOpt = Hawk.get(HawkConfig.DOH_URL, 0);

            List<String> sortList = new ArrayList<>();
            sortList.add("设置其他");

            if (sortAdapter != null) {
                sortAdapter.setNewData(sortList);
                LOG.d("SettingActivity: sortAdapter数据设置成功");
            } else {
                LOG.e("SettingActivity: sortAdapter为null");
                throw new RuntimeException("sortAdapter初始化失败");
            }

            initViewPager();
            LOG.d("SettingActivity: 数据初始化完成");
        } catch (Exception e) {
            LOG.e("SettingActivity: 初始化数据失败: " + e.getMessage(), e);
            e.printStackTrace();
            showErrorAndExit();
        }
    }

    private void initViewPager() {
        try {
            LOG.d("SettingActivity: 开始初始化ViewPager");

            fragments.clear();

            // 安全创建Fragment
            try {
                ModelSettingFragment fragment = ModelSettingFragment.newInstance();
                if (fragment != null) {
                    fragments.add(fragment);
                    LOG.d("SettingActivity: ModelSettingFragment创建成功");
                } else {
                    LOG.e("SettingActivity: ModelSettingFragment创建失败");
                    throw new RuntimeException("Fragment创建失败");
                }
            } catch (Exception e) {
                LOG.e("SettingActivity: 创建Fragment异常: " + e.getMessage(), e);
                throw e;
            }

            // 确保所有fragment正确创建
            if (fragments.isEmpty()) {
                LOG.e("设置页面ViewPager初始化失败: fragments列表为空");
                showErrorAndExit();
                return;
            }

            // 检查FragmentManager
            if (getSupportFragmentManager() == null) {
                LOG.e("SettingActivity: FragmentManager为null");
                showErrorAndExit();
                return;
            }

            pageAdapter = new SettingPageAdapter(getSupportFragmentManager(), fragments);
            if (mViewPager != null) {
                mViewPager.setAdapter(pageAdapter);
                mViewPager.setCurrentItem(0);
                mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
                    @Override
                    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                    }

                    @Override
                    public void onPageSelected(int position) {
                        defaultSelected = position;
                        if (sortAdapter != null && mGridView != null) {
                            mGridView.setSelection(position);
                        }
                    }

                    @Override
                    public void onPageScrollStateChanged(int state) {
                    }
                });
                LOG.d("SettingActivity: ViewPager初始化成功");
            } else {
                LOG.e("设置页面ViewPager初始化失败: mViewPager为null");
                showErrorAndExit();
            }
        } catch (Exception e) {
            LOG.e("设置页面ViewPager初始化失败: " + e.getMessage());
            e.printStackTrace();
            showErrorAndExit();
        }
    }

    private Runnable mDataRunnable = new Runnable() {
        @Override
        public void run() {
            if (sortChange) {
                sortChange = false;
                if (sortFocused != defaultSelected) {
                    defaultSelected = sortFocused;
                    if (mViewPager != null) {
                        mViewPager.setCurrentItem(sortFocused, false);
                    }
                }
            }
        }
    };

    private Runnable mDevModeRun = new Runnable() {
        @Override
        public void run() {
            devMode = "";
        }
    };


    public interface DevModeCallback {
        void onChange();
    }

    public static DevModeCallback callback = null;

    String devMode = "";

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        try {
            if (!isInitialized) {
                showErrorAndExit();
                return true;
            }
            
            // 处理首次进入或按键事件问题
            if (event.getAction() == KeyEvent.ACTION_DOWN) {
                enterCount++;
                
                // 前两次点击取消
                if (enterCount <= 1) {
                    return true;
                }
                
                mHandler.removeCallbacks(mDataRunnable);
                int keyCode = event.getKeyCode();
                switch (keyCode) {
                    case KeyEvent.KEYCODE_0:
                        mHandler.removeCallbacks(mDevModeRun);
                        devMode += "0";
                        mHandler.postDelayed(mDevModeRun, 200);
                        if (devMode.length() >= 4) {
                            if (callback != null) {
                                try {
                                    callback.onChange();
                                } catch (Exception e) {
                                    LOG.e("开发者模式回调错误: " + e.getMessage());
                                    e.printStackTrace();
                                }
                            }
                        }
                        break;
                }
            } else if (event.getAction() == KeyEvent.ACTION_UP) {
                mHandler.postDelayed(mDataRunnable, 200);
            }
            return super.dispatchKeyEvent(event);
        } catch (Exception e) {
            LOG.e("处理按键事件出错: " + e.getMessage());
            e.printStackTrace();
            return true;
        }
    }

    @Override
    public void onBackPressed() {
        try {
            boolean needRestart = false;
            
            // 检查home源是否改变
            if (homeSourceKey != null && !homeSourceKey.equals(Hawk.get(HawkConfig.HOME_API, ""))) {
                needRestart = true;
            }
            
            // 检查API是否改变
            if (!currentApi.equals(Hawk.get(HawkConfig.API_URL, ""))) {
                needRestart = true;
            }
            
            // 检查其他设置是否改变
            if (homeRec != Hawk.get(HawkConfig.HOME_REC, 0) || dnsOpt != Hawk.get(HawkConfig.DOH_URL, 0)) {
                needRestart = true;
            }
            
            if (needRestart) {
                AppManager.getInstance().finishAllActivity();
                if (currentApi.equals(Hawk.get(HawkConfig.API_URL, ""))) {
                    Bundle bundle = new Bundle();
                    bundle.putBoolean("useCache", true);
                    jumpActivity(HomeActivity.class, bundle);
                } else {
                    jumpActivity(HomeActivity.class);
                }
            } else {
                finish();
            }
        } catch (Exception e) {
            LOG.e("设置页面返回处理失败: " + e.getMessage());
            e.printStackTrace();
            finish();
        }
    }

    @Override
    protected void onDestroy() {
        try {
            // 清理资源
            mHandler.removeCallbacks(mDataRunnable);
            mHandler.removeCallbacks(mDevModeRun);
            callback = null; // 重要：清理静态回调
            
            // 先处理ViewPager
            if (mViewPager != null) {
                mViewPager.clearOnPageChangeListeners();
                mViewPager.setAdapter(null);
            }
            
            // 后清理fragments集合
            if (fragments != null) {
                fragments.clear();
            }
        } catch (Exception e) {
            LOG.e("设置页面销毁出错: " + e.getMessage());
            e.printStackTrace();
        }
        super.onDestroy();
    }
}