package com.github.tvbox.osc.util;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.github.tvbox.osc.api.ApiConfig;
import com.github.tvbox.osc.base.App;
import com.github.tvbox.osc.bean.SourceBean;
import com.github.tvbox.osc.bean.VodInfo;
import com.github.tvbox.osc.cache.CacheManager;
import com.github.tvbox.osc.event.RefreshEvent;
import com.github.tvbox.osc.util.HawkConfig;
import com.github.tvbox.osc.util.PreloadHelper;
import com.lzy.okgo.OkGo;
import com.lzy.okgo.callback.AbsCallback;
import com.lzy.okgo.model.Response;
import com.orhanobut.hawk.Hawk;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 多级预加载系统管理器
 * 提供了播放源预加载和视频预加载两个级别的预加载功能
 */
public class PreloadManager {
    private static final String TAG = "TVBox-Preload";
    private static volatile PreloadManager instance;
    private ExecutorService preloadExecutor;
    private Handler handler;
    
    // 存储正在预加载的任务
    private Map<String, Boolean> preloadingTasks = new ConcurrentHashMap<>();
    // 存储预加载好的播放地址
    private Map<String, String> preloadedUrls = new ConcurrentHashMap<>();
    // 存储解析好的真实播放地址
    private Map<String, Map<String, String>> resolvedPlayUrls = new ConcurrentHashMap<>();
    // 存储预缓冲的视频内容
    private Map<String, VideoPreloadInfo> preloadedContents = new ConcurrentHashMap<>();
    
    // 最大同时预加载任务数
    private static final int MAX_PRELOAD_TASKS = 2;
    // 最大缓存预加载结果数
    private static final int MAX_CACHE_COUNT = 10;
    // 预缓冲的TS分片数量
    private static final int PRELOAD_SEGMENTS = 3;
    // 最大预缓冲大小 (5MB)
    private static final int MAX_PRELOAD_SIZE = 20 * 1024 * 1024;
    // 视频缓冲超时时间 (30秒)
    private static final int PRELOAD_TIMEOUT = 30 * 1000;
    
    // 统计数据
    private int totalPreloadTasks = 0;
    private int successPreloadTasks = 0;
    private int failedPreloadTasks = 0;
    private final AtomicInteger runningTasks = new AtomicInteger(0);
    
    // 预加载功能是否启用
    private boolean isPreloadEnabled = true;
    
    // 视频内容预缓冲功能是否启用
    private boolean isContentPreloadEnabled = true;
    
    /**
     * 视频预缓冲信息类
     * 用于存储预缓冲的视频内容信息
     */
    public static class VideoPreloadInfo {
        public String name;
        public String sourceUrl;
        public String baseUrl;
        public long cacheTime;
        public long bufferSize;
        public int segmentCount;
        public Map<Integer, byte[]> segments;
        public byte[] initialBuffer; // 初始缓冲数据
        public boolean isCompleted; // 预加载是否完成
        
        public VideoPreloadInfo(String name, String sourceUrl) {
            this.name = name;
            this.sourceUrl = sourceUrl;
            this.cacheTime = System.currentTimeMillis();
            this.bufferSize = 0;
            this.segmentCount = 0;
            this.isCompleted = false;
            this.segments = new ConcurrentHashMap<>();
        }
        
        /**
         * 添加分片数据
         * @param index 分片索引
         * @param data 分片数据
         */
        public void addSegment(int index, byte[] data) {
            if (data != null && data.length > 0) {
                segments.put(index, data);
                bufferSize += data.length;
                segmentCount = segments.size();
            }
        }
        
        /**
         * 获取分片数据
         * @param index 分片索引
         * @return 分片数据
         */
        public byte[] getSegment(int index) {
            return segments.get(index);
        }
        
        /**
         * 检查是否包含特定分片
         * @param index 分片索引
         * @return 是否包含
         */
        public boolean hasSegment(int index) {
            return segments.containsKey(index);
        }
        
        /**
         * 获取预缓冲进度百分比
         * @return 进度百分比 (0-100)
         */
        public int getProgress() {
            if (isCompleted) {
                return 100;
            }
            if (segmentCount == 0) {
                return 0;
            }
            // 假设每个视频通常有10个分片
            return Math.min(100, segmentCount * 10);
        }
    }
    
    private PreloadManager() {
        preloadExecutor = Executors.newFixedThreadPool(MAX_PRELOAD_TASKS);
        handler = new Handler(Looper.getMainLooper());
        isPreloadEnabled = Hawk.get(HawkConfig.CACHE_VIDEO_PRELOAD_ENABLE, true);
        // 默认开启视频内容预缓冲，可以添加专门的设置选项
        isContentPreloadEnabled = Hawk.get(HawkConfig.CACHE_VIDEO_CONTENT_PRELOAD_ENABLE, true);
        
        // 在初始化时打印预加载状态
        logPreloadStatus();
    }
    
    private void logPreloadStatus() {
        String statusMsg = "预加载功能状态：" + (isPreloadEnabled ? "【启用】" : "【禁用】");
        String contentStatusMsg = "视频内容预缓冲：" + (isContentPreloadEnabled ? "【启用】" : "【禁用】");
        
        Log.d(TAG, "┌─────────────────────────────────────┐");
        Log.d(TAG, "│           预加载系统初始化            │");
        Log.d(TAG, "├─────────────────────────────────────┤");
        Log.d(TAG, "│ " + statusMsg + "              │");
        Log.d(TAG, "│ " + contentStatusMsg + "              │");
        Log.d(TAG, "│ 最大任务数: " + MAX_PRELOAD_TASKS + "                  │");
        Log.d(TAG, "│ 缓存容量: " + MAX_CACHE_COUNT + "                      │");
        Log.d(TAG, "│ 预缓冲分片: " + PRELOAD_SEGMENTS + "                    │");
        Log.d(TAG, "│ 最大预缓冲: " + (MAX_PRELOAD_SIZE / 1024 / 1024) + "MB                  │");
        Log.d(TAG, "└─────────────────────────────────────┘");
        
        // 显示toast消息（仅在调试时）
        if (Hawk.get(HawkConfig.DEBUG_OPEN, false)) {
            handler.post(() -> {
                Toast.makeText(App.getInstance(), 
                    statusMsg + ", " + contentStatusMsg, 
                    Toast.LENGTH_SHORT).show();
            });
        }
    }
    
    public static PreloadManager getInstance() {
        if (instance == null) {
            synchronized (PreloadManager.class) {
                if (instance == null) {
                    instance = new PreloadManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 获取视频内容预缓冲功能是否启用
     * @return 是否启用
     */
    public boolean isContentPreloadEnabled() {
        return isContentPreloadEnabled;
    }

    /**
     * 获取最大任务数
     * @return 最大任务数
     */
    public int getMaxTaskCount() {
        return MAX_PRELOAD_TASKS;
    }
    
    /**
     * 预加载下一集
     * @param vodInfo 当前播放信息
     * @param preloadNumber 预加载数量
     */
    public void preloadNextSeries(VodInfo vodInfo, int preloadNumber) {
        // 每次调用时检查预加载功能是否启用
        isPreloadEnabled = Hawk.get(HawkConfig.CACHE_VIDEO_PRELOAD_ENABLE, true);
        
        if (!isPreloadEnabled) {
            Log.w(TAG, "⚠️ 预加载功能已禁用，跳过预加载请求");
            return;
        }
        
        if (vodInfo == null || vodInfo.seriesMap == null || !vodInfo.seriesMap.containsKey(vodInfo.playFlag)) {
            Log.w(TAG, "⚠️ 预加载取消：无效的视频信息或剧集信息");
            return;
        }
        
        List<VodInfo.VodSeries> seriesList = vodInfo.seriesMap.get(vodInfo.playFlag);
        if (seriesList == null || seriesList.isEmpty()) {
            Log.w(TAG, "⚠️ 预加载取消：剧集列表为空");
            return;
        }
        
        int currentIndex = vodInfo.playIndex;
        int total = seriesList.size();
        
        Log.d(TAG, "┌─────────────────────────────────────┐");
        Log.d(TAG, "│           开始预加载下一集            │");
        Log.d(TAG, "├─────────────────────────────────────┤");
        Log.d(TAG, "│ 视频: " + vodInfo.name + " │");
        Log.d(TAG, "│ 当前播放: 第" + (currentIndex + 1) + "集 / 总共" + total + "集      │");
        Log.d(TAG, "│ 预加载数量: " + preloadNumber + "                    │");
        Log.d(TAG, "└─────────────────────────────────────┘");
        
        // 找出要预加载的集数
        List<Integer> preloadIndexes = new ArrayList<>();
        for (int i = 1; i <= preloadNumber; i++) {
            int nextIndex = currentIndex + i;
            if (nextIndex < total) {
                preloadIndexes.add(nextIndex);
            }
        }
        
        // 开始预加载
        for (Integer index : preloadIndexes) {
            VodInfo.VodSeries series = seriesList.get(index);
            String preloadKey = getPreloadKey(vodInfo.sourceKey, vodInfo.id, vodInfo.playFlag, index);
            
            Log.i(TAG, "🔄 预加载第" + (index + 1) + "集: " + series.name);
            preloadPlayUrl(vodInfo.sourceKey, vodInfo.playFlag, preloadKey, series.url);
        }
        
        // 如果没有可预加载的剧集
        if (preloadIndexes.isEmpty()) {
            Log.w(TAG, "⚠️ 没有可预加载的剧集（已是最后一集）");
        }
    }
    
    /**
     * 预加载播放URL
     */
    private void preloadPlayUrl(String sourceKey, String playFlag, String cacheKey, String playUrl) {
        // 如果已经在预加载，或者已经预加载完成，则跳过
        if (preloadingTasks.containsKey(cacheKey)) {
            Log.d(TAG, "⏭️ 跳过重复预加载: " + cacheKey);
            return;
        }
        
        if (preloadedUrls.containsKey(cacheKey)) {
            Log.d(TAG, "✅ 已有缓存，无需预加载: " + cacheKey);
            return;
        }
        
        // 标记为正在预加载
        preloadingTasks.put(cacheKey, true);
        totalPreloadTasks++;
        runningTasks.incrementAndGet();
        
        // 尝试从缓存键中提取剧集名称
        String seriesName = "第" + (cacheKey.substring(cacheKey.lastIndexOf("@") + 1).trim()) + "集";
        
        // 通知预加载开始
        PreloadHelper.getInstance().notifyPreloadStarted(playUrl, seriesName);
        
        preloadExecutor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    Log.d(TAG, "🚀 开始预加载: " + cacheKey);
                    
                    // 使用正确的方式获取播放地址
                    SourceBean sourceBean = ApiConfig.get().getSource(sourceKey);
                    if (sourceBean == null) {
                        Log.e(TAG, "❌ 预加载失败: 无法找到播放源 " + sourceKey);
                        preloadingTasks.remove(cacheKey);
                        runningTasks.decrementAndGet();
                        failedPreloadTasks++;
                        
                        // 通知预加载失败
                        PreloadHelper.getInstance().notifyPreloadFailed(playUrl, seriesName, "无法找到播放源");
                        return;
                    }
                    
                    String apiUrl = "";
                    if (sourceBean.getType() == 4) {
                        // API类型为4的处理方式
                        apiUrl = sourceBean.getApi() + "?play=" + URLEncoder.encode(playUrl) + "&flag=" + playFlag;
                        Log.d(TAG, "🔗 类型4源，使用URL: " + apiUrl);
                    } else if (sourceBean.getType() == 3) {
                        // 使用CSP处理方式，需要在主线程调用，无法在预加载线程中处理
                        Log.w(TAG, "⚠️ 类型3源不支持预加载，已跳过");
                        preloadingTasks.remove(cacheKey);
                        runningTasks.decrementAndGet();
                        
                        // 通知预加载失败
                        PreloadHelper.getInstance().notifyPreloadFailed(playUrl, seriesName, "类型3源不支持预加载");
                        return;
                    } else {
                        // 普通API类型
                        String playUrlPrefix = sourceBean.getPlayerUrl().trim();
                        if (DefaultConfig.isVideoFormat(playUrl) && playUrlPrefix.isEmpty()) {
                            // 直接可播放的格式
                            Log.d(TAG, "✅ 直接播放格式，无需解析: " + playUrl);
                            JSONObject result = new JSONObject();
                            result.put("parse", 0);
                            result.put("url", playUrl);
                            
                            // 存储预加载结果
                            preloadedUrls.put(cacheKey, result.toString());
                            preloadingTasks.remove(cacheKey);
                            
                            // 解析真实URL并缓存
                            resolveRealPlayUrl(result.toString(), cacheKey);
                            
                            Log.d(TAG, "✅ 预加载成功: " + cacheKey);
                            successPreloadTasks++;
                            runningTasks.decrementAndGet();
                            
                            // 通知预加载完成(100%进度)
                            PreloadHelper.getInstance().notifyPreloadProgress(playUrl, seriesName, 100);
                            PreloadHelper.getInstance().notifyPreloadCompleted(playUrl, seriesName);
                            
                            // 直接播放的格式也可能需要预缓冲内容
                            Log.d(TAG, "🎯 直接格式，开始预缓冲视频内容: " + playUrl);
                            preloadVideoContent(playUrl, seriesName);
                            
                            // 清理过多缓存
                            clearExcessCache();
                            return;
                        } else {
                            apiUrl = playUrlPrefix + playUrl;
                            Log.d(TAG, "🔗 使用播放器URL: " + apiUrl);
                        }
                    }
                    
                    // 对于需要网络请求的情况，使用OkGo请求
                    Log.d(TAG, "📡 发送网络请求获取播放数据: " + apiUrl);
                    OkGo.<String>get(apiUrl)
                            .tag(cacheKey)
                            .execute(new AbsCallback<String>() {
                                @Override
                                public void onSuccess(Response<String> response) {
                                    try {
                                        String json = response.body();
                                        JSONObject result = new JSONObject(json);
                                        if (!result.has("header") && !result.has("parse") && !result.has("url")) {
                                            Log.e(TAG, "❌ 预加载失败: 响应格式无效 " + cacheKey);
                                            preloadingTasks.remove(cacheKey);
                                            failedPreloadTasks++;
                                            runningTasks.decrementAndGet();
                                            
                                            // 通知预加载失败
                                            PreloadHelper.getInstance().notifyPreloadFailed(playUrl, seriesName, "响应格式无效");
                                            return;
                                        }
                                        
                                        // 解析成功，存储预加载结果
                                        preloadedUrls.put(cacheKey, json);
                                        preloadingTasks.remove(cacheKey);
                                        
                                        // 进一步解析真实URL并缓存
                                        resolveRealPlayUrl(json, cacheKey);
                                        
                                        Log.d(TAG, "✅ 预加载成功: " + cacheKey);
                                        successPreloadTasks++;
                                        runningTasks.decrementAndGet();
                                        
                                        // 通知预加载完成(100%进度)
                                        PreloadHelper.getInstance().notifyPreloadProgress(playUrl, seriesName, 100);
                                        PreloadHelper.getInstance().notifyPreloadCompleted(playUrl, seriesName);
                                        
                                        // 直接播放的格式也可能需要预缓冲内容
                                        Log.d(TAG, "🎯 直接格式，开始预缓冲视频内容: " + playUrl);
                                        preloadVideoContent(playUrl, seriesName);
                                        
                                        // 清理过多缓存
                                        clearExcessCache();
                                    } catch (Exception e) {
                                        preloadingTasks.remove(cacheKey);
                                        failedPreloadTasks++;
                                        runningTasks.decrementAndGet();
                                        Log.e(TAG, "❌ 预加载解析失败: " + e.getMessage());
                                        
                                        // 通知预加载失败
                                        PreloadHelper.getInstance().notifyPreloadFailed(playUrl, seriesName, "解析失败: " + e.getMessage());
                                    }
                                }
                                
                                @Override
                                public void onError(Response<String> response) {
                                    preloadingTasks.remove(cacheKey);
                                    failedPreloadTasks++;
                                    runningTasks.decrementAndGet();
                                    Log.e(TAG, "❌ 预加载请求失败: " + response.message());
                                    
                                    // 通知预加载失败
                                    PreloadHelper.getInstance().notifyPreloadFailed(playUrl, seriesName, "请求失败: " + response.message());
                                }
                                
                                @Override
                                public String convertResponse(okhttp3.Response response) throws Throwable {
                                    return response.body().string();
                                }
                                
                                // 下载进度回调
                                public void downloadProgress(long currentSize, long totalSize) {
                                    // 计算下载进度
                                    if (totalSize > 0) {
                                        int progress = (int) (currentSize * 100 / totalSize);
                                        // 通知进度更新
                                        PreloadHelper.getInstance().notifyPreloadProgress(playUrl, seriesName, progress);
                                    }
                                }
                            });
                } catch (Exception e) {
                    preloadingTasks.remove(cacheKey);
                    failedPreloadTasks++;
                    runningTasks.decrementAndGet();
                    Log.e(TAG, "❌ 预加载异常: " + e.getMessage());
                    
                    // 通知预加载失败
                    PreloadHelper.getInstance().notifyPreloadFailed(playUrl, seriesName, "异常: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 解析预加载数据中的真实播放地址
     */
    private void resolveRealPlayUrl(String json, String cacheKey) {
        try {
            JSONObject result = new JSONObject(json);
            if (result.has("url")) {
                String playUrl = result.optString("url");
                Log.d(TAG, "📺 解析到真实播放地址: " + playUrl);
                
                // 存储真实播放地址
                Map<String, String> headers = new HashMap<>();
                if (result.has("header")) {
                    try {
                        JSONObject headerJson = result.optJSONObject("header");
                        if (headerJson != null) {
                            for (Iterator<String> it = headerJson.keys(); it.hasNext(); ) {
                                String k = it.next();
                                headers.put(k, headerJson.optString(k));
                            }
                            Log.d(TAG, "🔑 播放地址包含" + headers.size() + "个请求头");
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "❌ 解析header错误: " + e.getMessage());
                    }
                }
                
                HashMap<String, String> urlParams = new HashMap<>();
                urlParams.put("url", playUrl);
                resolvedPlayUrls.put(cacheKey, urlParams);
            }
        } catch (JSONException e) {
            Log.e(TAG, "❌ 解析JSON失败: " + e.getMessage());
        }
    }
    
    /**
     * 清理过多的缓存，如果缓存超过最大限制
     */
    private void clearExcessCache() {
        try {
            // 使用我们添加的静态方法
            if (com.github.tvbox.osc.cache.CacheManager.getCacheSize() > com.github.tvbox.osc.cache.CacheManager.getMaxCacheSize()) {
                Log.w(TAG, "缓存超过最大限制，执行清理");
                com.github.tvbox.osc.cache.CacheManager.clearCache();
            }
        } catch (Exception e) {
            Log.e(TAG, "清理缓存异常", e);
        }
    }
    
    /**
     * 获取预加载的播放地址
     */
    public String getPreloadPlayUrl(String sourceKey, String vodId, String playFlag, int playIndex) {
        String key = getPreloadKey(sourceKey, vodId, playFlag, playIndex);
        String result = preloadedUrls.get(key);
        if (result != null) {
            Log.d(TAG, "🎯 命中预加载缓存: " + key);
        }
        return result;
    }
    
    /**
     * 获取解析后的真实播放地址
     */
    public Map<String, String> getResolvedPlayUrl(String sourceKey, String vodId, String playFlag, int playIndex) {
        String key = getPreloadKey(sourceKey, vodId, playFlag, playIndex);
        Map<String, String> result = resolvedPlayUrls.get(key);
        if (result != null) {
            Log.d(TAG, "🎯 命中真实播放地址缓存: " + key);
        }
        return result;
    }
    
    /**
     * 取消预加载任务
     */
    public void cancelPreload(String sourceKey, String vodId, String playFlag, int playIndex) {
        String key = getPreloadKey(sourceKey, vodId, playFlag, playIndex);
        Log.d(TAG, "🛑 取消预加载任务: " + key);
        OkGo.getInstance().cancelTag(key);
        preloadingTasks.remove(key);
    }
    
    /**
     * 取消所有预加载任务
     */
    public void cancelAllPreload() {
        Log.d(TAG, "🛑 取消所有预加载任务");
        for (String key : preloadingTasks.keySet()) {
            OkGo.getInstance().cancelTag(key);
        }
        preloadingTasks.clear();
    }
    
    /**
     * 清空所有缓存
     */
    public void clearCache() {
        Log.d(TAG, "🧹 清空所有预加载缓存");
        preloadedUrls.clear();
        resolvedPlayUrls.clear();
        cancelAllPreload();
    }
    
    /**
     * 生成预加载缓存的键
     */
    private String getPreloadKey(String sourceKey, String vodId, String playFlag, int playIndex) {
        return sourceKey + "@" + vodId + "@" + playFlag + "@" + playIndex;
    }
    
    /**
     * 检查是否有预加载的URL
     */
    public boolean hasPreloadedUrl(String sourceKey, String vodId, String playFlag, int playIndex) {
        String key = getPreloadKey(sourceKey, vodId, playFlag, playIndex);
        return preloadedUrls.containsKey(key);
    }
    
    /**
     * 打印预加载统计信息
     */
    public void printStatistics() {
        Log.d(TAG, "┌─────────────────────────────────────┐");
        Log.d(TAG, "│           预加载统计信息             │");
        Log.d(TAG, "├─────────────────────────────────────┤");
        Log.d(TAG, "│ 总任务数: " + totalPreloadTasks + "                      │");
        Log.d(TAG, "│ 成功任务: " + successPreloadTasks + "                      │");
        Log.d(TAG, "│ 失败任务: " + failedPreloadTasks + "                       │");
        Log.d(TAG, "│ 运行中任务: " + runningTasks.get() + "                     │");
        Log.d(TAG, "│ 缓存数量: " + preloadedUrls.size() + "/" + MAX_CACHE_COUNT + "                   │");
        Log.d(TAG, "└─────────────────────────────────────┘");
        
        // 在UI线程显示Toast
        if (Hawk.get(HawkConfig.DEBUG_OPEN, false)) {
            handler.post(() -> {
                Toast.makeText(
                    App.getInstance(), 
                    "预加载统计: 成功" + successPreloadTasks + "/总计" + totalPreloadTasks + "，缓存量" + preloadedUrls.size(), 
                    Toast.LENGTH_SHORT
                ).show();
            });
        }
    }
    
    /**
     * 通知预加载进度
     */
    private void notifyPreloadProgress(String key, String name, int progress) {
        // 仅在调试模式下显示Toast
        if (Hawk.get(HawkConfig.DEBUG_OPEN, false) && progress % 25 == 0) {
            handler.post(() -> {
                Toast.makeText(
                    App.getInstance(), 
                    "预加载 " + name + ": " + progress + "%", 
                    Toast.LENGTH_SHORT
                ).show();
            });
        }
    }
    
    /**
     * 通知预加载完成
     */
    private void notifyPreloadCompleted(String key, String name) {
        // 仅在调试模式下显示Toast
        if (Hawk.get(HawkConfig.DEBUG_OPEN, false)) {
            handler.post(() -> {
                printStatistics();
            });
        }
    }
    
    /**
     * 通知预加载失败
     */
    private void notifyPreloadFailed(String key, String name, String errorMsg) {
        // 仅在调试模式下显示Toast
        if (Hawk.get(HawkConfig.DEBUG_OPEN, false)) {
            handler.post(() -> {
                printStatistics();
            });
        }
    }
    
    /**
     * 预加载下一集(多集预加载)
     */
    public void preloadNextEpisodes(VodInfo vodInfo, int playIndex) {
        if (vodInfo == null || vodInfo.seriesMap.isEmpty()) {
            LOG.i("PreloadManager: 没有可预加载的剧集");
            return;
        }

        // 尝试预加载当前集后面的2-3集（如果有）
        // 获取当前播放分组的剧集列表
        List<VodInfo.VodSeries> seriesList = vodInfo.seriesMap.get(vodInfo.playFlag);
        if (seriesList != null && !seriesList.isEmpty()) {
            int total = seriesList.size();
            
            // 预加载优先级：下一集 > 下下集 > 上一集
            preloadEpisode(vodInfo, playIndex + 1, total, 0);
            
            if (runningTasks.get() < MAX_PRELOAD_TASKS) {
                preloadEpisode(vodInfo, playIndex + 2, total, 1);
            }
            
            if (runningTasks.get() < MAX_PRELOAD_TASKS) {
                preloadEpisode(vodInfo, playIndex - 1, total, 2);
            }
        }
    }
    
    /**
     * 预加载指定集数
     */
    private void preloadEpisode(VodInfo vodInfo, int targetIndex, int total, int delaySeconds) {
        if (targetIndex < 0 || targetIndex >= total) {
            // 索引超出范围
            return;
        }
        
        VodInfo.VodSeries series = vodInfo.seriesMap.get(vodInfo.playFlag).get(targetIndex);
        
        // 检查是否已经预加载或正在加载
        if (preloadedUrls.containsKey(series.url) || preloadingTasks.containsKey(series.url)) {
            LOG.i("PreloadManager: 剧集已预加载或正在加载: " + series.name);
            return;
        }
        
        // 延迟执行，错开请求
        handler.postDelayed(() -> {
            LOG.i("PreloadManager: 开始预加载剧集: " + series.name);
            
            // 记录为正在加载
            preloadingTasks.put(series.url, true);
            totalPreloadTasks++;
            runningTasks.incrementAndGet();
            
            // 使用线程池执行预加载任务
            preloadExecutor.execute(() -> {
                try {
                    preloadPlayUrl(vodInfo.sourceKey, series.url, series.name);
                } catch (Exception e) {
                    LOG.e("PreloadManager: 预加载错误: " + e.getMessage());
                    
                    // 出错移除记录
                    preloadingTasks.remove(series.url);
                    failedPreloadTasks++;
                    runningTasks.decrementAndGet();
                }
            });
        }, delaySeconds * 1000L);
    }
    
    /**
     * 预加载播放地址
     */
    public void preloadPlayUrl(String sourceKey, String url, String seriesName) {
        try {
            // 使用之前的逻辑处理播放地址的预加载
            // 构建缓存键
            String cacheKey = sourceKey + "_" + url;
            try {
                cacheKey = MD5.encode(cacheKey);
            } catch (Exception e) {
                LOG.e("PreloadManager: 预加载缓存键生成错误: " + e.getMessage());
                // 使用URL转义作为备选
                try {
                    cacheKey = URLEncoder.encode(cacheKey, "UTF-8");
                } catch (UnsupportedEncodingException e1) {
                    // 如果都失败了就直接使用原始字符串
                }
            }
            
            final String finalCacheKey = cacheKey;
            
            // 检查是否已缓存
            if (CacheManager.getCache(finalCacheKey) != null) {
                LOG.i("PreloadManager: 播放地址已经缓存: " + seriesName);
                
                // 已缓存，更新状态
                preloadingTasks.remove(url);
                preloadedUrls.put(url, finalCacheKey);
                successPreloadTasks++;
                runningTasks.decrementAndGet();
                
                return;
            }
            
            // 开始网络请求
            OkGo.<String>get(url)
                .tag(url)
                .execute(new AbsCallback<String>() {
                    
                    @Override
                    public void onSuccess(Response<String> response) {
                        String playUrl = response.body();
                        if (playUrl != null && !playUrl.isEmpty()) {
                            LOG.i("PreloadManager: 预加载成功: " + seriesName);
                            
                            // 添加到缓存
                            CacheManager.save(finalCacheKey, playUrl);
                            
                            // 更新状态
                            preloadingTasks.remove(url);
                            preloadedUrls.put(url, finalCacheKey);
                            successPreloadTasks++;
                            
                            // 通知预加载完成
                            notifyPreloadCompleted(url, seriesName);
                        } else {
                            LOG.e("PreloadManager: 预加载失败，返回数据为空: " + seriesName);
                            
                            // 更新状态
                            preloadingTasks.remove(url);
                            failedPreloadTasks++;
                            
                            // 通知预加载失败
                            notifyPreloadFailed(url, seriesName, "返回数据为空");
                        }
                        
                        runningTasks.decrementAndGet();
                    }
                    
                    @Override
                    public void onError(Response<String> response) {
                        LOG.e("PreloadManager: 预加载请求失败: " + seriesName);
                        
                        // 更新状态
                        preloadingTasks.remove(url);
                        failedPreloadTasks++;
                        runningTasks.decrementAndGet();
                        
                        // 通知预加载失败
                        notifyPreloadFailed(url, seriesName, response.message());
                    }
                    
                    @Override
                    public String convertResponse(okhttp3.Response response) throws Throwable {
                        if (response.body() != null) {
                            return response.body().string();
                        }
                        return "";
                    }
                    
                    // 下载进度回调
                    public void downloadProgress(long currentSize, long totalSize) {
                        // 计算下载进度
                        if (totalSize > 0) {
                            int progress = (int) (currentSize * 100 / totalSize);
                            // 通知进度更新
                            handler.post(() -> {
                                notifyPreloadProgress(url, seriesName, progress);
                            });
                        }
                    }
                });
        } catch (Exception e) {
            LOG.e("PreloadManager: 预加载执行错误: " + e.getMessage());
            preloadingTasks.remove(url);
            failedPreloadTasks++;
            runningTasks.decrementAndGet();
            
            // 通知预加载失败
            notifyPreloadFailed(url, seriesName, e.getMessage());
        }
    }
    
    /**
     * 停止指定的预加载任务
     */
    public void stopPreloadTask(String url) {
        OkGo.getInstance().cancelTag(url);
        preloadingTasks.remove(url);
    }
    
    /**
     * 停止所有预加载任务
     */
    public synchronized void stopAllTasks() {
        try {
            // 清空任务并取消所有请求
            preloadingTasks.clear();
            preloadedUrls.clear();
            preloadedContents.clear();
            OkGo.getInstance().cancelAll();
            runningTasks.set(0);
            
            Log.i(TAG, "停止所有预加载任务");
        } catch (Exception e) {
            Log.e(TAG, "停止预加载任务错误", e);
        }
    }
    
    /**
     * 预加载监听接口
     */
    public interface loadListener {
        void success(String key, String value);
        void error(String key, String errorMsg);
        void progress(String key, int progress);
    }
    
    /**
     * 预缓冲视频内容
     * @param url 视频URL
     * @param seriesName 剧集名称
     */
    private void preloadVideoContent(String url, String seriesName) {
        if (!isContentPreloadEnabled || TextUtils.isEmpty(url)) {
            Log.d(TAG, "⚠️ 视频内容预缓冲已禁用或URL为空，跳过内容预缓冲");
            return;
        }
        
        // 检查URL格式，目前只支持HLS格式的预缓冲
        if (url.endsWith(".m3u8") || url.contains(".m3u8?")) {
            preloadHlsContent(url, seriesName);
        } else {
            Log.d(TAG, "⚠️ 暂不支持预缓冲的视频格式: " + url);
            // 可以在这里添加对其他格式的支持，如mp4、flv等
        }
    }
    
    /**
     * 预缓冲HLS格式的视频内容
     * 下载m3u8文件和前几个TS分片
     */
    private void preloadHlsContent(String hlsUrl, String seriesName) {
        Log.d(TAG, "🌊 开始预缓冲HLS内容: " + seriesName);
        
        // 创建预缓冲信息对象
        VideoPreloadInfo preloadInfo = new VideoPreloadInfo(seriesName, hlsUrl);
        String cacheKey = MD5.encode(hlsUrl);
        preloadedContents.put(cacheKey, preloadInfo);
        
        // 下载m3u8文件
        OkGo.<String>get(hlsUrl)
                .tag("preload_" + cacheKey)
                .execute(new AbsCallback<String>() {
                    @Override
                    public String convertResponse(okhttp3.Response response) throws Throwable {
                        if (response.body() != null) {
                            return response.body().string();
                        }
                        return "";
                    }
                    
                    @Override
                    public void onSuccess(Response<String> response) {
                        String m3u8Content = response.body();
                        if (TextUtils.isEmpty(m3u8Content)) {
                            Log.e(TAG, "❌ m3u8文件内容为空，预缓冲失败: " + hlsUrl);
                            // 通知预加载进度
                            PreloadHelper.getInstance().notifyPreloadFailed(hlsUrl, seriesName, "m3u8文件内容为空");
                            return;
                        }
                        
                        // 保存m3u8内容
                        try {
                            preloadInfo.initialBuffer = m3u8Content.getBytes("UTF-8");
                            preloadInfo.bufferSize += preloadInfo.initialBuffer.length;
                            
                            // 解析m3u8内容，获取TS分片列表
                            List<String> tsUrls = parseM3u8(m3u8Content, hlsUrl);
                            if (tsUrls.isEmpty()) {
                                Log.w(TAG, "⚠️ 未找到TS分片，可能是master playlist: " + hlsUrl);
                                // 如果是master playlist，需要进一步处理
                                handleMasterPlaylist(m3u8Content, hlsUrl, seriesName);
                                return;
                            }
                            
                            // 通知预加载进度
                            PreloadHelper.getInstance().notifyPreloadProgress(hlsUrl, seriesName, 10);
                            
                            // 预加载前几个TS分片
                            int segmentsToPreload = Math.min(PRELOAD_SEGMENTS, tsUrls.size());
                            for (int i = 0; i < segmentsToPreload; i++) {
                                String tsUrl = tsUrls.get(i);
                                downloadTsSegment(tsUrl, hlsUrl, seriesName, preloadInfo, i, segmentsToPreload);
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "❌ 处理m3u8内容时出错: " + e.getMessage());
                            PreloadHelper.getInstance().notifyPreloadFailed(hlsUrl, seriesName, "处理m3u8失败: " + e.getMessage());
                        }
                    }
                    
                    @Override
                    public void onError(Response<String> response) {
                        Log.e(TAG, "❌ 下载m3u8文件失败: " + response.message());
                        PreloadHelper.getInstance().notifyPreloadFailed(hlsUrl, seriesName, "下载m3u8失败: " + response.message());
                    }
                });
    }
    
    /**
     * 处理HLS主播放列表
     */
    private void handleMasterPlaylist(String content, String masterUrl, String seriesName) {
        // 查找带宽最高的子播放列表
        String bestStreamUrl = "";
        int maxBandwidth = 0;
        
        String[] lines = content.split("\n");
        String baseUrl = masterUrl.substring(0, masterUrl.lastIndexOf("/") + 1);
        
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            if (line.startsWith("#EXT-X-STREAM-INF")) {
                // 解析带宽
                int bandwidth = 0;
                if (line.contains("BANDWIDTH=")) {
                    String bw = line.substring(line.indexOf("BANDWIDTH=") + 10);
                    if (bw.contains(",")) {
                        bw = bw.substring(0, bw.indexOf(","));
                    }
                    try {
                        bandwidth = Integer.parseInt(bw);
                    } catch (NumberFormatException e) {
                        // 忽略解析错误
                    }
                }
                
                // 查找下一行的URL
                if (i + 1 < lines.length) {
                    String urlLine = lines[i + 1].trim();
                    if (!urlLine.startsWith("#")) {
                        String subUrl = urlLine;
                        // 如果是相对路径，转换为绝对路径
                        if (!subUrl.startsWith("http")) {
                            if (subUrl.startsWith("/")) {
                                // 绝对路径从域名开始
                                String domain = masterUrl.substring(0, masterUrl.indexOf("/", 8));
                                subUrl = domain + subUrl;
                            } else {
                                // 相对路径从当前目录开始
                                subUrl = baseUrl + subUrl;
                            }
                        }
                        
                        // 更新最高带宽的流
                        if (bandwidth > maxBandwidth) {
                            maxBandwidth = bandwidth;
                            bestStreamUrl = subUrl;
                        }
                    }
                }
            }
        }
        
        if (!TextUtils.isEmpty(bestStreamUrl)) {
            Log.d(TAG, "🔍 找到最佳子播放列表，带宽: " + maxBandwidth + ", URL: " + bestStreamUrl);
            // 预加载找到的最佳子播放列表
            preloadHlsContent(bestStreamUrl, seriesName);
        } else {
            Log.e(TAG, "❌ 无法在主播放列表中找到子播放列表URL");
            PreloadHelper.getInstance().notifyPreloadFailed(masterUrl, seriesName, "无法解析主播放列表");
        }
    }
    
    /**
     * 解析m3u8内容，提取TS分片URL
     */
    private List<String> parseM3u8(String m3u8Content, String m3u8Url) {
        List<String> tsUrls = new ArrayList<>();
        String baseUrl = m3u8Url.substring(0, m3u8Url.lastIndexOf("/") + 1);
        
        String[] lines = m3u8Content.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (!line.startsWith("#") && line.length() > 0) {
                // 这是一个媒体片段URL
                String tsUrl = line;
                // 如果是相对路径，转换为绝对路径
                if (!tsUrl.startsWith("http")) {
                    if (tsUrl.startsWith("/")) {
                        // 绝对路径从域名开始
                        String domain = m3u8Url.substring(0, m3u8Url.indexOf("/", 8));
                        tsUrl = domain + tsUrl;
                    } else {
                        // 相对路径从当前目录开始
                        tsUrl = baseUrl + tsUrl;
                    }
                }
                tsUrls.add(tsUrl);
            }
        }
        
        return tsUrls;
    }
    
    /**
     * 下载TS分片
     */
    private void downloadTsSegment(String tsUrl, String hlsUrl, String seriesName, 
                                  VideoPreloadInfo preloadInfo, int index, int total) {
        // 检查是否已经超出预缓冲大小限制
        if (preloadInfo.bufferSize >= MAX_PRELOAD_SIZE) {
            Log.d(TAG, "⚖️ 已达到预缓冲大小限制，停止下载更多TS分片");
            
            // 如果至少下载了一个分片，则认为预缓冲成功
            if (preloadInfo.segmentCount > 0) {
                preloadInfo.isCompleted = true;
                int progress = Math.min(100, (int)(preloadInfo.segmentCount * 100 / total));
                PreloadHelper.getInstance().notifyPreloadProgress(hlsUrl, seriesName, progress);
                PreloadHelper.getInstance().notifyPreloadCompleted(hlsUrl, seriesName);
                Log.d(TAG, "✅ 预缓冲部分完成: " + seriesName + ", 已缓存" + preloadInfo.segmentCount + "个分片，共" + 
                      (preloadInfo.bufferSize / 1024) + "KB");
            }
            return;
        }
        
        String cacheKey = MD5.encode(hlsUrl);
        OkGo.<byte[]>get(tsUrl)
                .tag("preload_ts_" + cacheKey + "_" + index)
                .execute(new AbsCallback<byte[]>() {
                    @Override
                    public byte[] convertResponse(okhttp3.Response response) throws Throwable {
                        if (response.body() != null) {
                            return response.body().bytes();
                        }
                        return new byte[0];
                    }
                    
                    @Override
                    public void onSuccess(Response<byte[]> response) {
                        byte[] tsData = response.body();
                        if (tsData != null && tsData.length > 0) {
                            // 保存TS片段数据到缓存
                            preloadInfo.segments.put(index, tsData);
                            preloadInfo.bufferSize += tsData.length;
                            preloadInfo.segmentCount++;
                            
                            // 缓存到文件系统
                            try {
                                String tsCacheKey = MD5.encode(tsUrl);
                                CacheManager.save(tsCacheKey, tsData);
                            } catch (Exception e) {
                                Log.e(TAG, "❌ 缓存TS分片失败: " + e.getMessage());
                            }
                            
                            // 计算并通知进度
                            int progress = 10 + (int)((index + 1) * 90 / total);
                            PreloadHelper.getInstance().notifyPreloadProgress(hlsUrl, seriesName, progress);
                            
                            Log.d(TAG, "📦 下载TS分片成功: " + (index + 1) + "/" + total + 
                                  ", 大小:" + (tsData.length / 1024) + "KB");
                            
                            // 检查是否完成所有预缓冲
                            if (preloadInfo.segmentCount >= total) {
                                preloadInfo.isCompleted = true;
                                PreloadHelper.getInstance().notifyPreloadCompleted(hlsUrl, seriesName);
                                Log.d(TAG, "✅ 预缓冲成功完成: " + seriesName + ", 共" + 
                                     preloadInfo.segmentCount + "个分片, " + 
                                     (preloadInfo.bufferSize / 1024) + "KB");
                            }
                        } else {
                            Log.e(TAG, "❌ TS分片数据为空: " + tsUrl);
                        }
                    }
                    
                    @Override
                    public void onError(Response<byte[]> response) {
                        Log.e(TAG, "❌ 下载TS分片失败: " + response.message());
                        // 通知但不标记整个预加载失败，因为其他分片可能成功
                    }
                });
    }
    
    /**
     * 获取预缓冲的视频内容
     * @param url 视频URL
     * @return 预缓冲信息
     */
    public VideoPreloadInfo getPreloadedContent(String url) {
        if (TextUtils.isEmpty(url)) {
            return null;
        }
        
        String cacheKey = MD5.encode(url);
        VideoPreloadInfo info = preloadedContents.get(cacheKey);
        
        if (info != null) {
            Log.d(TAG, "🎯 命中视频预缓冲: " + info.name + ", 分片数: " + 
                  info.segmentCount + ", 大小: " + (info.bufferSize / 1024) + "KB");
        }
        
        return info;
    }
    
    /**
     * 清理所有预缓冲内容
     */
    public void clearPreloadedContents() {
        Log.d(TAG, "🧹 清空所有视频预缓冲内容");
        
        // 取消所有正在下载的任务
        for (String key : preloadedContents.keySet()) {
            OkGo.getInstance().cancelTag("preload_" + key);
            
            // 查找并取消相关的TS分片下载
            for (int i = 0; i < PRELOAD_SEGMENTS; i++) {
                OkGo.getInstance().cancelTag("preload_ts_" + key + "_" + i);
            }
        }
        
        // 清空预缓冲内容集合
        preloadedContents.clear();
    }
    
    /**
     * 检查URL是否已预缓冲
     */
    public boolean hasPreloadedContent(String url) {
        if (TextUtils.isEmpty(url)) {
            return false;
        }
        
        String cacheKey = MD5.encode(url);
        return preloadedContents.containsKey(cacheKey);
    }
    
    /**
     * 打印预缓冲统计信息
     */
    public void printContentStatistics() {
        int totalItems = preloadedContents.size();
        long totalSize = 0;
        int totalSegments = 0;
        
        for (VideoPreloadInfo info : preloadedContents.values()) {
            totalSize += info.bufferSize;
            totalSegments += info.segmentCount;
        }
        
        Log.d(TAG, "┌─────────────────────────────────────┐");
        Log.d(TAG, "│          视频预缓冲统计信息           │");
        Log.d(TAG, "├─────────────────────────────────────┤");
        Log.d(TAG, "│ 预缓冲项目数: " + totalItems + "                   │");
        Log.d(TAG, "│ 总缓存大小: " + (totalSize / 1024 / 1024) + "MB                  │");
        Log.d(TAG, "│ 总分片数: " + totalSegments + "                     │");
        Log.d(TAG, "└─────────────────────────────────────┘");
        
        // 显示toast消息（仅在调试时）
        if (Hawk.get(HawkConfig.DEBUG_OPEN, false)) {
            final int finalTotalItems = totalItems;
            final long finalTotalSize = totalSize;
            handler.post(() -> {
                Toast.makeText(
                    App.getInstance(), 
                    "视频预缓冲统计: " + finalTotalItems + "个项目, " + (finalTotalSize / 1024 / 1024) + "MB", 
                    Toast.LENGTH_SHORT
                ).show();
            });
        }
    }
    
    /**
     * 清理过期的预缓冲内容
     * 删除超过一定时间未使用的预缓冲内容
     */
    public void clearExpiredPreloadedContents() {
        long currentTime = System.currentTimeMillis();
        long expireTime = 30 * 60 * 1000; // 30分钟
        
        List<String> keysToRemove = new ArrayList<>();
        
        for (Map.Entry<String, VideoPreloadInfo> entry : preloadedContents.entrySet()) {
            VideoPreloadInfo info = entry.getValue();
            if (currentTime - info.cacheTime > expireTime) {
                keysToRemove.add(entry.getKey());
            }
        }
        
        for (String key : keysToRemove) {
            VideoPreloadInfo info = preloadedContents.remove(key);
            Log.d(TAG, "🗑️ 清理过期预缓冲: " + info.name);
        }
        
        if (!keysToRemove.isEmpty()) {
            Log.d(TAG, "清理过期预缓冲完成，共移除" + keysToRemove.size() + "项");
        }
    }
} 