package com.github.tvbox.osc.cache;

import android.os.AsyncTask;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.github.tvbox.osc.data.AppDataManager;
import com.github.tvbox.osc.util.FileUtils;

import java.io.File;
import java.util.List;

/**
 * 缓存助手类
 * 提供缓存管理和维护功能
 */
public class CacheHelper {
    private static final String TAG = "CacheHelper";
    
    // 自动缓存清理的间隔时间（12小时）
    private static final long AUTO_CLEAN_INTERVAL = 12 * 60 * 60 * 1000;
    
    // 定义清理阈值，当缓存大小超过总大小的70%时清理
    private static final float CACHE_CLEAN_THRESHOLD_RATIO = 0.7f;
    
    private static Handler mainHandler = new Handler(Looper.getMainLooper());
    private static Runnable autoCleanRunnable;
    private static boolean initialized = false;
    
    /**
     * 初始化缓存系统
     * 清理过期缓存并启动定时清理任务
     */
    public static void init() {
        if (initialized) {
            return;
        }
        
        try {
            Log.d(TAG, "开始初始化缓存系统");
            
            // 确保数据库已正确初始化
            if (com.github.tvbox.osc.data.AppDataManager.get() == null) {
                Log.e(TAG, "数据库管理器未初始化，缓存系统初始化失败");
                return;
            }
            
            // 执行初始清理
            AsyncTask.execute(() -> {
                try {
                    // 清理过期缓存
                    CacheManager.clearExpiredCache();
                    
                    // 检查缓存大小
                    long cacheSize = CacheManager.getTotalCacheSize();
                    int cacheCount = CacheManager.getCacheCount();
                    Log.d(TAG, "当前缓存: " + cacheCount + "项, 大小: " + formatSize(cacheSize));
                    
                    // 如果缓存过大，执行LRU清理
                    if (cacheSize > CacheManager.getMaxCacheSize() * CACHE_CLEAN_THRESHOLD_RATIO) {
                        int cleanCount = Math.max(cacheCount / 5, 10); // 清理约20%的缓存项
                        cleanLeastUsedCache(cleanCount);
                    }
                    
                    // 清理老旧视频缓存
                    cleanOldVideoCache();
                } catch (Exception e) {
                    Log.e(TAG, "缓存初始化清理失败", e);
                }
            });
            
            // 设置定时清理任务
            if (mainHandler == null) {
                mainHandler = new Handler(Looper.getMainLooper());
            }
            
            autoCleanRunnable = new Runnable() {
                @Override
                public void run() {
                    AsyncTask.execute(() -> {
                        try {
                            CacheManager.clearExpiredCache();
                            Log.d(TAG, "执行定时缓存清理");
                            
                            // 清理老旧视频缓存
                            cleanOldVideoCache();
                        } catch (Exception e) {
                            Log.e(TAG, "定时缓存清理失败", e);
                        }
                    });
                    
                    // 安排下一次清理
                    mainHandler.postDelayed(this, AUTO_CLEAN_INTERVAL);
                }
            };
            
            // 启动定时清理
            mainHandler.postDelayed(autoCleanRunnable, AUTO_CLEAN_INTERVAL);
            
            initialized = true;
            Log.d(TAG, "缓存系统初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "缓存系统初始化出错", e);
        }
    }
    
    /**
     * 停止缓存维护
     */
    public static void stopMaintenance() {
        if (autoCleanRunnable != null && mainHandler != null) {
            mainHandler.removeCallbacks(autoCleanRunnable);
        }
    }
    
    /**
     * 清理最少使用的缓存
     */
    private static void cleanLeastUsedCache(int count) {
        try {
            if (count <= 0) {
                return;
            }
            
            AsyncTask.execute(() -> {
                try {
                    // 获取最少使用的缓存项并删除
                    List<Cache> leastUsedCaches = CacheManager.getLeastRecentUsedCache(count);
                    if (leastUsedCaches != null && !leastUsedCaches.isEmpty()) {
                        // 分批处理避免一次删除过多数据
                        int batchSize = 5;
                        int totalSize = leastUsedCaches.size();
                        int processedCount = 0;
                        
                        for (int i = 0; i < totalSize; i += batchSize) {
                            int end = Math.min(i + batchSize, totalSize);
                            List<Cache> batch = leastUsedCaches.subList(i, end);
                            
                            try {
                                AppDataManager.get().getCacheDao().deleteAll(batch);
                                processedCount += batch.size();
                                Log.d(TAG, "批量删除LRU缓存: " + batch.size() + "项, 进度: " + 
                                     processedCount + "/" + totalSize);
                            } catch (Exception e) {
                                // 如果批量删除失败，尝试单个删除
                                Log.w(TAG, "批量删除失败，尝试单个删除", e);
                                for (Cache cache : batch) {
                                    try {
                                        // 只需要key就可以删除
                                        Cache toDelete = new Cache();
                                        toDelete.key = cache.key;
                                        AppDataManager.get().getCacheDao().delete(toDelete);
                                        processedCount++;
                                    } catch (Exception ex) {
                                        Log.e(TAG, "删除单个缓存项失败: " + cache.key, ex);
                                    }
                                }
                            }
                        }
                        
                        Log.d(TAG, "LRU缓存清理完成: 共处理 " + processedCount + "/" + totalSize + " 项");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "LRU缓存清理失败", e);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "执行LRU清理失败", e);
        }
    }
    
    /**
     * 清理老旧视频缓存
     * 根据用户设置的天数自动清理
     */
    private static void cleanOldVideoCache() {
        try {
            boolean autoCleanEnabled = com.orhanobut.hawk.Hawk.get(com.github.tvbox.osc.util.HawkConfig.CACHE_VIDEO_AUTO_CLEAN, true);
            if (!autoCleanEnabled) {
                return;
            }
            
            int cleanDays = com.orhanobut.hawk.Hawk.get(com.github.tvbox.osc.util.HawkConfig.CACHE_AUTO_CLEAN_DAYS, 7);
            long cleanTimeThreshold = System.currentTimeMillis() - (cleanDays * 24 * 60 * 60 * 1000L);
            
            // 清理播放器缓存目录
            String ijkCachePath = FileUtils.getCachePath() + "/ijkcaches/";
            String exoCachePath = FileUtils.getCachePath() + "/exo-video-cache/";
            String thunderCachePath = FileUtils.getCachePath() + "/thunder/";
            
            cleanOldFilesInDir(new File(ijkCachePath), cleanTimeThreshold);
            cleanOldFilesInDir(new File(exoCachePath), cleanTimeThreshold);
            cleanOldFilesInDir(new File(thunderCachePath), cleanTimeThreshold);
            
            Log.d(TAG, "清理" + cleanDays + "天前的视频缓存完成");
        } catch (Exception e) {
            Log.e(TAG, "清理老旧视频缓存失败", e);
        }
    }
    
    /**
     * 清理指定目录中的老旧文件
     * @param dir 目录
     * @param timeThreshold 时间阈值，早于此时间的文件将被删除
     */
    private static void cleanOldFilesInDir(File dir, long timeThreshold) {
        if (!dir.exists() || !dir.isDirectory()) {
            return;
        }
        
        File[] files = dir.listFiles();
        if (files == null || files.length == 0) {
            return;
        }
        
        int count = 0;
        for (File file : files) {
            if (file.isFile() && file.lastModified() < timeThreshold) {
                long fileSize = file.length();
                if (file.delete()) {
                    count++;
                    Log.d(TAG, "删除老旧缓存文件: " + file.getName() + ", 大小: " + formatSize(fileSize));
                }
            } else if (file.isDirectory()) {
                cleanOldFilesInDir(file, timeThreshold);
            }
        }
        
        if (count > 0) {
            Log.d(TAG, "目录 " + dir.getPath() + " 中清理了 " + count + " 个老旧文件");
        }
    }
    
    /**
     * 格式化缓存大小显示
     */
    public static String formatSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0f);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0f * 1024.0f));
        } else {
            return String.format("%.2f GB", size / (1024.0f * 1024.0f * 1024.0f));
        }
    }
    
    /**
     * 获取格式化的缓存大小
     */
    public static String getTotalCacheSizeFormatted() {
        return formatSize(CacheManager.getTotalCacheSize());
    }
} 