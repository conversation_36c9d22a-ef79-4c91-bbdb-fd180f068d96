package com.github.tvbox.osc.ui.widget;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.github.tvbox.osc.R;
import com.github.tvbox.osc.util.HawkConfig;
import com.github.tvbox.osc.util.NetworkUtils;
import com.orhanobut.hawk.Hawk;

/**
 * 网络状态显示控件
 * 显示当前网络连接状态，并在状态变化时自动显示提示
 */
public class NetworkStatusView extends FrameLayout implements NetworkUtils.NetworkStateListener {
    private static final String TAG = "NetworkStatusView";
    
    private View normalStatusView;
    private View errorStatusView;
    private TextView statusText;
    private ImageView statusIcon;
    private TextView errorStatusText;
    
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    private boolean isVisible = false;
    private final int AUTO_HIDE_DELAY = 3000; // 3秒后自动隐藏
    
    public NetworkStatusView(@NonNull Context context) {
        super(context);
        init(context);
    }
    
    public NetworkStatusView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }
    
    public NetworkStatusView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }
    
    private void init(Context context) {
        // 加载布局
        LayoutInflater.from(context).inflate(R.layout.view_network_status, this, true);
        
        // 初始化控件
        normalStatusView = findViewById(R.id.network_status_normal);
        errorStatusView = findViewById(R.id.network_status_error_text);
        statusText = findViewById(R.id.network_status_text);
        statusIcon = findViewById(R.id.network_status_icon);
        errorStatusText = findViewById(R.id.network_status_error_text);
        
        // 默认隐藏
        setVisibility(GONE);
        
        // 注册网络状态监听
        NetworkUtils.getInstance().addNetworkStateListener(this);
        
        // 根据设置决定是否启用
        updateEnableStatus();
    }
    
    /**
     * 更新启用状态
     */
    public void updateEnableStatus() {
        boolean enabled = Hawk.get(HawkConfig.SHOW_NETWORK_STATUS, true);
        if (enabled) {
            NetworkUtils.getInstance().startNetworkListener();
        } else {
            NetworkUtils.getInstance().stopNetworkListener();
            hide();
        }
    }
    
    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        // 取消网络状态监听
        NetworkUtils.getInstance().removeNetworkStateListener(this);
    }
    
    /**
     * 显示网络状态提示
     */
    public void showStatus(String status, boolean isError) {
        if (!Hawk.get(HawkConfig.SHOW_NETWORK_STATUS, true)) {
            return;
        }
        
        mainHandler.post(() -> {
            if (isError) {
                normalStatusView.setVisibility(GONE);
                errorStatusView.setVisibility(VISIBLE);
                errorStatusText.setText(status);
                setBackgroundResource(R.drawable.shape_network_status_error);
            } else {
                normalStatusView.setVisibility(VISIBLE);
                errorStatusView.setVisibility(GONE);
                statusText.setText(status);
                setBackgroundResource(R.drawable.shape_network_status);
                
                // 设置WiFi/数据图标
                NetworkUtils.NetworkType networkType = NetworkUtils.getInstance().getNetworkType();
                int iconRes = getNetworkTypeIcon(networkType);
                if (iconRes != 0) {
                    statusIcon.setImageResource(iconRes);
                }
            }
            
            show();
            
            // 3秒后自动隐藏
            mainHandler.removeCallbacksAndMessages(null);
            mainHandler.postDelayed(this::hide, AUTO_HIDE_DELAY);
        });
    }
    
    /**
     * 根据网络类型获取图标
     */
    private int getNetworkTypeIcon(NetworkUtils.NetworkType networkType) {
        switch (networkType) {
            case WIFI:
                return R.drawable.ic_wifi;
            case MOBILE:
                return R.drawable.ic_mobile_data;
            case ETHERNET:
                return R.drawable.ic_ethernet;
            case NONE:
            default:
                return R.drawable.ic_no_network;
        }
    }
    
    /**
     * 显示控件
     */
    public void show() {
        if (isVisible) return;
        
        isVisible = true;
        setVisibility(VISIBLE);
        
        // 显示动画
        AlphaAnimation animation = new AlphaAnimation(0f, 1f);
        animation.setDuration(300);
        startAnimation(animation);
    }
    
    /**
     * 隐藏控件
     */
    public void hide() {
        if (!isVisible) return;
        
        isVisible = false;
        
        // 隐藏动画
        AlphaAnimation animation = new AlphaAnimation(1f, 0f);
        animation.setDuration(300);
        animation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {}
            
            @Override
            public void onAnimationEnd(Animation animation) {
                setVisibility(GONE);
            }
            
            @Override
            public void onAnimationRepeat(Animation animation) {}
        });
        startAnimation(animation);
    }
    
    @Override
    public void onNetworkStateChanged(boolean isAvailable, boolean isConnected, NetworkUtils.NetworkType networkType) {
        if (!isAvailable) {
            showStatus("网络连接已断开", true);
        } else if (!isConnected) {
            showStatus("网络已连接，但无法访问互联网", true);
        } else {
            String networkName = "";
            switch (networkType) {
                case WIFI:
                    networkName = "WiFi";
                    break;
                case MOBILE:
                    networkName = "移动数据";
                    break;
                case ETHERNET:
                    networkName = "有线网络";
                    break;
                default:
                    networkName = "未知网络";
            }
            showStatus("已连接到" + networkName, false);
        }
    }
    
    public void onNetworkSpeedChanged(String downloadSpeed, String uploadSpeed) {
        // 可在此更新网络速度显示
    }
    
    /**
     * 显示网络错误提示
     */
    public void showNetworkError(String message) {
        showStatus(message, true);
    }
} 