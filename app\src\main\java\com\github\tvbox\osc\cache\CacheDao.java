package com.github.tvbox.osc.cache;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

/**
 * 缓存数据访问对象
 * 提供更丰富的缓存操作和查询方法
 */
@Dao
public interface CacheDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long save(Cache cache);

    /**
     * 注意，冒号后面必须紧跟参数名，中间不能有空格。大于小于号和冒号中间是有空格的。
     * select *from cache where【表中列名】 =:【参数名】------>等于
     * where 【表中列名】 < :【参数名】 小于
     * where 【表中列名】 between :【参数名1】 and :【参数2】------->这个区间
     * where 【表中列名】like :参数名----->模糊查询
     * where 【表中列名】 in (:【参数名集合】)---->查询符合集合内指定字段值的记录
     *
     * @param key
     * @return
     */

    //如果是一对多,这里可以写List<Cache>
    @Query("SELECT * FROM cache WHERE `key`=:key")
    Cache getCache(String key);

    //只能传递对象昂,删除时根据Cache中的主键 来比对的
    @Delete
    int delete(Cache cache);

    //只能传递对象昂,删除时根据Cache中的主键 来比对的
    @Delete
    int deleteAll(List<Cache> caches);

    //只能传递对象昂,删除时根据Cache中的主键 来比对的
    @Update(onConflict = OnConflictStrategy.REPLACE)
    int update(Cache cache);

    /**
     * 获取所有缓存
     */
    @Query("SELECT * FROM cache")
    List<Cache> getAllCache();

    /**
     * 获取指定类型的缓存
     */
    @Query("SELECT * FROM cache WHERE type=:type")
    List<Cache> getCacheByType(int type);

    /**
     * 获取已过期的缓存
     */
    @Query("SELECT * FROM cache WHERE expireTime > 0 AND expireTime < :currentTime")
    List<Cache> getExpiredCache(long currentTime);

    /**
     * 删除已过期的缓存
     */
    @Query("DELETE FROM cache WHERE expireTime > 0 AND expireTime < :currentTime")
    int deleteExpiredCache(long currentTime);

    /**
     * 获取总缓存大小
     */
    @Query("SELECT SUM(size) FROM cache")
    long getTotalSize();

    /**
     * 获取指定类型的缓存总大小
     */
    @Query("SELECT SUM(size) FROM cache WHERE type=:type")
    long getTotalSizeByType(int type);

    /**
     * 获取缓存数量
     */
    @Query("SELECT COUNT(*) FROM cache")
    int getCacheCount();

    /**
     * 获取低优先级的缓存项
     * @param limit 限制数量
     * @return 低优先级缓存列表
     */
    @Query("SELECT * FROM cache WHERE priority = 0 ORDER BY lastUsedTime ASC LIMIT :limit")
    List<Cache> getLowPriorityCache(int limit);

    /**
     * 获取低优先级的缓存项（仅返回键和基本信息，不返回实际数据内容）
     * 避免SQLiteBlobTooBigException
     * @param limit 限制数量
     * @return 低优先级缓存键列表
     */
    @SuppressWarnings(androidx.room.RoomWarnings.CURSOR_MISMATCH)
    @Query("SELECT key, size, createTime, accessTime, lastUsedTime, expireTime, type, priority, accessCount FROM cache WHERE priority = 0 ORDER BY lastUsedTime ASC LIMIT :limit")
    List<Cache> getLowPriorityCacheKeys(int limit);

    /**
     * 通过键获取单个缓存项
     * 单独获取每个缓存项可以避免一次性加载过多BLOB数据
     * @param key 缓存键
     * @return 缓存项
     */
    @Query("SELECT * FROM cache WHERE key = :key")
    Cache getCacheByKey(String key);

    /**
     * 获取最近最少使用的缓存项
     * @param limit 限制数量
     * @return 最近最少使用的缓存列表
     */
    @Query("SELECT * FROM cache ORDER BY lastUsedTime ASC LIMIT :limit")
    List<Cache> getLeastRecentUsedCache(int limit);

    /**
     * 获取最近最少使用的缓存项（仅返回键和基本信息，不返回实际数据内容）
     * 避免SQLiteBlobTooBigException
     * @param limit 限制数量
     * @return 最近最少使用的缓存键列表
     */
    @SuppressWarnings(androidx.room.RoomWarnings.CURSOR_MISMATCH)
    @Query("SELECT key, size, createTime, accessTime, lastUsedTime, expireTime, type, priority, accessCount FROM cache ORDER BY lastUsedTime ASC LIMIT :limit")
    List<Cache> getLeastRecentUsedCacheKeys(int limit);

    /**
     * 根据键前缀删除缓存
     * @param keyPrefix 键前缀
     * @return 删除的项数
     */
    @Query("DELETE FROM cache WHERE `key` LIKE :keyPrefix || '%'")
    int deleteByKeyPrefix(String keyPrefix);

    /**
     * 清空全部缓存
     */
    @Query("DELETE FROM cache")
    int clearAllCache();

    /**
     * 获取早于指定时间的缓存
     * 用于清理旧缓存
     */
    @Query("SELECT * FROM cache WHERE createTime < :timestamp")
    List<Cache> getCacheOlderThan(long timestamp);

    /**
     * 按优先级获取缓存
     * 按照从低到高的顺序排列
     */
    @Query("SELECT * FROM cache ORDER BY priority ASC")
    List<Cache> getCacheByPriorityAsc();

    /**
     * 按优先级获取缓存
     * 按照从高到低的顺序排列
     */
    @Query("SELECT * FROM cache ORDER BY priority DESC")
    List<Cache> getCacheByPriorityDesc();

    /**
     * 更新缓存优先级
     */
    @Query("UPDATE cache SET priority = :priority WHERE `key` = :key")
    int updatePriority(String key, int priority);

    /**
     * 增加访问次数
     */
    @Query("UPDATE cache SET accessCount = accessCount + 1, accessTime = :timestamp WHERE `key` = :key")
    int increaseAccessCount(String key, long timestamp);

    /**
     * 获取指定类型和优先级范围的缓存
     */
    @Query("SELECT * FROM cache WHERE type = :type AND priority BETWEEN :minPriority AND :maxPriority")
    List<Cache> getCacheByTypeAndPriority(int type, int minPriority, int maxPriority);

    /**
     * 获取总缓存大小（按类型分组）
     */
    @Query("SELECT type, SUM(CASE WHEN data IS NOT NULL THEN LENGTH(data) ELSE 0 END) as totalSize FROM cache GROUP BY type")
    List<TypeSizeInfo> getCacheSizeByType();

    /**
     * 获取某个类别的最近访问缓存
     */
    @Query("SELECT * FROM cache WHERE type = :type ORDER BY accessTime DESC LIMIT :limit")
    List<Cache> getRecentCacheByType(int type, int limit);

    /**
     * 按大小获取最大的缓存项
     */
    @Query("SELECT * FROM cache ORDER BY size DESC LIMIT :limit")
    List<Cache> getLargestCache(int limit);

    /**
     * 删除在指定时间之前创建的缓存
     * @param createTime 创建时间阈值
     * @return 删除的项数
     */
    @Query("DELETE FROM cache WHERE createTime < :createTime")
    int deleteOldCache(long createTime);
}
