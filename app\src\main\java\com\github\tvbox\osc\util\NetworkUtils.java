package com.github.tvbox.osc.util;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.NetworkRequest;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.telephony.TelephonyManager;
import android.util.Log;

import androidx.annotation.NonNull;

import com.github.tvbox.osc.base.App;
import com.orhanobut.hawk.Hawk;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 网络工具类
 * 提供网络状态监测、连接性验证等功能
 */
public class NetworkUtils {
    private static final String TAG = "NetworkUtils";
    
    private static NetworkUtils instance;
    private final ConnectivityManager connectivityManager;
    private final Handler mainHandler;
    private final ExecutorService executorService;
    private final List<NetworkStateListener> listeners;
    private boolean isNetworkAvailable;
    private boolean isConnectedToInternet;
    private boolean isMonitoring;
    
    private ConnectivityManager.NetworkCallback networkCallback;
    
    /**
     * 私有构造方法
     */
    private NetworkUtils() {
        Context context = App.getInstance();
        connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        mainHandler = new Handler(Looper.getMainLooper());
        executorService = Executors.newSingleThreadExecutor();
        listeners = new ArrayList<>();
        isNetworkAvailable = checkNetworkState();
        isConnectedToInternet = false;
        checkRealConnectionAsync();
    }
    
    /**
     * 获取实例
     */
    public static synchronized NetworkUtils getInstance() {
        if (instance == null) {
            instance = new NetworkUtils();
        }
        return instance;
    }
    
    /**
     * 开始监听网络状态变化
     */
    public void startNetworkListener() {
        if (isMonitoring) return;
        isMonitoring = true;
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            // Android 7.0及以上版本使用新API
            networkCallback = new ConnectivityManager.NetworkCallback() {
                @Override
                public void onAvailable(@NonNull Network network) {
                    LOG.i("网络连接可用");
                    isNetworkAvailable = true;
                    checkRealConnectionAsync();
                    notifyListeners();
                }
                
                @Override
                public void onLost(@NonNull Network network) {
                    LOG.i("网络连接断开");
                    isNetworkAvailable = false;
                    isConnectedToInternet = false;
                    notifyListeners();
                }
                
                @Override
                public void onCapabilitiesChanged(@NonNull Network network, @NonNull NetworkCapabilities capabilities) {
                    LOG.i("网络能力变化");
                    checkRealConnectionAsync();
                }
            };
            
            NetworkRequest.Builder builder = new NetworkRequest.Builder();
            connectivityManager.registerNetworkCallback(builder.build(), networkCallback);
        } else {
            // 对于旧版本，定期检查网络状态
            final Runnable checkRunnable = new Runnable() {
                @Override
                public void run() {
                    boolean previousState = isNetworkAvailable;
                    isNetworkAvailable = checkNetworkState();
                    
                    if (previousState != isNetworkAvailable) {
                        LOG.i("网络状态变化: " + (isNetworkAvailable ? "连接" : "断开"));
                        if (isNetworkAvailable) {
                            checkRealConnectionAsync();
                        } else {
                            isConnectedToInternet = false;
                        }
                        notifyListeners();
                    } else if (isNetworkAvailable) {
                        // 网络可用时，定期检查实际连通性
                        if (System.currentTimeMillis() % (30 * 1000) < 1000) { // 每30秒检查一次
                            checkRealConnectionAsync();
                        }
                    }
                    
                    mainHandler.postDelayed(this, 2000); // 每2秒检查一次
                }
            };
            
            mainHandler.post(checkRunnable);
        }
    }
    
    /**
     * 停止监听网络状态变化
     */
    public void stopNetworkListener() {
        if (!isMonitoring) return;
        isMonitoring = false;
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && networkCallback != null) {
            try {
                connectivityManager.unregisterNetworkCallback(networkCallback);
            } catch (Exception e) {
                LOG.e("停止网络监听出错: " + e.getMessage());
            }
        } else {
            mainHandler.removeCallbacksAndMessages(null);
        }
    }
    
    /**
     * 检查当前网络是否可用
     */
    private boolean checkNetworkState() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // 适用于API 23及以上设备
                NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(
                        connectivityManager.getActiveNetwork());
                
                if (capabilities != null) {
                    return capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) 
                            || capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
                            || capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET);
                }
            } else {
                // 适用于API 23以下设备
                NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
                return activeNetworkInfo != null && activeNetworkInfo.isConnected();
            }
        } catch (Exception e) {
            LOG.e("检查网络状态出错: " + e.getMessage());
        }
        
        return false;
    }
    
    /**
     * 异步检查实际网络连通性
     */
    private void checkRealConnectionAsync() {
        executorService.execute(() -> {
            boolean previousState = isConnectedToInternet;
            isConnectedToInternet = checkRealConnection();
            
            if (previousState != isConnectedToInternet) {
                LOG.i("互联网连接状态变化: " + (isConnectedToInternet ? "可访问" : "不可访问"));
                mainHandler.post(this::notifyListeners);
            }
        });
    }
    
    /**
     * 检查实际网络连通性
     * 通过尝试连接特定URL来验证
     */
    private boolean checkRealConnection() {
        // 检查多个常用网站以提高可靠性
        String[] testUrls = {
                "https://www.baidu.com",
                "https://www.qq.com",
                "https://www.163.com"
        };
        
        for (String testUrl : testUrls) {
            if (isUrlReachable(testUrl)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查指定URL是否可访问
     */
    private boolean isUrlReachable(String urlString) {
        HttpURLConnection connection = null;
        try {
            URL url = new URL(urlString);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(3000);
            connection.setReadTimeout(3000);
            int responseCode = connection.getResponseCode();
            return responseCode >= 200 && responseCode < 400;
        } catch (IOException e) {
            return false;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
    
    /**
     * 获取当前网络类型
     */
    public NetworkType getNetworkType() {
        if (!isNetworkAvailable) {
            return NetworkType.NONE;
        }
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(
                        connectivityManager.getActiveNetwork());
                
                if (capabilities != null) {
                    if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
                        return NetworkType.WIFI;
                    } else if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
                        return NetworkType.MOBILE;
                    } else if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)) {
                        return NetworkType.ETHERNET;
                    }
                }
            } else {
                NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
                if (activeNetworkInfo != null) {
                    switch (activeNetworkInfo.getType()) {
                        case ConnectivityManager.TYPE_WIFI:
                            return NetworkType.WIFI;
                        case ConnectivityManager.TYPE_MOBILE:
                            return NetworkType.MOBILE;
                        case ConnectivityManager.TYPE_ETHERNET:
                            return NetworkType.ETHERNET;
                    }
                }
            }
        } catch (Exception e) {
            LOG.e("获取网络类型出错: " + e.getMessage());
        }
        
        return NetworkType.UNKNOWN;
    }
    
    /**
     * 判断WiFi是否可用
     */
    public boolean isWifiAvailable() {
        return getNetworkType() == NetworkType.WIFI;
    }
    
    /**
     * 判断移动网络是否可用
     */
    public boolean isMobileAvailable() {
        return getNetworkType() == NetworkType.MOBILE;
    }
    
    /**
     * 判断网络是否可用(实例方法)
     */
    public boolean isNetworkAvailable() {
        return isNetworkAvailable;
    }
    
    /**
     * 判断是否连接到互联网
     */
    public boolean isConnectedToInternet() {
        return isNetworkAvailable && isConnectedToInternet;
    }
    
    /**
     * 获取网络状态描述
     */
    public String getNetworkStatusDescription() {
        if (!isNetworkAvailable) {
            return "网络未连接";
        } else if (!isConnectedToInternet) {
            return "网络已连接, 但无法访问互联网";
        } else {
            NetworkType type = getNetworkType();
            switch (type) {
                case WIFI:
                    return "WiFi已连接";
                case MOBILE:
                    return "移动网络已连接";
                case ETHERNET:
                    return "有线网络已连接";
                default:
                    return "网络已连接";
            }
        }
    }
    
    /**
     * 添加网络状态变化监听器
     */
    public void addNetworkStateListener(NetworkStateListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
        }
    }
    
    /**
     * 移除网络状态变化监听器
     */
    public void removeNetworkStateListener(NetworkStateListener listener) {
        if (listener != null) {
            listeners.remove(listener);
        }
    }
    
    /**
     * 通知所有监听器网络状态变化
     */
    private void notifyListeners() {
        mainHandler.post(() -> {
            for (NetworkStateListener listener : listeners) {
                listener.onNetworkStateChanged(isNetworkAvailable, isConnectedToInternet, getNetworkType());
            }
        });
    }
    
    /**
     * 网络类型枚举
     */
    public enum NetworkType {
        NONE,       // 无网络
        WIFI,       // WiFi网络
        MOBILE,     // 移动网络
        ETHERNET,   // 有线网络
        UNKNOWN     // 未知网络
    }
    
    /**
     * 网络状态变化监听器接口
     */
    public interface NetworkStateListener {
        void onNetworkStateChanged(boolean isAvailable, boolean isConnected, NetworkType networkType);
    }

    /**
     * 检查当前网络是否可用(静态方法)
     */
    public static boolean checkNetworkAvailable() {
        try {
            ConnectivityManager cm = (ConnectivityManager) App.getInstance().getSystemService(Context.CONNECTIVITY_SERVICE);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (cm != null) {
                    NetworkCapabilities capabilities = cm.getNetworkCapabilities(cm.getActiveNetwork());
                    if (capabilities != null) {
                        return capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
                                || capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
                                || capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET);
                    }
                }
            } else {
                if (cm != null) {
                    NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
                    return activeNetwork != null && activeNetwork.isConnected();
                }
            }
        } catch (Exception e) {
            LOG.e("检查网络可用性出错", e);
        }
        return false;
    }

    /**
     * 检测是否是WiFi
     */
    public static boolean isWifiConnection() {
        ConnectivityManager cm = (ConnectivityManager) App.getInstance()
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm != null) {
            NetworkInfo networkInfo = cm.getActiveNetworkInfo();
            if (networkInfo != null) {
                return networkInfo.getType() == ConnectivityManager.TYPE_WIFI;
            }
        }
        return false;
    }

    /**
     * 检测是否是移动网络
     */
    public static boolean isMobileConnection() {
        ConnectivityManager cm = (ConnectivityManager) App.getInstance()
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm != null) {
            NetworkInfo networkInfo = cm.getActiveNetworkInfo();
            if (networkInfo != null) {
                return networkInfo.getType() == ConnectivityManager.TYPE_MOBILE;
            }
        }
        return false;
    }

    /**
     * 获取网络类型名称
     */
    public static String getNetworkTypeName() {
        ConnectivityManager cm = (ConnectivityManager) App.getInstance()
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm != null) {
            NetworkInfo networkInfo = cm.getActiveNetworkInfo();
            if (networkInfo != null) {
                if (networkInfo.getType() == ConnectivityManager.TYPE_WIFI) {
                    return "WIFI";
                } else if (networkInfo.getType() == ConnectivityManager.TYPE_MOBILE) {
                    return getMobileNetworkType(networkInfo.getSubtype());
                }
            }
        }
        return "UNKNOWN";
    }

    /**
     * 获取移动网络类型名称
     */
    private static String getMobileNetworkType(int subType) {
        switch (subType) {
            case TelephonyManager.NETWORK_TYPE_GPRS:
            case TelephonyManager.NETWORK_TYPE_EDGE:
            case TelephonyManager.NETWORK_TYPE_CDMA:
            case TelephonyManager.NETWORK_TYPE_1xRTT:
            case TelephonyManager.NETWORK_TYPE_IDEN:
                return "2G";
            case TelephonyManager.NETWORK_TYPE_UMTS:
            case TelephonyManager.NETWORK_TYPE_EVDO_0:
            case TelephonyManager.NETWORK_TYPE_EVDO_A:
            case TelephonyManager.NETWORK_TYPE_HSDPA:
            case TelephonyManager.NETWORK_TYPE_HSUPA:
            case TelephonyManager.NETWORK_TYPE_HSPA:
            case TelephonyManager.NETWORK_TYPE_EVDO_B:
            case TelephonyManager.NETWORK_TYPE_EHRPD:
            case TelephonyManager.NETWORK_TYPE_HSPAP:
                return "3G";
            case TelephonyManager.NETWORK_TYPE_LTE:
                return "4G";
            default:
                return "UNKNOWN";
        }
    }

    /**
     * 获取WiFi信号强度
     * @return 0-100 信号强度百分比
     */
    public static int getWifiSignalStrength() {
        try {
            WifiManager wm = (WifiManager) App.getInstance().getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            if (wm != null) {
                WifiInfo wifiInfo = wm.getConnectionInfo();
                if (wifiInfo != null) {
                    int level = wifiInfo.getRssi();
                    // 信号强度范围通常是-100到-55，将其转换为0-5的范围
                    if (level <= -100) return 0;
                    if (level >= -55) return 5;
                    return (level + 100) * 5 / 45;
                }
            }
        } catch (Exception e) {
            LOG.e("获取WiFi信号强度出错", e);
        }
        return 0;
    }
} 