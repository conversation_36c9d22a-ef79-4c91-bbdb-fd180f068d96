package com.github.tvbox.osc.util;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.widget.Toast;

import com.github.tvbox.osc.base.App;

import java.util.HashMap;
import java.util.Map;

/**
 * 错误提示辅助类
 * 提供统一的错误处理和显示
 */
public class ErrorHelper {
    private static final String TAG = "ErrorHelper";
    private static volatile ErrorHelper instance;
    
    private final Handler mainHandler;
    private final Context context;
    
    // 记录最近的错误，避免重复显示
    private final Map<String, Long> recentErrors;
    private static final long ERROR_REPEAT_THRESHOLD = 10 * 1000; // 10秒内相同错误不重复显示
    
    // 错误分类
    public enum ErrorType {
        NETWORK("网络错误"),
        PARSE("解析错误"),
        PLAYBACK("播放错误"),
        SOURCE("片源错误"),
        SYSTEM("系统错误"),
        PRELOAD("预加载错误"),
        UNKNOWN("未知错误");
        
        private final String typeName;
        
        ErrorType(String typeName) {
            this.typeName = typeName;
        }
        
        public String getTypeName() {
            return typeName;
        }
    }
    
    private ErrorHelper() {
        context = App.getInstance();
        mainHandler = new Handler(Looper.getMainLooper());
        recentErrors = new HashMap<>();
    }
    
    public static ErrorHelper getInstance() {
        if (instance == null) {
            synchronized (ErrorHelper.class) {
                if (instance == null) {
                    instance = new ErrorHelper();
                }
            }
        }
        return instance;
    }
    
    /**
     * 显示错误提示(Toast方式)
     */
    public void showToast(ErrorType type, String message) {
        String finalMessage = formatErrorMessage(type, message);
        
        // 检查是否为重复错误
        if (isRecentError(finalMessage)) {
            LOG.i("ErrorHelper: 抑制重复错误提示: " + finalMessage);
            return;
        }
        
        // 记录错误
        markErrorShown(finalMessage);
        
        // 主线程显示
        mainHandler.post(() -> {
            Toast.makeText(context, finalMessage, Toast.LENGTH_LONG).show();
        });
    }
    
    /**
     * 显示错误提示(回调方式)
     */
    public void showError(ErrorType type, String message, ErrorCallback callback) {
        String finalMessage = formatErrorMessage(type, message);
        
        // 检查是否为重复错误
        if (isRecentError(finalMessage)) {
            LOG.i("ErrorHelper: 抑制重复错误提示: " + finalMessage);
            return;
        }
        
        // 记录错误
        markErrorShown(finalMessage);
        
        // 主线程回调
        mainHandler.post(() -> {
            if (callback != null) {
                callback.onError(type, message, getFriendlyMessage(type, message));
            }
        });
    }
    
    /**
     * 记录错误日志
     */
    public void logError(ErrorType type, String message, Throwable throwable) {
        String errorMessage = formatErrorMessage(type, message);
        
        // 记录详细日志
        if (throwable != null) {
            LOG.e(errorMessage + "\n" + throwable.getMessage());
        } else {
            LOG.e(errorMessage);
        }
    }
    
    /**
     * 格式化错误消息
     */
    private String formatErrorMessage(ErrorType type, String message) {
        return "[" + type.getTypeName() + "] " + message;
    }
    
    /**
     * 检查是否为最近显示过的错误
     */
    private boolean isRecentError(String errorMessage) {
        Long lastTime = recentErrors.get(errorMessage);
        if (lastTime == null) {
            return false;
        }
        
        long now = System.currentTimeMillis();
        return (now - lastTime) < ERROR_REPEAT_THRESHOLD;
    }
    
    /**
     * 标记错误已显示
     */
    private void markErrorShown(String errorMessage) {
        recentErrors.put(errorMessage, System.currentTimeMillis());
        
        // 清理过期的错误记录
        cleanupExpiredErrors();
    }
    
    /**
     * 清理过期的错误记录
     */
    private void cleanupExpiredErrors() {
        long now = System.currentTimeMillis();
        recentErrors.entrySet().removeIf(entry -> 
                (now - entry.getValue()) > ERROR_REPEAT_THRESHOLD);
    }
    
    /**
     * 根据错误类型生成友好提示
     */
    public String getFriendlyMessage(ErrorType type, String message) {
        switch (type) {
            case NETWORK:
                if (message.contains("timeout") || message.contains("超时")) {
                    return "网络请求超时，请检查网络连接";
                } else if (message.contains("connect") || message.contains("连接")) {
                    return "无法连接到服务器，请检查网络是否正常";
                } else {
                    return "网络错误，请检查网络连接后重试";
                }
                
            case PARSE:
                return "解析数据失败，数据格式可能已变更";
                
            case PLAYBACK:
                if (message.contains("format") || message.contains("格式")) {
                    return "视频格式不支持，请尝试其他片源";
                } else if (message.contains("404") || message.contains("找不到")) {
                    return "视频资源不存在或已失效";
                } else {
                    return "播放失败，请尝试其他片源";
                }
                
            case SOURCE:
                if (message.contains("empty") || message.contains("为空")) {
                    return "未获取到视频地址，请切换其他片源";
                } else {
                    return "片源异常，请更换其他片源";
                }
                
            case SYSTEM:
                return "系统错误，请尝试重启应用";
                
            case PRELOAD:
                return "预加载失败，请检查网络连接后重试";
                
            case UNKNOWN:
            default:
                return "发生未知错误，请重试";
        }
    }
    
    /**
     * 错误回调接口
     */
    public interface ErrorCallback {
        void onError(ErrorType type, String originalMessage, String friendlyMessage);
    }
    
    /**
     * 静态方法，显示错误消息（Toast方式）
     */
    public static void showErrorMessage(Context context, ErrorType type, String title, String message) {
        getInstance().showToast(type, title + ": " + message);
        // 记录日志
        getInstance().logError(type, title + ": " + message, null);
    }
} 