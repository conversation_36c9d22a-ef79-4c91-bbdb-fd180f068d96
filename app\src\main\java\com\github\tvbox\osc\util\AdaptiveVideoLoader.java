package com.github.tvbox.osc.util;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.github.tvbox.osc.base.App;
import com.github.tvbox.osc.cache.CacheManager;
import com.lzy.okgo.OkGo;
import com.lzy.okgo.model.HttpHeaders;
import com.orhanobut.hawk.Hawk;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.ConcurrentHashMap;
import java.util.HashMap;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 自适应视频加载器
 * 根据网络状况动态调整加载策略，优化视频传输效率
 */
public class AdaptiveVideoLoader {
    private static final String TAG = "AdaptiveVideoLoader";
    
    // 单例实例
    private static volatile AdaptiveVideoLoader instance;
    
    // 网络相关
    private NetworkUtils.NetworkType lastNetworkType = NetworkUtils.NetworkType.UNKNOWN;
    private int lastNetworkSpeed = 0; // kbps
    private boolean isNetworkStable = true;
    
    // 缓存和队列
    private final LinkedBlockingQueue<LoadTask> loadQueue = new LinkedBlockingQueue<>();
    private final ExecutorService executor;
    private final Handler mainHandler;
    
    // 分块大小设置 (动态调整)
    private int minChunkSize = 256 * 1024;     // 256KB
    private int maxChunkSize = 4 * 1024 * 1024; // 4MB
    private int currentChunkSize = 1024 * 1024; // 默认1MB
    
    // 并发任务数 (动态调整)
    private int minConcurrentTasks = 2;
    private int maxConcurrentTasks = 8;
    private int currentConcurrentTasks = 3;
    
    // 自适应比特率设置
    private int preferredMaxBitrate = 0; // 0表示自动选择
    
    // 预热区块 (优先加载的部分)
    private int preloadSectionSize = 3 * 1024 * 1024; // 默认3MB
    
    // 网络健康评分 (0-100，越高表示网络越好)
    private int networkHealthScore = 50;
    
    // 设备性能评分 (0-100，越高表示设备越好)
    private int devicePerformanceScore = 50;
    
    // 网络速度历史记录，用于计算稳定性
    private final List<Integer> speedHistory = new ArrayList<>();
    private static final int MAX_SPEED_HISTORY = 10;
    
    // 缓存索引 - 记录URL对应的已下载分片情况
    private final Map<String, Map<Long, Integer>> cacheIndex = new ConcurrentHashMap<>();
    
    // 设备硬件评估结果
    private boolean isHighEndDevice = false;
    
    // 统计和监控
    private final AtomicInteger activeTaskCount = new AtomicInteger(0);
    private final List<LoadListener> listeners = new ArrayList<>();
    
    /**
     * 私有构造函数
     */
    private AdaptiveVideoLoader() {
        executor = Executors.newCachedThreadPool();
        mainHandler = new Handler(Looper.getMainLooper());
        
        // 初始化网络监听
        initNetworkMonitoring();
        
        // 评估设备性能
        evaluateDevicePerformance();
        
        // 启动队列处理线程
        startQueueProcessor();
    }
    
    /**
     * 获取单例实例
     */
    public static AdaptiveVideoLoader getInstance() {
        if (instance == null) {
            synchronized (AdaptiveVideoLoader.class) {
                if (instance == null) {
                    instance = new AdaptiveVideoLoader();
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化网络监听
     */
    private void initNetworkMonitoring() {
        NetworkUtils.getInstance().addNetworkStateListener(
            (isAvailable, isConnected, networkType) -> {
                // 保存之前的网络类型
                NetworkUtils.NetworkType oldType = lastNetworkType;
                lastNetworkType = networkType;
                
                // 如果网络类型发生变化，调整加载策略
                if (oldType != networkType) {
                    adjustLoadingStrategy();
                }
                
                // 如果网络不可用，清理队列
                if (!isAvailable || !isConnected) {
                    clearQueue();
                }
            }
        );
    }
    
    /**
     * 开始队列处理
     */
    private void startQueueProcessor() {
        new Thread(() -> {
            while (true) {
                try {
                    // 获取待处理任务
                    LoadTask task = loadQueue.take();
                    
                    // 检查网络是否可用，不可用则暂停处理
                    if (!NetworkUtils.checkNetworkAvailable()) {
                        // 将任务放回队列前端，稍后重试
                        loadQueue.offer(task);
                        Thread.sleep(1000);
                        continue;
                    }
                    
                    // 检查当前活动任务数，如果已达上限则稍后重试
                    while (activeTaskCount.get() >= currentConcurrentTasks) {
                        Thread.sleep(100);
                    }
                    
                    // 开始加载任务
                    activeTaskCount.incrementAndGet();
                    executor.execute(() -> {
                        try {
                            processTask(task);
                        } finally {
                            activeTaskCount.decrementAndGet();
                        }
                    });
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    Log.e(TAG, "队列处理出错: " + e.getMessage());
                }
            }
        }).start();
    }
    
    /**
     * 处理加载任务
     */
    private void processTask(LoadTask task) {
        Log.d(TAG, "处理视频加载任务: " + task.url);
        
        switch (task.type) {
            case WHOLE_FILE:
                loadWholeFile(task);
                break;
            case CHUNKS:
                loadFileInChunks(task);
                break;
            case HLS:
                loadHlsContent(task);
                break;
            case DASH:
                loadDashContent(task);
                break;
        }
    }
    
    /**
     * 加载整个文件
     */
    private void loadWholeFile(LoadTask task) {
        try {
            OkHttpClient client = OkGoHelper.getDefaultClient();
            Request request = new Request.Builder().url(task.url).build();
            
            Response response = client.newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                // 获取内容
                byte[] data = response.body().bytes();
                
                // 回调成功
                notifySuccess(task.url, data);
            } else {
                notifyError(task.url, "HTTP错误: " + response.code());
            }
        } catch (Exception e) {
            notifyError(task.url, "加载出错: " + e.getMessage());
        }
    }
    
    /**
     * 分块加载文件
     */
    private void loadFileInChunks(LoadTask task) {
        try {
            OkHttpClient client = OkGoHelper.getDefaultClient();
            
            // 检查文件大小
            Request headRequest = new Request.Builder().url(task.url).head().build();
            Response headResponse = client.newCall(headRequest).execute();
            
            String contentLength = headResponse.header("Content-Length");
            if (contentLength == null) {
                // 无法获取文件大小，回退到整体下载
                loadWholeFile(task);
                return;
            }
            
            long fileSize = Long.parseLong(contentLength);
            int chunkCount = calculateChunkCount(fileSize);
            
            Log.d(TAG, "文件大小: " + fileSize + " 字节, 分块数: " + chunkCount);
            
            // 创建结果数组
            byte[][] chunks = new byte[chunkCount][];
            AtomicInteger completedChunks = new AtomicInteger(0);
            Object lock = new Object();
            
            // 预热区块 (如果有)
            if (task.prioritySections != null && !task.prioritySections.isEmpty()) {
                // 优先处理预热区块
                for (int[] section : task.prioritySections) {
                    downloadChunk(client, task, fileSize, chunks, completedChunks, 
                                 chunkCount, section[0], section[1], lock);
                }
            }
            
            // 计算分块大小
            int chunkSize = (int) Math.ceil((double) fileSize / chunkCount);
            
            // 创建并发下载任务
            for (int i = 0; i < chunkCount; i++) {
                final int index = i;
                final long startByte = i * chunkSize;
                final long endByte = Math.min((i + 1) * chunkSize - 1, fileSize - 1);
                
                // 检查这个块是否已经在预热区块中下载过了
                boolean alreadyDownloaded = false;
                if (task.prioritySections != null) {
                    for (int[] section : task.prioritySections) {
                        if (startByte >= section[0] && endByte <= section[1]) {
                            alreadyDownloaded = true;
                            break;
                        }
                    }
                }
                
                if (!alreadyDownloaded) {
                    downloadChunk(client, task, fileSize, chunks, completedChunks, 
                                 chunkCount, startByte, endByte, lock);
                }
            }
            
            // 等待所有分块完成
            synchronized (lock) {
                while (completedChunks.get() < chunkCount) {
                    try {
                        lock.wait(1000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            
            // 合并所有分块
            byte[] result = mergeChunks(chunks, fileSize);
            
            // 通知完成
            notifySuccess(task.url, result);
            
        } catch (Exception e) {
            notifyError(task.url, "分块加载出错: " + e.getMessage());
        }
    }
    
    /**
     * 下载单个分块
     */
    private void downloadChunk(OkHttpClient client, LoadTask task, long fileSize, 
                             byte[][] chunks, AtomicInteger completedChunks, 
                             int chunkCount, long startByte, long endByte, Object lock) {
        executor.execute(() -> {
            try {
                Log.d(TAG, "下载分块: " + startByte + "-" + endByte);
                
                Request request = new Request.Builder()
                    .url(task.url)
                    .header("Range", "bytes=" + startByte + "-" + endByte)
                    .build();
                
                Response response = client.newCall(request).execute();
                if (!response.isSuccessful() || response.body() == null) {
                    throw new IOException("请求失败: " + response.code());
                }
                
                byte[] data = response.body().bytes();
                int chunkIndex = (int)(startByte / Math.ceil((double) fileSize / chunkCount));
                chunks[chunkIndex] = data;
                
                int completed = completedChunks.incrementAndGet();
                int progress = (int)(completed * 100.0 / chunkCount);
                
                // 通知进度
                notifyProgress(task.url, progress);
                
                // 唤醒等待的线程
                synchronized (lock) {
                    lock.notifyAll();
                }
            } catch (Exception e) {
                Log.e(TAG, "分块下载出错: " + e.getMessage());
            }
        });
    }
    
    /**
     * 加载HLS内容
     * 使用改进的HLS加载逻辑
     */
    private void loadHlsContent(LoadTask task) {
        try {
            OkHttpClient client = OkGoHelper.getDefaultClient();
            Request request = new Request.Builder().url(task.url).build();
            
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful() || response.body() == null) {
                notifyError(task.url, "加载M3U8文件失败: " + response.code());
                return;
            }
            
            String m3u8Content = response.body().string();
            if (TextUtils.isEmpty(m3u8Content)) {
                notifyError(task.url, "M3U8内容为空");
                return;
            }
            
            // 解析m3u8文件并选择最佳品质
            List<String> tsUrls = parseM3u8AndSelectBestQuality(m3u8Content, task.url);
            if (tsUrls.isEmpty()) {
                notifyError(task.url, "无法解析M3U8内容中的TS文件");
                return;
            }
            
            // 确定预加载分片数量
            int segmentsToPreload = calculateSegmentsToPreload();
            segmentsToPreload = Math.min(segmentsToPreload, tsUrls.size());
            
            Log.d(TAG, "HLS预加载: 将加载 " + segmentsToPreload + "/" + tsUrls.size() + " 个TS分片");
            
            // 创建结果列表
            List<byte[]> preloadedSegments = new ArrayList<>();
            
            // 准备TS分片缓存索引
            String cacheKey = task.url;
            Map<Long, Integer> segmentIndex = new ConcurrentHashMap<>();
            cacheIndex.put(cacheKey, segmentIndex);
            
            // 创建进度跟踪
            final AtomicInteger completedSegments = new AtomicInteger(0);
            final int totalSegments = segmentsToPreload;
            
            // 并发预加载TS分片
            ExecutorService tsExecutor = Executors.newFixedThreadPool(Math.min(3, currentConcurrentTasks));
            List<Future<byte[]>> futures = new ArrayList<>();
            
            for (int i = 0; i < segmentsToPreload; i++) {
                final int index = i;
                final String tsUrl = tsUrls.get(i);
                
                // 创建下载任务
                futures.add(tsExecutor.submit(() -> {
                    try {
                        // 尝试从缓存加载TS片段
                        if (VideoCache.isEnabled() && VideoCache.isTsSegmentEnabled()) {
                            // 尝试从缓存中获取
                            final byte[][] cachedData = new byte[1][];
                            final boolean[] isComplete = new boolean[1];
                            
                            VideoCache.loadFromCache(tsUrl, new VideoCache.GetCallback() {
                                @Override
                                public void onSuccess(String url, byte[] data) {
                                    cachedData[0] = data;
                                    isComplete[0] = true;
                                }
                                
                                @Override
                                public void onPartialData(String url, String filePath, Map<Long, Integer> segments) {
                                    // 部分数据对TS分片不适用
                                }
                                
                                @Override
                                public void onRangeSuccess(String url, byte[] data, long startPos) {
                                    // 范围数据对TS分片不适用
                                }
                                
                                @Override
                                public void onRangeNotFound(String url, long startPos, int length) {
                                    // 未找到范围
                                }
                                
                                @Override
                                public void onNotFound(String url) {
                                    // 未找到缓存
                                }
                                
                                @Override
                                public void onError(String url, String error) {
                                    // 缓存错误
                                }
                            });
                            
                            // 如果缓存命中，直接返回
                            if (isComplete[0] && cachedData[0] != null) {
                                updateProgress(completedSegments.incrementAndGet(), totalSegments, task.url);
                                return cachedData[0];
                            }
                        }
                        
                        // 缓存未命中，从网络下载
                        byte[] data = downloadTsSegment(tsUrl);
                        if (data != null && data.length > 0) {
                            // 更新进度
                            updateProgress(completedSegments.incrementAndGet(), totalSegments, task.url);
                            
                            // 缓存TS片段
                            if (VideoCache.isEnabled() && VideoCache.isTsSegmentEnabled()) {
                                VideoCache.cacheVideo(tsUrl, data);
                            }
                            
                            return data;
                        } else {
                            Log.e(TAG, "TS分片下载失败: " + tsUrl);
                            // 返回空数组表示下载失败
                            return new byte[0];
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "TS片段下载出错: " + e.getMessage());
                        // 返回空数组表示下载失败
                        return new byte[0];
                    }
                }));
            }
            
            // 收集预加载结果
            for (int i = 0; i < futures.size(); i++) {
                try {
                    byte[] segmentData = futures.get(i).get(30, TimeUnit.SECONDS);
                    if (segmentData.length > 0) {
                        preloadedSegments.add(segmentData);
                        segmentIndex.put((long)i, segmentData.length);
                    } else {
                        // 下载失败的片段添加空值
                        preloadedSegments.add(null);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "等待TS片段超时: " + e.getMessage());
                    // 超时的片段添加空值
                    preloadedSegments.add(null);
                }
            }
            
            // 关闭线程池
            tsExecutor.shutdown();
            
            // 创建结果对象
            HlsPreloadResult result = new HlsPreloadResult(m3u8Content, tsUrls, preloadedSegments);
            
            // 如果是HLS监听器，回调特定方法
            notifyHlsSuccess(task.url, result);
        } catch (Exception e) {
            Log.e(TAG, "加载HLS内容出错: " + e.getMessage());
            notifyError(task.url, "加载HLS内容出错: " + e.getMessage());
        }
    }
    
    /**
     * 更新进度并通知监听器
     */
    private void updateProgress(int completed, int total, String url) {
        int progress = (int)(((float)completed / total) * 100);
        mainHandler.post(() -> notifyProgress(url, progress));
    }
    
    /**
     * 加载DASH内容
     */
    private void loadDashContent(LoadTask task) {
        // DASH格式处理，类似HLS但解析和处理方式不同
        // 此处添加DASH格式处理逻辑
        notifyError(task.url, "DASH格式支持尚未实现");
    }
    
    /**
     * 下载单个TS分片
     */
    private byte[] downloadTsSegment(String tsUrl) {
        try {
            OkHttpClient client = OkGoHelper.getDefaultClient();
            Request request = new Request.Builder().url(tsUrl).build();
            
            Response response = client.newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                return response.body().bytes();
            }
        } catch (Exception e) {
            Log.e(TAG, "TS分片下载出错: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 根据网络状况调整加载策略
     */
    private void adjustLoadingStrategy() {
        // 基于网络类型的基础调整
        switch (lastNetworkType) {
            case WIFI:
                // WiFi下可以激进一些
                currentConcurrentTasks = isHighEndDevice ? 6 : 4;
                break;
            case MOBILE:
                // 移动网络保守一些
                currentConcurrentTasks = isHighEndDevice ? 4 : 3;
                break;
            case ETHERNET:
                // 有线网络通常很稳定
                currentConcurrentTasks = isHighEndDevice ? 8 : 5;
                break;
            default:
                // 未知网络类型，使用保守设置
                currentConcurrentTasks = 3;
                break;
        }
        
        // 根据网络健康评分进一步调整并发任务数
        if (networkHealthScore > 80) {
            // 网络非常好，可以增加并发
            currentConcurrentTasks += 2;
        } else if (networkHealthScore < 30) {
            // 网络不好，减少并发
            currentConcurrentTasks = Math.max(minConcurrentTasks, currentConcurrentTasks - 1);
        }
        
        // 确保并发任务数在范围内
        currentConcurrentTasks = Math.max(minConcurrentTasks, 
                                     Math.min(maxConcurrentTasks, currentConcurrentTasks));
        
        // 调整分块大小
        if (networkHealthScore > 80 && isNetworkStable) {
            // 网络非常好且稳定，使用较大块
            currentChunkSize = isHighEndDevice ? 3 * 1024 * 1024 : 2 * 1024 * 1024;
        } else if (networkHealthScore > 50) {
            // 网络良好，使用中等块
            currentChunkSize = isHighEndDevice ? 2 * 1024 * 1024 : 1 * 1024 * 1024;
        } else if (networkHealthScore > 30) {
            // 网络一般，使用较小块
            currentChunkSize = 512 * 1024;
        } else {
            // 网络不好，使用最小块
            currentChunkSize = minChunkSize;
        }
        
        // 确保分块大小在范围内
        currentChunkSize = Math.max(minChunkSize, Math.min(maxChunkSize, currentChunkSize));
        
        // 根据网络状况调整预载区块大小
        if (networkHealthScore > 70) {
            preloadSectionSize = 5 * 1024 * 1024; // 5MB
        } else if (networkHealthScore > 40) {
            preloadSectionSize = 3 * 1024 * 1024; // 3MB
        } else {
            preloadSectionSize = 1 * 1024 * 1024; // 1MB
        }
        
        Log.d(TAG, "调整加载策略 - 网络: " + lastNetworkType + 
              ", 健康分: " + networkHealthScore + 
              ", 并发数: " + currentConcurrentTasks + 
              ", 块大小: " + (currentChunkSize / 1024) + "KB");
    }
    
    /**
     * 根据文件大小计算适合的分块数量
     */
    private int calculateChunkCount(long fileSize) {
        // 根据文件大小调整分块数量
        if (fileSize < 5 * 1024 * 1024) { // 5MB以下
            return Math.min(3, currentConcurrentTasks);
        } else if (fileSize < 20 * 1024 * 1024) { // 5-20MB
            return Math.min(5, currentConcurrentTasks);
        } else if (fileSize < 50 * 1024 * 1024) { // 20-50MB
            return Math.min(8, currentConcurrentTasks);
        } else if (fileSize < 100 * 1024 * 1024) { // 50-100MB
            return Math.min(10, currentConcurrentTasks);
        } else { // 100MB以上
            return currentConcurrentTasks;
        }
    }
    
    /**
     * 计算需要预加载的TS分片数量
     */
    private int calculateSegmentsToPreload() {
        NetworkUtils.NetworkType networkType = lastNetworkType;
        
        switch (networkType) {
            case WIFI:
            case ETHERNET:
                return 5; // WiFi或有线网络预加载5个分片
            case MOBILE:
                String networkTypeName = NetworkUtils.getNetworkTypeName();
                if (networkTypeName.contains("5G") || networkTypeName.contains("4G") || 
                    networkTypeName.contains("LTE")) {
                    return 3; // 4G/5G预加载3个分片
                } else {
                    return 2; // 其他移动网络预加载2个分片
                }
            default:
                return 1; // 最保守只预加载1个分片
        }
    }
    
    /**
     * 解析m3u8内容，选择最佳画质
     */
    private List<String> parseM3u8AndSelectBestQuality(String m3u8Content, String m3u8Url) {
        List<String> tsUrls = new ArrayList<>();
        
        // 检查是否是master playlist
        if (m3u8Content.contains("#EXT-X-STREAM-INF")) {
            return handleMasterPlaylist(m3u8Content, m3u8Url);
        }
        
        // 解析常规m3u8
        String baseUrl = m3u8Url.substring(0, m3u8Url.lastIndexOf("/") + 1);
        String[] lines = m3u8Content.split("\n");
        
        for (String line : lines) {
            line = line.trim();
            if (!line.startsWith("#") && !line.isEmpty()) {
                // 这是TS文件的URL
                String tsUrl = line;
                if (!tsUrl.startsWith("http")) {
                    // 相对路径，转换为绝对路径
                    if (tsUrl.startsWith("/")) {
                        // 从域名开始的绝对路径
                        String domain = m3u8Url.substring(0, m3u8Url.indexOf("/", 8));
                        tsUrl = domain + tsUrl;
                    } else {
                        // 相对当前目录的路径
                        tsUrl = baseUrl + tsUrl;
                    }
                }
                tsUrls.add(tsUrl);
            }
        }
        
        return tsUrls;
    }
    
    /**
     * 处理HLS主播放列表
     */
    private List<String> handleMasterPlaylist(String content, String masterUrl) {
        // 解析主播放列表，查找可用的子播放列表
        List<PlaylistInfo> playlists = new ArrayList<>();
        String[] lines = content.split("\n");
        String baseUrl = masterUrl.substring(0, masterUrl.lastIndexOf("/") + 1);
        
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            if (line.startsWith("#EXT-X-STREAM-INF")) {
                // 解析带宽和分辨率
                int bandwidth = 0;
                String resolution = "";
                
                if (line.contains("BANDWIDTH=")) {
                    String bw = line.substring(line.indexOf("BANDWIDTH=") + 10);
                    if (bw.contains(",")) {
                        bw = bw.substring(0, bw.indexOf(","));
                    }
                    try {
                        bandwidth = Integer.parseInt(bw);
                    } catch (NumberFormatException e) {
                        // 忽略解析错误
                    }
                }
                
                if (line.contains("RESOLUTION=")) {
                    int resStart = line.indexOf("RESOLUTION=") + 11;
                    int resEnd = line.indexOf(",", resStart);
                    if (resEnd == -1) resEnd = line.length();
                    resolution = line.substring(resStart, resEnd);
                }
                
                // 获取URL
                if (i + 1 < lines.length) {
                    String urlLine = lines[i + 1].trim();
                    if (!urlLine.startsWith("#")) {
                        String subUrl = urlLine;
                        // 如果是相对路径，转换为绝对路径
                        if (!subUrl.startsWith("http")) {
                            if (subUrl.startsWith("/")) {
                                // 绝对路径从域名开始
                                String domain = masterUrl.substring(0, masterUrl.indexOf("/", 8));
                                subUrl = domain + subUrl;
                            } else {
                                // 相对路径从当前目录开始
                                subUrl = baseUrl + subUrl;
                            }
                        }
                        
                        PlaylistInfo info = new PlaylistInfo(subUrl, bandwidth, resolution);
                        playlists.add(info);
                    }
                }
            }
        }
        
        // 根据偏好选择最佳质量
        PlaylistInfo bestPlaylist = selectBestPlaylist(playlists);
        
        if (bestPlaylist != null) {
            Log.d(TAG, "选择播放列表: " + bestPlaylist.url + 
                  ", 带宽: " + bestPlaylist.bandwidth + 
                  ", 分辨率: " + bestPlaylist.resolution);
            
            // 递归加载选中的播放列表
            try {
                OkHttpClient client = OkGoHelper.getDefaultClient();
                Request request = new Request.Builder().url(bestPlaylist.url).build();
                
                Response response = client.newCall(request).execute();
                if (response.isSuccessful() && response.body() != null) {
                    String subContent = response.body().string();
                    return parseM3u8AndSelectBestQuality(subContent, bestPlaylist.url);
                }
            } catch (Exception e) {
                Log.e(TAG, "加载子播放列表失败: " + e.getMessage());
            }
        }
        
        return new ArrayList<>();
    }
    
    /**
     * 根据当前网络状况选择最佳播放列表
     */
    private PlaylistInfo selectBestPlaylist(List<PlaylistInfo> playlists) {
        if (playlists.isEmpty()) {
            return null;
        }
        
        // 按带宽排序
        playlists.sort((a, b) -> Integer.compare(a.bandwidth, b.bandwidth));
        
        // 如果没有带宽限制，选择最高质量
        if (preferredMaxBitrate <= 0) {
            return playlists.get(playlists.size() - 1);
        }
        
        // 否则选择不超过首选带宽的最高质量
        PlaylistInfo best = playlists.get(0);
        for (PlaylistInfo info : playlists) {
            if (info.bandwidth <= preferredMaxBitrate && info.bandwidth > best.bandwidth) {
                best = info;
            }
        }
        
        return best;
    }
    
    /**
     * 合并分块数据
     */
    private byte[] mergeChunks(byte[][] chunks, long totalSize) {
        byte[] result = new byte[(int)totalSize];
        int offset = 0;
        
        for (byte[] chunk : chunks) {
            if (chunk != null) {
                System.arraycopy(chunk, 0, result, offset, chunk.length);
                offset += chunk.length;
            }
        }
        
        return result;
    }
    
    /**
     * 清空加载队列
     */
    private void clearQueue() {
        loadQueue.clear();
    }
    
    /**
     * 通知加载进度
     */
    private void notifyProgress(String url, int progress) {
        mainHandler.post(() -> {
            for (LoadListener listener : listeners) {
                listener.onProgress(url, progress);
            }
        });
    }
    
    /**
     * 通知加载成功
     */
    private void notifySuccess(String url, byte[] data) {
        mainHandler.post(() -> {
            for (LoadListener listener : listeners) {
                listener.onSuccess(url, data);
            }
        });
    }
    
    /**
     * 通知HLS加载成功
     */
    private void notifyHlsSuccess(String url, HlsPreloadResult result) {
        mainHandler.post(() -> {
            for (LoadListener listener : listeners) {
                if (listener instanceof HlsLoadListener) {
                    ((HlsLoadListener)listener).onHlsSuccess(url, result);
                } else {
                    // 默认转换为普通成功回调
                    listener.onSuccess(url, result.m3u8Content.getBytes());
                }
            }
        });
    }
    
    /**
     * 通知加载失败
     */
    private void notifyError(String url, String error) {
        mainHandler.post(() -> {
            for (LoadListener listener : listeners) {
                listener.onError(url, error);
            }
        });
    }
    
    /**
     * 添加加载监听器
     */
    public void addListener(LoadListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
        }
    }
    
    /**
     * 移除加载监听器
     */
    public void removeListener(LoadListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * 加载整个视频文件
     */
    public void loadVideo(String url, LoadListener listener) {
        if (listener != null) {
            addListener(listener);
        }
        
        LoadTask task = new LoadTask();
        task.type = TaskType.WHOLE_FILE;
        task.url = url;
        
        loadQueue.offer(task);
    }
    
    /**
     * 智能分块加载视频文件
     */
    public void loadVideoWithChunks(String url, LoadListener listener) {
        if (listener != null) {
            addListener(listener);
        }
        
        LoadTask task = new LoadTask();
        task.type = TaskType.CHUNKS;
        task.url = url;
        
        loadQueue.offer(task);
    }
    
    /**
     * 智能分块加载视频文件，带预热区块
     * @param url 视频URL
     * @param listener 加载监听器
     * @param prioritySections 优先加载的区块，格式为[开始字节,结束字节]
     */
    public void loadVideoWithChunks(String url, LoadListener listener, List<int[]> prioritySections) {
        if (listener != null) {
            addListener(listener);
        }
        
        LoadTask task = new LoadTask();
        task.type = TaskType.CHUNKS;
        task.url = url;
        task.prioritySections = prioritySections;
        
        loadQueue.offer(task);
    }
    
    /**
     * 加载HLS视频
     */
    public void loadHlsVideo(String url, HlsLoadListener listener) {
        if (listener != null) {
            addListener(listener);
        }
        
        LoadTask task = new LoadTask();
        task.type = TaskType.HLS;
        task.url = url;
        
        loadQueue.offer(task);
    }
    
    /**
     * 取消特定URL的加载任务
     * @param url 需要取消的URL
     */
    public void cancelLoad(String url) {
        if (url == null || url.isEmpty()) {
            return;
        }
        
        try {
            // 从队列中移除任务
            synchronized (loadQueue) {
                Iterator<LoadTask> iterator = loadQueue.iterator();
                while (iterator.hasNext()) {
                    LoadTask queuedTask = iterator.next();
                    if (queuedTask.url.equals(url)) {
                        iterator.remove();
                        break;
                    }
                }
            }
            
            // 取消网络请求
            OkGo.getInstance().cancelTag(url);
            
            Log.i(TAG, "已取消加载任务: " + url);
        } catch (Exception e) {
            Log.i(TAG, "取消加载任务出错: " + e.getMessage());
        }
    }
    
    /**
     * 取消所有加载任务并清空队列
     */
    public void cancelAllLoads() {
        try {
            // 清空任务队列
            synchronized (loadQueue) {
                loadQueue.clear();
            }
            
            // 取消所有由加载器发起的网络请求
            OkGo.getInstance().cancelTag(this);
            
            Log.i(TAG, "已取消所有加载任务");
        } catch (Exception e) {
            Log.i(TAG, "取消所有加载任务出错: " + e.getMessage());
        }
    }
    
    /**
     * 设置最大并发任务数
     */
    public void setMaxConcurrentTasks(int max) {
        this.maxConcurrentTasks = Math.max(2, max);
        // 重新调整当前并发任务数
        adjustLoadingStrategy();
    }
    
    /**
     * 设置最大分块大小 (字节)
     */
    public void setMaxChunkSize(int bytes) {
        this.maxChunkSize = bytes;
        // 重新调整当前分块大小
        adjustLoadingStrategy();
    }
    
    /**
     * 设置首选最大比特率 (bps)
     */
    public void setPreferredMaxBitrate(int bps) {
        this.preferredMaxBitrate = bps;
    }
    
    /**
     * 获取当前网络类型
     */
    public NetworkUtils.NetworkType getCurrentNetworkType() {
        return lastNetworkType;
    }
    
    /**
     * 获取当前分块大小
     */
    public int getCurrentChunkSize() {
        return currentChunkSize;
    }
    
    /**
     * 获取当前并发任务数
     */
    public int getCurrentConcurrentTasks() {
        return currentConcurrentTasks;
    }
    
    /**
     * 播放列表信息类
     */
    private static class PlaylistInfo {
        String url;
        int bandwidth;
        String resolution;
        
        PlaylistInfo(String url, int bandwidth, String resolution) {
            this.url = url;
            this.bandwidth = bandwidth;
            this.resolution = resolution;
        }
    }
    
    /**
     * 加载任务类型枚举
     */
    private enum TaskType {
        WHOLE_FILE,  // 整个文件加载
        CHUNKS,      // 分块加载
        HLS,         // HLS格式加载
        DASH         // DASH格式加载
    }
    
    /**
     * 加载任务类
     */
    private static class LoadTask {
        TaskType type;
        String url;
        List<int[]> prioritySections; // 优先加载的区块，格式为[开始字节,结束字节]
        HashMap<String, String> headers; // HTTP请求头
    }
    
    /**
     * HLS预加载结果类
     */
    public static class HlsPreloadResult {
        public String m3u8Content;            // m3u8文件内容
        public List<String> tsUrls;           // TS分片URL列表
        public List<byte[]> preloadedSegments; // 预加载的TS分片数据
        
        public HlsPreloadResult(String m3u8Content, List<String> tsUrls, List<byte[]> preloadedSegments) {
            this.m3u8Content = m3u8Content;
            this.tsUrls = tsUrls;
            this.preloadedSegments = preloadedSegments;
        }
    }
    
    /**
     * 加载监听器接口
     */
    public interface LoadListener {
        void onProgress(String url, int progress);
        void onSuccess(String url, byte[] data);
        void onError(String url, String error);
    }
    
    /**
     * HLS加载监听器接口
     */
    public interface HlsLoadListener extends LoadListener {
        void onHlsSuccess(String url, HlsPreloadResult result);
    }
    
    /**
     * 评估设备性能
     * 基于CPU核心数、内存大小等因素
     */
    private void evaluateDevicePerformance() {
        try {
            // 获取CPU核心数
            int cpuCores = Runtime.getRuntime().availableProcessors();
            
            // 获取可用内存
            ActivityManager activityManager = (ActivityManager) App.getInstance()
                .getSystemService(Context.ACTIVITY_SERVICE);
            ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
            activityManager.getMemoryInfo(memoryInfo);
            long availMemory = memoryInfo.availMem / (1024 * 1024); // MB
            
            // 计算设备性能得分
            devicePerformanceScore = 0;
            
            // CPU评分 (每核心10分，最高50分)
            devicePerformanceScore += Math.min(50, cpuCores * 10);
            
            // 内存评分 (每512MB内存10分，最高50分)
            devicePerformanceScore += Math.min(50, (int)(availMemory / 512) * 10);
            
            // 确定设备等级
            isHighEndDevice = devicePerformanceScore >= 70;
            
            // 根据设备性能调整初始参数
            if (isHighEndDevice) {
                // 高端设备可以处理更多并发和更大块
                currentConcurrentTasks = Math.min(maxConcurrentTasks, 6);
                currentChunkSize = Math.min(maxChunkSize, 2 * 1024 * 1024);
            } else {
                // 低端设备减少并发和块大小
                currentConcurrentTasks = Math.max(minConcurrentTasks, 3);
                currentChunkSize = Math.max(minChunkSize, 512 * 1024);
            }
            
            Log.d(TAG, "设备性能评分: " + devicePerformanceScore + 
                  ", 并发数: " + currentConcurrentTasks + 
                  ", 块大小: " + (currentChunkSize / 1024) + "KB");
        } catch (Exception e) {
            Log.e(TAG, "评估设备性能出错: " + e.getMessage());
            // 使用默认值
        }
    }
    
    /**
     * 获取网络速度测量
     */
    private void measureNetworkSpeed() {
        try {
            // 简单测试：下载一个小文件并测量速度
            long startTime = System.currentTimeMillis();
            OkHttpClient client = OkGoHelper.getDefaultClient();
            Request request = new Request.Builder()
                .url("https://www.google.com/favicon.ico") // 使用一个小图标测速
                .build();
            
            Response response = client.newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                byte[] data = response.body().bytes();
                long endTime = System.currentTimeMillis();
                long timeTaken = endTime - startTime;
                
                // 计算速度 (Kbps)
                if (timeTaken > 0) {
                    int speed = (int)((data.length * 8) / timeTaken); // Kbps
                    updateSpeedHistory(speed);
                    Log.d(TAG, "网络速度: " + speed + " Kbps");
                }
            }
        } catch (Exception e) {
            // 测速失败，可能是网络问题
            Log.e(TAG, "网络速度测量失败: " + e.getMessage());
            // 降低网络健康评分
            networkHealthScore = Math.max(10, networkHealthScore - 10);
        }
    }
    
    /**
     * 更新网络速度历史
     */
    private void updateSpeedHistory(int speed) {
        // 添加新速度记录
        speedHistory.add(speed);
        
        // 如果历史记录过多，移除最旧的
        while (speedHistory.size() > MAX_SPEED_HISTORY) {
            speedHistory.remove(0);
        }
        
        // 计算平均速度
        int totalSpeed = 0;
        for (int s : speedHistory) {
            totalSpeed += s;
        }
        lastNetworkSpeed = totalSpeed / speedHistory.size();
        
        // 计算速度波动性 (标准差/平均值)
        double mean = lastNetworkSpeed;
        double variance = 0;
        for (int s : speedHistory) {
            variance += Math.pow(s - mean, 2);
        }
        variance /= speedHistory.size();
        double stdDev = Math.sqrt(variance);
        double variability = stdDev / mean;
        
        // 根据波动性判断网络稳定性
        isNetworkStable = variability < 0.3; // 波动小于30%认为稳定
        
        // 更新网络健康评分
        if (lastNetworkSpeed > 5000) { // 5Mbps以上给高分
            networkHealthScore = 80 + (isNetworkStable ? 20 : 0);
        } else if (lastNetworkSpeed > 2000) { // 2Mbps-5Mbps给中高分
            networkHealthScore = 60 + (isNetworkStable ? 20 : 0);
        } else if (lastNetworkSpeed > 1000) { // 1Mbps-2Mbps给中分
            networkHealthScore = 40 + (isNetworkStable ? 20 : 0);
        } else if (lastNetworkSpeed > 500) { // 500Kbps-1Mbps给中低分
            networkHealthScore = 20 + (isNetworkStable ? 20 : 0);
        } else { // 低于500Kbps给低分
            networkHealthScore = 10 + (isNetworkStable ? 10 : 0);
        }
        
        // 根据网络状况调整加载策略
        adjustLoadingStrategy();
    }
    
    /**
     * 获取网络健康评分 (0-100, 越高越好)
     */
    public int getNetworkHealthScore() {
        return networkHealthScore;
    }

    /**
     * 判断是否为高端设备
     */
    public boolean isHighEndDevice() {
        return isHighEndDevice;
    }

    /**
     * 使用HTTP头部信息加载视频 
     */
    public void loadVideo(String url, HashMap<String, String> headers, LoadListener listener) {
        if (TextUtils.isEmpty(url) || listener == null) {
            return;
        }
        
        // 添加监听器
        addListener(listener);
        
        // 创建任务
        LoadTask task = new LoadTask();
        task.type = TaskType.WHOLE_FILE;
        task.url = url;
        task.headers = headers;
        
        // 加入队列
        loadQueue.offer(task);
    }

    /**
     * 使用HTTP头部信息分块加载视频
     */
    public void loadVideoWithChunks(String url, HashMap<String, String> headers, LoadListener listener) {
        if (TextUtils.isEmpty(url) || listener == null) {
            return;
        }
        
        // 添加监听器
        addListener(listener);
        
        // 创建任务
        LoadTask task = new LoadTask();
        task.type = TaskType.CHUNKS;
        task.url = url;
        task.headers = headers;
        
        // 加入队列
        loadQueue.offer(task);
    }
    
    /**
     * 使用HTTP头部信息和优先加载区域分块加载视频
     */
    public void loadVideoWithPrioritySections(String url, HashMap<String, String> headers, LoadListener listener, 
                                          List<ParallelVideoDownloader.PrioritySection> prioritySections) {
        if (TextUtils.isEmpty(url) || listener == null) {
            return;
        }
        
        // 添加监听器
        addListener(listener);
        
        // 将PrioritySection转换为int[]格式
        List<int[]> sections = new ArrayList<>();
        if (prioritySections != null && !prioritySections.isEmpty()) {
            for (ParallelVideoDownloader.PrioritySection section : prioritySections) {
                sections.add(new int[]{(int)section.startPos, (int)(section.startPos + section.length - 1)});
            }
        }
        
        // 创建任务
        LoadTask task = new LoadTask();
        task.type = TaskType.CHUNKS;
        task.url = url;
        task.headers = headers;
        task.prioritySections = sections;
        
        // 加入队列
        loadQueue.offer(task);
    }
    
    /**
     * 使用HTTP头部信息加载HLS视频
     */
    public void loadHlsVideo(String url, HashMap<String, String> headers, HlsLoadListener listener) {
        if (TextUtils.isEmpty(url) || listener == null) {
            return;
        }
        
        // 添加监听器
        addListener(listener);
        
        // 创建任务
        LoadTask task = new LoadTask();
        task.type = TaskType.HLS;
        task.url = url;
        task.headers = headers;
        
        // 加入队列
        loadQueue.offer(task);
    }
} 