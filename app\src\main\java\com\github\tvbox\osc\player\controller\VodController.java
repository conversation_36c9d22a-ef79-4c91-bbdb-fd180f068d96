package com.github.tvbox.osc.player.controller;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;

import com.bumptech.glide.Glide;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.github.tvbox.osc.R;
import com.github.tvbox.osc.api.ApiConfig;
import com.github.tvbox.osc.bean.IJKCode;
import com.github.tvbox.osc.bean.ParseBean;
import com.github.tvbox.osc.beanry.InitBean;
import com.github.tvbox.osc.subtitle.widget.SimpleSubtitleView;
import com.github.tvbox.osc.ui.adapter.ParseAdapter;
import com.github.tvbox.osc.ui.adapter.SelectDialogAdapter;
import com.github.tvbox.osc.ui.dialog.SelectDialog;
import com.github.tvbox.osc.util.FastClickCheckUtil;
import com.github.tvbox.osc.util.HawkConfig;
import com.github.tvbox.osc.util.MD5;
import com.github.tvbox.osc.util.MMkvUtils;
import com.github.tvbox.osc.util.PlayerHelper;
import com.github.tvbox.osc.util.ScreenUtils;
import com.github.tvbox.osc.util.SubtitleHelper;
import com.github.tvbox.osc.util.ToolUtils;
import com.orhanobut.hawk.Hawk;
import com.owen.tvrecyclerview.widget.TvRecyclerView;
import com.owen.tvrecyclerview.widget.V7LinearLayoutManager;

import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import java.util.Date;
import java.util.Locale;

import xyz.doikki.videoplayer.player.VideoView;
import xyz.doikki.videoplayer.util.PlayerUtils;

import static xyz.doikki.videoplayer.util.PlayerUtils.stringForTime;

import com.github.tvbox.osc.cache.CacheManager;
import com.github.tvbox.osc.util.LOG;
import com.github.tvbox.osc.util.PerformanceMonitor;

public class VodController extends BaseController {
    public VodController(@NonNull @NotNull Context context) {
        super(context);
        mHandlerCallback = new HandlerCallback() {
            @Override
            public void callback(Message msg) {
                switch (msg.what) {
                    case 1000: { // seek 刷新
                        mProgressRoot.setVisibility(VISIBLE);
                        break;
                    }
                    case 1001: { // seek 关闭
                        mProgressRoot.setVisibility(GONE);
                        break;
                    }
                    case 1002: { // 显示底部菜单
                        mBottomRoot.setVisibility(VISIBLE);
                        mTopRoot1.setVisibility(VISIBLE);
                        mTopRoot2.setVisibility(VISIBLE);
                        mPlayTitle.setVisibility(GONE);
                        mBottomRoot.requestFocus();
                        backBtn.setVisibility(ScreenUtils.isTv(context) ? INVISIBLE : VISIBLE);
                        break;
                    }
                    case 1003: { // 隐藏底部菜单
                        mBottomRoot.setVisibility(GONE);
                        mTopRoot1.setVisibility(GONE);
                        mTopRoot2.setVisibility(GONE);
                        backBtn.setVisibility(INVISIBLE);
                        break;
                    }
                    case 1004: { // 设置速度
                        if (isInPlaybackState()) {
                            try {
                                float speed = (float) mPlayerConfig.getDouble("sp");
                                mControlWrapper.setSpeed(speed);
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                        } else
                            mHandler.sendEmptyMessageDelayed(1004, 100);
                        break;
                    }
                }
            }
        };
    }

    SeekBar mSeekBar;
    TextView mCurrentTime;
    TextView mTotalTime;
    boolean mIsDragging;
    LinearLayout mProgressRoot;
    TextView mProgressText;
    ImageView mProgressIcon;
    LinearLayout mBottomRoot;
    LinearLayout mTopRoot1;
    LinearLayout mTopRoot2;
    LinearLayout mParseRoot;
    TvRecyclerView mGridView;
    TextView mPlayTitle;
    TextView mPlayTitle1;
    TextView mPlayLoadNetSpeedRightTop;
    LinearLayout mNextBtn;//下一集
    LinearLayout mPreBtn;//上一集
    LinearLayout mPlayerScaleBtn;//屏幕大小
    ImageView mPlayerScaleImg;//屏幕大小
    TextView mPlayerScaleTxt;//屏幕大小
    LinearLayout mPlayerSpeedBtn;//快进
    ImageView mFFwdImg;//快进
    TextView mFFwdTxt;//快进
    LinearLayout mPlayerBtn;//播放器
    TextView mPlayerTxt;
    ImageView mPlayerImg;

    TextView mPlayerIJKBtn;
    LinearLayout mPlayerRetry;//重播
    LinearLayout backBtn;//返回
    private boolean isClickBackBtn;

    //TextView mPlayrefresh;//刷新
    TextView mPlayerTimeStartBtn;
    TextView mPlayerTimeSkipBtn;
    // TextView mPlayerTimeStepBtn;
    TextView mPlayPauseTime;
    TextView mPlayLoadNetSpeed;
    TextView mVideoSize;
    private TextView mCacheStatus; // 新增：缓存状态显示
    public SimpleSubtitleView mSubtitleView;

    LinearLayout mZimuBtn;//字幕
    LinearLayout mAudioTrackBtn;//音轨
    //影片结束时间
    TextView mTimeEnd;

    //暂停
    LinearLayout mPauseBtn;
    ImageView mPauseImg;

    Handler myHandle;
    Runnable myRunnable;
    LinearLayout mSpeedHidell;

    LinearLayout mSpeedll;
    ProgressBar mDialogVideoProgressBar;
    ProgressBar mDialogVideoPauseBar;
    private ImageView jiexibg;
    private InitBean initBean;
    private ImageView iv_AboutActivity_logo;


    int myHandleSeconds = 6000;//闲置多少毫秒秒关闭底栏  默认6秒
    private boolean isPaused = false;
    private boolean isKeyUp = false;

    private Runnable myRunnable2 = new Runnable() {
        @Override
        public void run() {
            Date date = new Date();
            SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
            mPlayPauseTime.setText(timeFormat.format(date));
            String speed = PlayerHelper.getDisplaySpeed(mControlWrapper.getTcpSpeed());
            mPlayLoadNetSpeedRightTop.setText(speed);
            mPlayLoadNetSpeed.setText(speed);
            String width = Integer.toString(mControlWrapper.getVideoSize()[0]);
            String height = Integer.toString(mControlWrapper.getVideoSize()[1]);
            mVideoSize.setText("[ " + width + " X " + height +" ]");
            
            // 更新缓存状态信息
            updateCacheStatus();

            mHandler.postDelayed(this, 1000);
        }
    };



    private long mPosition = 0L;

    private android.os.Handler cacheUpdateHandler = new android.os.Handler();
    private Runnable cacheUpdateRunnable = new Runnable() {
        @Override
        public void run() {
            updateCacheStatus();
            cacheUpdateHandler.postDelayed(this, 1000); // 每秒更新一次
        }
    };
    
    // 开始缓存状态更新计时器
    private void startCacheTimer() {
        // 先停止之前的计时器，避免重复
        stopCacheTimer();
        // 启动新计时器
        cacheUpdateHandler.postDelayed(cacheUpdateRunnable, 1000);
        android.util.Log.d("VodController", "已启动缓存状态更新计时器");
    }
    
    // 停止缓存状态更新计时器
    private void stopCacheTimer() {
        cacheUpdateHandler.removeCallbacks(cacheUpdateRunnable);
        android.util.Log.d("VodController", "已停止缓存状态更新计时器");
    }

    @Override
    protected void initView() {
        super.initView();
        mCurrentTime = findViewById(R.id.curr_time);
        mTotalTime = findViewById(R.id.total_time);
        mPlayTitle = findViewById(R.id.tv_info_name);
        mPlayTitle1 = findViewById(R.id.tv_info_name1);
        mPlayLoadNetSpeedRightTop = findViewById(R.id.tv_play_load_net_speed_right_top);
        mSeekBar = findViewById(R.id.seekBar);
        mProgressRoot = findViewById(R.id.tv_progress_container);
        mProgressIcon = findViewById(R.id.tv_progress_icon);
        mProgressText = findViewById(R.id.tv_progress_text);
        mDialogVideoProgressBar = findViewWithTag("progressbar_video");
        mDialogVideoPauseBar = findViewWithTag("pausebar_video");


        mBottomRoot = findViewById(R.id.bottom_container);
        mTopRoot1 = findViewById(R.id.tv_top_l_container);
        mTopRoot2 = findViewById(R.id.tv_top_r_container);
        mParseRoot = findViewById(R.id.parse_root);
        mGridView = findViewById(R.id.mGridView);
        mPlayerRetry = findViewById(R.id.play_retry);
        backBtn = findViewById(R.id.tv_back);//返回
        //  mPlayrefresh = findViewById(R.id.play_refresh);//刷新
        mNextBtn = findViewById(R.id.play_next);
        mPreBtn = findViewById(R.id.play_pre);
        mPlayerScaleBtn = findViewById(R.id.play_scale);
        mPlayerSpeedBtn = findViewById(R.id.play_speed);//快进
        mFFwdImg = findViewById(R.id.play_speed_img);//快进
        mFFwdTxt = findViewById(R.id.play_speed_txt);//快进

        mPlayerBtn = findViewById(R.id.play_player);
        mPlayerImg = findViewById(R.id.play_player_img);
        mPlayerTxt = findViewById(R.id.play_player_txt);

        mPlayerIJKBtn = findViewById(R.id.play_ijk);
        mPlayerTimeStartBtn = findViewById(R.id.play_time_start);
        mPlayerTimeSkipBtn = findViewById(R.id.play_time_end);
        //  mPlayerTimeStepBtn = findViewById(R.id.play_time_step);
        mPlayPauseTime = findViewById(R.id.tv_sys_time);
        mPlayLoadNetSpeed = findViewById(R.id.tv_play_load_net_speed);
        mVideoSize = findViewById(R.id.tv_videosize);
        mSubtitleView = findViewById(R.id.subtitle_view);
        mZimuBtn = findViewById(R.id.zimu_select);
        mAudioTrackBtn = findViewById(R.id.play_audio);//音轨
        
        // 尝试找到缓存状态TextView
        mCacheStatus = findViewById(R.id.tv_cache_status);
        
        // 如果找不到，就直接创建一个新的
        if (mCacheStatus == null) {
            android.util.Log.d("VodController", "创建新的缓存状态显示");
            mCacheStatus = new TextView(getContext());
            mCacheStatus.setId(R.id.tv_cache_status);
            mCacheStatus.setLayoutParams(new FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.WRAP_CONTENT,
                    FrameLayout.LayoutParams.WRAP_CONTENT));
            
            // 设置在右下角显示
            FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) mCacheStatus.getLayoutParams();
            params.gravity = android.view.Gravity.BOTTOM | android.view.Gravity.RIGHT;
            params.bottomMargin = 120; // 对应XML中的marginBottom
            params.rightMargin = 20;   // 对应XML中的marginEnd
            
            // 设置样式
            mCacheStatus.setPadding(5, 5, 5, 5);
            mCacheStatus.setBackgroundColor(0x80000000); // 半透明黑色背景
            mCacheStatus.setTextColor(0xFFFFFFFF);       // 白色文字
            mCacheStatus.setTextSize(android.util.TypedValue.COMPLEX_UNIT_SP, 14);
            mCacheStatus.setText("正在加载...");
            mCacheStatus.setVisibility(VISIBLE);
            
            // 添加到根视图，这里直接添加到当前控制器就可以，不需要获取父视图
            try {
                // 尝试添加到本视图
                this.addView(mCacheStatus);
                android.util.Log.d("VodController", "成功添加新的缓存状态显示");
            } catch (Exception e) {
                android.util.Log.e("VodController", "添加缓存状态显示失败: " + e.getMessage());
                e.printStackTrace();
            }
        } else {
            android.util.Log.d("VodController", "找到了现有的缓存状态显示");
        }

        //暂停
        mPauseBtn = findViewById(R.id.play_pause);
        mPauseImg = findViewById(R.id.play_pauseImg);
        //影片结束时间
        mTimeEnd = findViewById(R.id.tv_time_end);


        //屏幕大小
        mPlayerScaleImg = findViewById(R.id.play_scale_img);
        mPlayerScaleTxt = findViewById(R.id.play_scale_txt);

        //缓存进度
        mSpeedHidell = findViewById(R.id.tv_speed_top_hide);
        mSpeedll = findViewById(R.id.tv_top_r_container);

        jiexibg = findViewById(R.id.jiexibg);//解析前背景



        //隐藏
        backBtn.setVisibility(INVISIBLE);//返回按钮
        mTopRoot1.setVisibility(INVISIBLE);//最上面的
        mBottomRoot.setVisibility(INVISIBLE);//下面




        //返回
        backBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (getContext() instanceof Activity) {
                    isClickBackBtn = true;
                    ((Activity) getContext()).onBackPressed();
                }
            }
        });




        //背景图
        initBean = MMkvUtils.loadInitBean("");
        if (initBean != null && initBean.msg != null && ToolUtils.getIsEmpty(initBean.msg.uiPaybackg)) {
            Glide.with(this)
                    .load(initBean.msg.uiPaybackg)
                    .error(R.drawable.jiexibg)
                    .override(1920, 1080) // 设置加载图片的大小
                    .into(jiexibg);
        }



        int subtitleTextSize = SubtitleHelper.getTextSize(mActivity);
        mSubtitleView.setTextSize(subtitleTextSize);

        myHandle=new Handler();
        myRunnable = new Runnable() {
            @Override
            public void run() {
                hideBottom();
            }
        };

        mPlayPauseTime.post(new Runnable() {
            @Override
            public void run() {
                mHandler.post(myRunnable2);
            }
        });

        mGridView.setLayoutManager(new V7LinearLayoutManager(getContext(), 0, false));
        ParseAdapter parseAdapter = new ParseAdapter();
        parseAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                ParseBean parseBean = parseAdapter.getItem(position);
                // 当前默认解析需要刷新
                int currentDefault = parseAdapter.getData().indexOf(ApiConfig.get().getDefaultParse());
                parseAdapter.notifyItemChanged(currentDefault);
                ApiConfig.get().setDefaultParse(parseBean);
                parseAdapter.notifyItemChanged(position);
                listener.changeParse(parseBean);
                hideBottom();
            }
        });
        mGridView.setAdapter(parseAdapter);
        parseAdapter.setNewData(ApiConfig.get().getParseBeanList());

        mParseRoot.setVisibility(VISIBLE);

        mSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (!fromUser) {
                    return;
                }

                long duration = mControlWrapper.getDuration();
                long newPosition = (duration * progress) / seekBar.getMax();
                mPosition = newPosition;
                if (mCurrentTime != null)
                    mCurrentTime.setText(stringForTime((int) newPosition));
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                mIsDragging = true;
                mControlWrapper.stopProgress();
                mControlWrapper.stopFadeOut();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                myHandle.removeCallbacks(myRunnable);
                myHandle.postDelayed(myRunnable, myHandleSeconds);
                long duration = mControlWrapper.getDuration();
                long newPosition = (duration * seekBar.getProgress()) / seekBar.getMax();
                mControlWrapper.seekTo((int) newPosition);
                mIsDragging = false;
                mControlWrapper.startProgress();
                mControlWrapper.startFadeOut();
            }
        });
        mPlayerRetry.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                listener.replay(true);
                hideBottom();
            }
        });
        // Button : 点击按钮暂停播放 --------------------------------------------
        mPauseBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                togglePlay();
                if (!isPaused) {
                    hideBottom();
                }
            }
        });
        /*
        mPlayrefresh.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                listener.replay(false);
                hideBottom();
            }
        });
        */
        mNextBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                listener.playNext(false);
                hideBottom();
            }
        });
        mPreBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                listener.playPre();
                hideBottom();
            }
        });
        mPlayerScaleBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                myHandle.removeCallbacks(myRunnable);
                myHandle.postDelayed(myRunnable, myHandleSeconds);
                try {
                    int scaleType = mPlayerConfig.getInt("sc");
                    scaleType++;
                    if (scaleType > 5)
                        scaleType = 0;
                    mPlayerConfig.put("sc", scaleType);
                    updatePlayerCfgView();
                    listener.updatePlayerCfg();
                    mControlWrapper.setScreenScaleType(scaleType);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
        mPlayerSpeedBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                myHandle.removeCallbacks(myRunnable);
                myHandle.postDelayed(myRunnable, myHandleSeconds);
                try {
                    float speed = (float) mPlayerConfig.getDouble("sp");
                    speed += 0.25f;
                    if (speed > 3)
                        speed = 0.5f;
                    mPlayerConfig.put("sp", speed);
                    updatePlayerCfgView();
                    listener.updatePlayerCfg();
                    mControlWrapper.setSpeed(speed);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
        // takagen99: Add long press to reset speed
        mPlayerSpeedBtn.setOnLongClickListener(new OnLongClickListener() {
            @Override
            public boolean onLongClick(View view) {
                try {
                    mPlayerConfig.put("sp", 1.0f);
                    updatePlayerCfgView();
                    listener.updatePlayerCfg();
                    mControlWrapper.setSpeed(1.0f);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                return true;
            }
        });
        mPlayerBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
//                myHandle.removeCallbacks(myRunnable);
//                myHandle.postDelayed(myRunnable, myHandleSeconds);
                FastClickCheckUtil.check(view);
                try {
                    int playerType = mPlayerConfig.getInt("pl");
                    int defaultPos = 0;
                    ArrayList<Integer> players = PlayerHelper.getExistPlayerTypes();
                    ArrayList<Integer> renders = new ArrayList<>();
                    for(int p = 0; p<players.size(); p++) {
                        renders.add(p);
                        if (players.get(p) == playerType) {
                            defaultPos = p;
                        }
                    }
                    SelectDialog<Integer> dialog = new SelectDialog<>(mActivity);
                    dialog.setTip("请选择播放器");
                    dialog.setAdapter(new SelectDialogAdapter.SelectDialogInterface<Integer>() {
                        @Override
                        public void click(Integer value, int pos) {
                            try {
                                dialog.cancel();
                                int thisPlayType = players.get(pos);
                                if (thisPlayType != playerType) {
                                    mPlayerConfig.put("pl", thisPlayType);
                                    updatePlayerCfgView();
                                    listener.updatePlayerCfg();
                                    listener.replay(false);
                                    mPlayerBtn.requestFocus();
//                                  hideBottom();
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }

                        @Override
                        public String getDisplay(Integer val) {
                            Integer playerType = players.get(val);
                            return PlayerHelper.getPlayerName(playerType);
                        }
                    }, new DiffUtil.ItemCallback<Integer>() {
                        @Override
                        public boolean areItemsTheSame(@NonNull @NotNull Integer oldItem, @NonNull @NotNull Integer newItem) {
                            return oldItem.intValue() == newItem.intValue();
                        }

                        @Override
                        public boolean areContentsTheSame(@NonNull @NotNull Integer oldItem, @NonNull @NotNull Integer newItem) {
                            return oldItem.intValue() == newItem.intValue();
                        }
                    }, renders, defaultPos);
                    dialog.show();
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
        mPlayerIJKBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
//                myHandle.removeCallbacks(myRunnable);
//                myHandle.postDelayed(myRunnable, myHandleSeconds);
                try {
                    String ijk = mPlayerConfig.getString("ijk");
                    List<IJKCode> codecs = ApiConfig.get().getIjkCodes();
                    for (int i = 0; i < codecs.size(); i++) {
                        if (ijk.equals(codecs.get(i).getName())) {
                            if (i >= codecs.size() - 1)
                                ijk = codecs.get(0).getName();
                            else {
                                ijk = codecs.get(i + 1).getName();
                            }
                            break;
                        }
                    }
                    mPlayerConfig.put("ijk", ijk);
                    updatePlayerCfgView();
                    listener.updatePlayerCfg();
                    listener.replay(false);
                    //hideBottom();
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                mPlayerIJKBtn.requestFocus();
            }
        });
//        增加播放页面片头片尾时间重置
        findViewById(R.id.play_time_reset).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                myHandle.removeCallbacks(myRunnable);
                myHandle.postDelayed(myRunnable, myHandleSeconds);
                try {
                    mPlayerConfig.put("et", 0);
                    mPlayerConfig.put("st", 0);
                    updatePlayerCfgView();
                    listener.updatePlayerCfg();
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
        // 音轨选择 --------------------------------------
        mAudioTrackBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                FastClickCheckUtil.check(view);
                listener.selectAudioTrack();
            }
        });
        mPlayerTimeStartBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                myHandle.removeCallbacks(myRunnable);
                myHandle.postDelayed(myRunnable, myHandleSeconds);
                try {
                    int current = (int) mControlWrapper.getCurrentPosition();
                    int duration = (int) mControlWrapper.getDuration();
                    if (current > duration / 2) return;
                    mPlayerConfig.put("st",current/1000);
                    updatePlayerCfgView();
                    listener.updatePlayerCfg();
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
        mPlayerTimeStartBtn.setOnLongClickListener(new OnLongClickListener() {
            @Override
            public boolean onLongClick(View view) {
                try {
                    mPlayerConfig.put("st", 0);
                    updatePlayerCfgView();
                    listener.updatePlayerCfg();
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                return true;
            }
        });
        mPlayerTimeSkipBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                myHandle.removeCallbacks(myRunnable);
                myHandle.postDelayed(myRunnable, myHandleSeconds);
                try {
                    int current = (int) mControlWrapper.getCurrentPosition();
                    int duration = (int) mControlWrapper.getDuration();
                    if (current < duration / 2) return;
                    mPlayerConfig.put("et", (duration - current)/1000);
                    updatePlayerCfgView();
                    listener.updatePlayerCfg();
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
        mPlayerTimeSkipBtn.setOnLongClickListener(new OnLongClickListener() {
            @Override
            public boolean onLongClick(View view) {
                try {
                    mPlayerConfig.put("et", 0);
                    updatePlayerCfgView();
                    listener.updatePlayerCfg();
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                return true;
            }
        });
        /*
        mPlayerTimeStepBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                myHandle.removeCallbacks(myRunnable);
                myHandle.postDelayed(myRunnable, myHandleSeconds);
                int step = Hawk.get(HawkConfig.PLAY_TIME_STEP, 5);
                step += 5;
                if (step > 30) {
                    step = 5;
                }
                Hawk.put(HawkConfig.PLAY_TIME_STEP, step);
                updatePlayerCfgView();
            }
        });
        */
        mZimuBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                FastClickCheckUtil.check(view);
                listener.selectSubtitle();
                hideBottom();
            }
        });
    }

    @Override
    protected int getLayoutId() {
        return R.layout.player_vod_control_view;
    }

    public void showParse(boolean userJxList) {
        mParseRoot.setVisibility(userJxList ? VISIBLE : GONE);
    }

    private JSONObject mPlayerConfig = null;

    public void setPlayerConfig(JSONObject playerCfg) {
        this.mPlayerConfig = playerCfg;
        updatePlayerCfgView();
    }

    void updatePlayerCfgView() {
        try {
            int playerType = mPlayerConfig.getInt("pl");
            if (playerType == 1) {
                mSpeedHidell.setVisibility(VISIBLE);
                mSpeedll.setVisibility(VISIBLE);
            } else {
                mSpeedHidell.setVisibility(GONE);
                mSpeedll.setVisibility(GONE);
            }
            mPlayerTxt.setText(PlayerHelper.getPlayerName(playerType));
            mPlayerScaleTxt.setText(PlayerHelper.getScaleName(mPlayerConfig.getInt("sc")));
            mPlayerIJKBtn.setText(mPlayerConfig.getString("ijk"));
            mPlayerIJKBtn.setVisibility(playerType == 1 ? VISIBLE : GONE);
            mPlayerScaleTxt.setText(PlayerHelper.getScaleName(mPlayerConfig.getInt("sc")));
            mFFwdTxt.setText("x" + mPlayerConfig.getDouble("sp"));
            mPlayerTimeStartBtn.setText(PlayerUtils.stringForTime(mPlayerConfig.getInt("st") * 1000));
            mPlayerTimeSkipBtn.setText(PlayerUtils.stringForTime(mPlayerConfig.getInt("et") * 1000));
            //   mPlayerTimeStepBtn.setText(Hawk.get(HawkConfig.PLAY_TIME_STEP, 5) + "s");
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }


    public void setTitle(String playTitleInfo) {
        mPlayTitle.setText(playTitleInfo);
        mPlayTitle1.setText(playTitleInfo);
    }

    public void resetSpeed() {
        skipEnd = true;
        mHandler.removeMessages(1004);
        mHandler.sendEmptyMessageDelayed(1004, 100);
    }

    public interface VodControlListener {
        void playNext(boolean rmProgress);

        void playPre();

        void changeParse(ParseBean pb);

        void updatePlayerCfg();

        void replay(boolean replay);

        void errReplay();

        void selectSubtitle();

        void selectAudioTrack();
    }

    public void setListener(VodControlListener listener) {
        this.listener = listener;
    }

    private VodControlListener listener;

    private boolean skipEnd = true;

    @Override
    protected void setProgress(int duration, int position) {

        if (mIsDragging) {
            return;
        }
        super.setProgress(duration, position);
        if (skipEnd && position != 0 && duration != 0) {
            int et = 0;
            try {
                et = mPlayerConfig.getInt("et");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            if (et > 0 && position + (et * 1000) >= duration) {
                skipEnd = false;
                listener.playNext(true);
            }
        }

        // takagen99 : 影片剩余时间
        long TimeRemaining = mControlWrapper.getDuration() - mControlWrapper.getCurrentPosition();
        Calendar date = Calendar.getInstance();
        long t = date.getTimeInMillis();
        Date afterAdd = new Date(t + TimeRemaining);
        SimpleDateFormat timeEnd = new SimpleDateFormat("hh:mm aa", Locale.ENGLISH);
        if (isPaused) {
            mTimeEnd.setText(getContext().getString(R.string.vod_remaining_time) + " " + PlayerUtils.stringForTime((int) TimeRemaining) + " | " + getContext().getString(R.string.vod_ends_at) + " " + timeEnd.format(afterAdd));
        } else {
            mTimeEnd.setText(getContext().getString(R.string.vod_ends_at) + " " + timeEnd.format(afterAdd));
        }
        mPosition = position;
        mCurrentTime.setText(PlayerUtils.stringForTime(position));
        mTotalTime.setText(PlayerUtils.stringForTime(duration));
        if (duration > 0) {
            mSeekBar.setEnabled(true);
            int pos = (int) (position * 1.0 / duration * mSeekBar.getMax());
            mSeekBar.setProgress(pos);
        } else {
            mSeekBar.setEnabled(false);
        }
        int percent = mControlWrapper.getBufferedPercentage();
        if (percent >= 95) {
            mSeekBar.setSecondaryProgress(mSeekBar.getMax());
        } else {
            mSeekBar.setSecondaryProgress(percent * 10);
        }
    }

    private boolean simSlideStart = false;
    private int simSeekPosition = 0;
    private long simSlideOffset = 0;

    public void tvSlideStop() {
        if (!simSlideStart)
            return;
        mControlWrapper.seekTo(simSeekPosition);
        if (!mControlWrapper.isPlaying())
            mControlWrapper.start();
        simSlideStart = false;
        simSeekPosition = 0;
        simSlideOffset = 0;
    }

    public void tvSlideStart(int dir) {
        int duration = (int) mControlWrapper.getDuration();
        if (duration <= 0)
            return;
        if (!simSlideStart) {
            simSlideStart = true;
        }
        // 每次10秒
        simSlideOffset += (10000.0f * dir);
        int currentPosition = (int) mControlWrapper.getCurrentPosition();
        int position = (int) (simSlideOffset + currentPosition);
        if (position > duration) position = duration;
        if (position < 0) position = 0;
        updateSeekUI(currentPosition, position, duration);
        simSeekPosition = position;
    }

    @Override
    protected void updateSeekUI(int curr, int seekTo, int duration) {
        super.updateSeekUI(curr, seekTo, duration);
        if (seekTo > curr) {
            mProgressIcon.setImageResource(R.drawable.play_ffwd);
        } else {
            mProgressIcon.setImageResource(R.drawable.play_rewind);
        }
        mProgressText.setText(PlayerUtils.stringForTime(seekTo) + " / " + PlayerUtils.stringForTime(duration));

        // takagen99: Update Minibar
        int percent = (int) (((double) seekTo / (double) duration) * 100);
        mDialogVideoPauseBar.setProgress(percent);
        mDialogVideoProgressBar.setProgress(percent);

        mHandler.sendEmptyMessage(1000);
        mHandler.removeMessages(1001);
        mHandler.sendEmptyMessageDelayed(1001, 1000);
    }

    @Override
    protected void onPlayStateChanged(int playState) {
        super.onPlayStateChanged(playState);
        String currentUrl = "";
        if (mControlWrapper != null && mControlWrapper.getUrl() != null) {
            currentUrl = mControlWrapper.getUrl();
        }
        
        switch (playState) {
            case VideoView.STATE_IDLE:
                android.util.Log.d("VodController", "播放状态: IDLE");
                stopCacheTimer();
                break;
            case VideoView.STATE_PLAYING:
                // startProgress();
                isPaused = false;
                mPauseImg.setImageDrawable(getResources().getDrawable(R.drawable.v_pause));
                startProgress();
                startCacheTimer(); // 开始更新缓存状态
                
                // 记录播放行为用于智能预加载
                recordPlayBehavior();
                
                // 性能监控 - 播放开始
                try {
                    PerformanceMonitor.getInstance().onPlayStart(currentUrl);
                    LOG.i("开始播放性能监控: " + currentUrl);
                } catch (Exception e) {
                    LOG.e("播放性能监控初始化失败: " + e.getMessage());
                }
                
                android.util.Log.d("VodController", "播放状态: PLAYING, 缓存显示: " + 
                                 (mCacheStatus != null ? mCacheStatus.getText() : "null"));
                break;
            case VideoView.STATE_PAUSED:
                isPaused = true;
                mPauseImg.setImageDrawable(getResources().getDrawable(R.drawable.v_play));
                android.util.Log.d("VodController", "播放状态: PAUSED");
                break;
            case VideoView.STATE_ERROR:
                listener.errReplay();
                stopCacheTimer();
                
                // 性能监控 - 播放结束（错误）
                try {
                    PerformanceMonitor.getInstance().onPlayEnd(currentUrl);
                    LOG.i("结束播放性能监控(错误): " + currentUrl);
                } catch (Exception e) {
                    LOG.e("播放性能监控关闭失败: " + e.getMessage());
                }
                
                android.util.Log.d("VodController", "播放状态: ERROR");
                break;
            case VideoView.STATE_PREPARED:
            case VideoView.STATE_BUFFERED:
                mPlayLoadNetSpeed.setVisibility(GONE);
                if (mCacheStatus != null) {
                    mCacheStatus.setVisibility(VISIBLE); // 缓冲完成后显示缓存状态
                }
                
                // 性能监控 - 缓冲结束
                try {
                    PerformanceMonitor.getInstance().onBufferingEnd(currentUrl);
                    LOG.i("结束缓冲性能监控: " + currentUrl);
                } catch (Exception e) {
                    LOG.e("缓冲性能监控关闭失败: " + e.getMessage());
                }
                
                android.util.Log.d("VodController", "播放状态: BUFFERED, 缓存显示: " + 
                                 (mCacheStatus != null ? mCacheStatus.getText() : "null"));
                break;
            case VideoView.STATE_PREPARING:
            case VideoView.STATE_BUFFERING:
                if(mProgressRoot.getVisibility()==GONE) {
                    mPlayLoadNetSpeed.setVisibility(VISIBLE);
                    if (mCacheStatus != null) {
                        mCacheStatus.setVisibility(VISIBLE); // 缓冲中也显示缓存状态
                    }
                }
                
                // 性能监控 - 缓冲开始
                try {
                    PerformanceMonitor.getInstance().onBufferingStart();
                    LOG.i("开始缓冲性能监控");
                } catch (Exception e) {
                    LOG.e("缓冲性能监控初始化失败: " + e.getMessage());
                }
                
                android.util.Log.d("VodController", "播放状态: BUFFERING, 缓存显示: " + 
                                 (mCacheStatus != null ? mCacheStatus.getText() : "null"));
                break;
            case VideoView.STATE_PLAYBACK_COMPLETED:
                // 性能监控 - 播放结束（完成）
                try {
                    PerformanceMonitor.getInstance().onPlayEnd(currentUrl);
                    LOG.i("结束播放性能监控(完成): " + currentUrl);
                } catch (Exception e) {
                    LOG.e("播放性能监控关闭失败: " + e.getMessage());
                }
                
                listener.playNext(true);
                stopCacheTimer();
                android.util.Log.d("VodController", "播放状态: COMPLETED");
                break;
        }
    }
    
    // 记录播放行为用于智能预加载
    private void recordPlayBehavior() {
        try {
            // 获取当前播放信息
            Object tag = getTag();
            if (tag != null && tag instanceof com.github.tvbox.osc.bean.VodInfo) {
                com.github.tvbox.osc.bean.VodInfo vodInfo = (com.github.tvbox.osc.bean.VodInfo) tag;
                
                // 提取分类信息
                String category = vodInfo.type != null ? vodInfo.type.toString() : "unknown";
                
                // 记录观看行为
                CacheManager.SmartPreloader.recordWatchBehavior(category, vodInfo.id);
                
                // 提高当前视频的缓存优先级
                CacheManager.CachePriorityManager.increaseAccessPriority(vodInfo.id);
                
                // 预加载下一集
                if (vodInfo.playIndex < vodInfo.seriesMap.get(vodInfo.playFlag).size() - 1) {
                    com.github.tvbox.osc.util.PreloadManager.getInstance().preloadNextSeries(
                            vodInfo, 
                            2  // 预加载2集
                    );
                }
                
                android.util.Log.d("VodController", "已记录播放行为: 分类=" + category + ", ID=" + vodInfo.id);
            }
        } catch (Exception e) {
            android.util.Log.e("VodController", "记录播放行为失败: " + e.getMessage());
        }
    }

    boolean isBottomVisible() {
        return mBottomRoot.getVisibility() == VISIBLE;
    }



    void showBottom() {
        mHandler.removeMessages(1003);
        mHandler.sendEmptyMessage(1002);
    }

    void hideBottom() {
        mHandler.removeMessages(1002);
        mHandler.sendEmptyMessage(1003);
    }

    @Override
    public boolean onKeyEvent(KeyEvent event) {
        myHandle.removeCallbacks(myRunnable);
        if (super.onKeyEvent(event)) {
            return true;
        }
        int keyCode = event.getKeyCode();
        int action = event.getAction();
        if (isBottomVisible()) {
            myHandle.postDelayed(myRunnable, myHandleSeconds);
            return super.dispatchKeyEvent(event);
        }
        boolean isInPlayback = isInPlaybackState();
        if (action == KeyEvent.ACTION_DOWN) {
            if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT || keyCode == KeyEvent.KEYCODE_DPAD_LEFT) {
                if (isInPlayback) {
                    tvSlideStart(keyCode == KeyEvent.KEYCODE_DPAD_RIGHT ? 1 : -1);
                    return true;
                }
            } else if (keyCode == KeyEvent.KEYCODE_DPAD_CENTER || keyCode == KeyEvent.KEYCODE_ENTER || keyCode == KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE) {
                if (isInPlayback) {
                    togglePlay();
                    return true;
                }
//            } else if (keyCode == KeyEvent.KEYCODE_DPAD_UP) {  return true;// 闲置开启计时关闭透明底栏
            } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN || keyCode == KeyEvent.KEYCODE_DPAD_UP || keyCode== KeyEvent.KEYCODE_MENU) {
                if (!isBottomVisible()) {
                    showBottom();
                    myHandle.postDelayed(myRunnable, myHandleSeconds);
                    return true;
                }
            }
        } else if (action == KeyEvent.ACTION_UP) {
            if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT || keyCode == KeyEvent.KEYCODE_DPAD_LEFT) {
                if (isInPlayback) {
                    tvSlideStop();
                    return true;
                }
            }
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    public boolean onSingleTapConfirmed(MotionEvent e) {
        myHandle.removeCallbacks(myRunnable);
        if (!isBottomVisible()) {
            showBottom();
            // 闲置计时关闭
            myHandle.postDelayed(myRunnable, myHandleSeconds);
        } else {
            hideBottom();
        }
        return true;
    }


    @Override
    public boolean onBackPressed() {
        if (isClickBackBtn) {
            isClickBackBtn = false;
            if (isBottomVisible()) {
                hideBottom();
            }
            return false;
        }
        if (super.onBackPressed()) {
            return true;
        }
        if (isBottomVisible()) {
            hideBottom();
            return true;
        }
        return false;
    }


    public long getCurrentPosition(){

        return  mPosition;
    }

    // 更新缓存状态的方法
    private void updateCacheStatus() {
        try {
            if (mCacheStatus == null) {
                android.util.Log.e("VodController", "updateCacheStatus: mCacheStatus为空");
                return;
            }
            
            // 获取网络速度
            long tcpSpeed = mControlWrapper.getTcpSpeed();
            String speedText = PlayerHelper.getDisplaySpeed(tcpSpeed);
            
            // 获取已缓冲百分比
            int percent = mControlWrapper.getBufferedPercentage();
            
            // 获取视频时长和当前位置
            long duration = mControlWrapper.getDuration();
            long position = mControlWrapper.getCurrentPosition();
            
            // 计算缓冲了多少秒的内容
            long bufferedPosition = position + (long)(percent * 0.01 * (duration - position));
            long bufferedDuration = bufferedPosition - position;
            String bufferedTimeStr = PlayerUtils.stringForTime((int)bufferedDuration);
            
            // 计算剩余时间
            long remainingTime = duration - position;
            String remainingTimeStr = PlayerUtils.stringForTime((int)remainingTime);
            
            // 显示格式化后的缓存信息
            String cacheInfo;
            if (tcpSpeed > 0) {
                // 下载中
                cacheInfo = String.format("↓%s | 缓冲:%d%% | 已缓冲:%s", 
                        speedText, percent, bufferedTimeStr);
            } else if (percent < 100) {
                // 缓冲中但没有下载速度
                cacheInfo = String.format("缓冲:%d%% | 已缓冲:%s", 
                        percent, bufferedTimeStr);
            } else {
                // 已完全缓冲
                cacheInfo = String.format("已完全缓冲 | 剩余:%s", remainingTimeStr);
            }
            
            // 记录日志
            android.util.Log.d("VodController", "更新缓存状态: " + cacheInfo);
            
            // 更新UI必须在主线程
            mCacheStatus.post(() -> {
                try {
                    mCacheStatus.setText(cacheInfo);
                    
                    // 根据播放状态设置可见性
                    if (percent >= 100 && tcpSpeed == 0) {
                        // 如果完全缓冲且没有网络活动，5秒后隐藏
                        mHandler.postDelayed(() -> {
                            if (mCacheStatus != null && mCacheStatus.getVisibility() == VISIBLE) {
                                mCacheStatus.setVisibility(GONE);
                            }
                        }, 5000);
                    } else {
                        // 否则一直显示
                        mCacheStatus.setVisibility(VISIBLE);
                    }
                    
                    // 自动检查缓存情况
                    checkCacheStatus(tcpSpeed);
                    
                } catch (Exception e) {
                    android.util.Log.e("VodController", "更新UI时出错: " + e.getMessage());
                }
            });
            
        } catch (Exception e) {
            android.util.Log.e("VodController", "缓存状态更新错误: " + e.getMessage());
            if (mCacheStatus != null) {
                mCacheStatus.setText("缓存状态: 未知");
            }
            e.printStackTrace();
        }
    }
    
    // 自动检查缓存状态，如果需要则执行智能清理
    private void checkCacheStatus(long currentSpeed) {
        try {
            // 检查是否需要执行智能缓存清理
            if (CacheManager.SmartCacheCleaner.shouldRunAutoClean()) {
                // 在后台线程执行清理
                new Thread(() -> {
                    // 如果网速较高，使用最小清理模式避免影响播放
                    int cleanMode = (currentSpeed > 1024 * 1024) ? 
                            CacheManager.SmartCacheCleaner.CLEAN_MODE_MINIMAL : 
                            CacheManager.SmartCacheCleaner.CLEAN_MODE_AUTO;
                    
                    long freedSpace = CacheManager.SmartCacheCleaner.smartCleanCache(cleanMode);
                    android.util.Log.d("VodController", "智能缓存清理完成，释放: " + (freedSpace / 1024 / 1024) + "MB");
                    
                    // 额外执行过期缓存清理
                    CacheManager.SmartCacheCleaner.cleanOldCache();
                }).start();
            }
            
            // 如果缓存优先级开启，尝试为当前播放视频设置高优先级
            boolean cachePriorityEnabled = Hawk.get(com.github.tvbox.osc.util.HawkConfig.CACHE_PRIORITY_ENABLE, false);
            if (cachePriorityEnabled) {
                Object tag = getTag();
                if (tag != null && tag instanceof com.github.tvbox.osc.bean.VodInfo) {
                    com.github.tvbox.osc.bean.VodInfo vodInfo = (com.github.tvbox.osc.bean.VodInfo) tag;
                    CacheManager.CachePriorityManager.setContentPriority(
                            vodInfo.id, 
                            CacheManager.CachePriorityManager.PRIORITY_HIGH
                    );
                }
            }
        } catch (Exception e) {
            android.util.Log.e("VodController", "检查缓存状态时出错: " + e.getMessage());
        }
    }

    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        stopCacheTimer(); // 确保停止计时器
    }

}
