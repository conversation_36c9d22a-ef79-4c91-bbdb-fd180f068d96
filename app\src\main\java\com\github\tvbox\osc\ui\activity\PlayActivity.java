package com.github.tvbox.osc.ui.activity;

import android.annotation.TargetApi;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.JsPromptResult;
import android.webkit.JsResult;
import android.webkit.SslErrorHandler;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.DiffUtil;

import com.github.catvod.crawler.Spider;
import com.github.tvbox.osc.R;
import com.github.tvbox.osc.api.ApiConfig;
import com.github.tvbox.osc.base.App;
import com.github.tvbox.osc.base.BaseActivity;
import com.github.tvbox.osc.bean.ParseBean;
import com.github.tvbox.osc.bean.SourceBean;
import com.github.tvbox.osc.bean.Subtitle;
import com.github.tvbox.osc.bean.VodInfo;
import com.github.tvbox.osc.cache.CacheManager;
import com.github.tvbox.osc.event.RefreshEvent;
import com.github.tvbox.osc.player.IjkMediaPlayer;
import com.github.tvbox.osc.player.MyVideoView;
import com.github.tvbox.osc.player.TrackInfo;
import com.github.tvbox.osc.player.TrackInfoBean;
import com.github.tvbox.osc.player.controller.VodController;
import com.github.tvbox.osc.ui.adapter.SelectDialogAdapter;
import com.github.tvbox.osc.ui.dialog.SearchSubtitleDialog;
import com.github.tvbox.osc.ui.dialog.SelectDialog;
import com.github.tvbox.osc.ui.dialog.SubtitleDialog;
import com.github.tvbox.osc.util.AdBlocker;
import com.github.tvbox.osc.util.DefaultConfig;
import com.github.tvbox.osc.util.HawkConfig;
import com.github.tvbox.osc.util.LOG;
import com.github.tvbox.osc.util.MD5;
import com.github.tvbox.osc.util.NetworkUtils;
import com.github.tvbox.osc.util.OkGoHelper;
import com.github.tvbox.osc.util.PlayerHelper;
import com.github.tvbox.osc.util.PreloadManager;
import com.github.tvbox.osc.util.SharePreferencesUtils;
import com.github.tvbox.osc.util.XWalkUtils;
import com.github.tvbox.osc.util.thunder.Jianpian;
import com.github.tvbox.osc.util.thunder.Thunder;
import com.github.tvbox.osc.viewmodel.SourceViewModel;
import com.lzy.okgo.OkGo;
import com.lzy.okgo.callback.AbsCallback;
import com.lzy.okgo.model.HttpHeaders;
import com.lzy.okgo.model.Response;
import com.obsez.android.lib.filechooser.ChooserDialog;
import com.orhanobut.hawk.Hawk;

import org.greenrobot.eventbus.EventBus;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;
import org.xwalk.core.XWalkJavascriptResult;
import org.xwalk.core.XWalkResourceClient;
import org.xwalk.core.XWalkSettings;
import org.xwalk.core.XWalkUIClient;
import org.xwalk.core.XWalkView;
import org.xwalk.core.XWalkWebResourceRequest;
import org.xwalk.core.XWalkWebResourceResponse;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import me.jessyan.autosize.AutoSize;
import okhttp3.Request;
import xyz.doikki.videoplayer.player.AbstractPlayer;
import xyz.doikki.videoplayer.player.ProgressManager;
import xyz.doikki.videoplayer.player.VideoView;

import com.github.tvbox.osc.player.PlayerConfig;
import com.github.tvbox.osc.util.VideoCache;
import com.github.tvbox.osc.util.VideoCache.GetCallback;
import com.github.tvbox.osc.util.AdaptiveVideoLoader;
import com.github.tvbox.osc.util.StringCallback;
import com.github.tvbox.osc.util.ParallelVideoDownloader.PrioritySection;

public class PlayActivity extends BaseActivity {
    private MyVideoView mVideoView;
    private TextView mPlayLoadTip;
    private ImageView mPlayLoadErr;
    private ProgressBar mPlayLoading;
    private VodController mController;
    private SourceViewModel sourceViewModel;
    private Handler mHandler;

    private  long outTime = 3000;
    private boolean startCheck = false;
    private boolean isCreateThread = true;
    private long startPlayTimer = 0L;
    private long playUrlTimer = 0L;
    private boolean checkThreadIsStart = false;
    private Thread checkThread = new Thread(new Runnable() {
        @Override
        public void run() {

            while (isCreateThread) {
                if (startCheck) {
                    if (System.currentTimeMillis() - startPlayTimer > outTime && startPlayTimer != 0L) {
                        if (playErrorListener != null) {
                            startCheck = false;
                            startPlayTimer = 0L;
                            playUrlTimer = 0L;
                            Log.e("DetailActivity","loadNext 1- 6");
                            playErrorListener.onError();
                        }
                    }
                    else if (playUrlTimer != 0L && System.currentTimeMillis() - playUrlTimer > outTime){
                        try {
                            Thread.sleep(2000);
                        }catch (Exception e){

                        }
                        if(startCheck){
                            if (mController.getCurrentPosition() == 0L && playErrorListener != null) {
                                startCheck = false;
                                startPlayTimer = 0L;
                                playUrlTimer= 0L;
                                Log.e("DetailActivity", "loadNext 1- 88");
                                playErrorListener.onError();

                            }
                        }
                    }
                }
            }
        }
    });

    private PlayErrorListener playErrorListener;

    public void setPlayErrorListener(PlayErrorListener playErrorListener) {
        this.playErrorListener = playErrorListener;
    }

    public interface PlayErrorListener {
        public void onError();
    }

    private String progressKey;
    private PreloadManager preloadManager;
    private PlayerConfig mVodPlayerCfg;
    private VodInfo mVodInfo;
    private String cachePath;
    private int autoRetryCount = 0;
    private File cacheDir; // 缓存目录

    @Override
    protected int getLayoutResID() {
        return R.layout.activity_play;
    }

    @Override
    protected void init() {
        initView();
        initViewModel();
        initData();
        String string = SharePreferencesUtils.getString(this, "demo", "timeoutS", "10");

        outTime = Integer.parseInt(string) * 1000;

        if(!checkThreadIsStart){
            checkThread.start();
            checkThreadIsStart = true;
        }

        setPlayErrorListener(new PlayErrorListener() {
            @Override
            public void onError() {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        setResult(RESULT_OK,getIntent());
                        PlayActivity.this.finish();
                    }
                });

            }
        });

        preloadManager = PreloadManager.getInstance();
    }

    private void initView() {
        mHandler = new Handler(new Handler.Callback() {
            @Override
            public boolean handleMessage(@NonNull Message msg) {
                switch (msg.what) {
                    case 100:
                        stopParse();
                        errorWithRetry("嗅探错误", false);
                        break;
                }
                return false;
            }
        });
        mVideoView = findViewById(R.id.mVideoView);
        mPlayLoadTip = findViewById(R.id.play_load_tip);
        mPlayLoading = findViewById(R.id.play_loading);
        mPlayLoadErr = findViewById(R.id.play_load_error);

        mController = new VodController(this){
            @Override
            protected void onPlayStateChanged(int playState) {
                super.onPlayStateChanged(playState);
                if(playState == VideoView.STATE_PREPARING){
                    startPlayTimer = 0L;
                }
            }
        };


        mController.setCanChangePosition(true);
        mController.setEnableInNormal(true);
        mController.setGestureEnabled(true);
        ProgressManager progressManager = new ProgressManager() {
            @Override
            public void saveProgress(String url, long progress) {
                CacheManager.save(MD5.string2MD5(url), progress);
            }

            @Override
            public long getSavedProgress(String url) {
                int st = 0;
                try {
                    if (mVodPlayerCfg != null) {
                    st = mVodPlayerCfg.getInt("st");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                long skip = st * 1000;
                if (CacheManager.getCache(MD5.string2MD5(url)) == null) {
                    return skip;
                }
                long rec = (long) CacheManager.getCache(MD5.string2MD5(url));
                if (rec < skip)
                    return skip;
                return rec;
            }
        };
        mVideoView.setProgressManager(progressManager);
             mController.setListener(new VodController.VodControlListener() {
            @Override
            public void playNext(boolean rmProgress) {
                String preProgressKey = progressKey;
                PlayActivity.this.playNext();
                if (rmProgress && preProgressKey != null)
                    CacheManager.delete(MD5.string2MD5(preProgressKey), 0);
            }

            @Override
            public void playPre() {
                PlayActivity.this.playPrevious();
            }

            @Override
            public void changeParse(ParseBean pb) {
                autoRetryCount = 0;
                doParse(pb);
            }

            @Override
            public void updatePlayerCfg() {
                mVodInfo.playerCfg = mVodPlayerCfg.toString();
                EventBus.getDefault().post(new RefreshEvent(RefreshEvent.TYPE_REFRESH, mVodPlayerCfg));
            }

            @Override
            public void replay(boolean replay) {
                autoRetryCount = 0;
                play(replay);
            }

            @Override
            public void errReplay() {
                errorWithRetry("视频播放出错", false);
            }

            @Override
            public void selectSubtitle() {
                SubtitleDialog subtitleDialog = new SubtitleDialog(PlayActivity.this);
                subtitleDialog.setSubtitleViewListener(new SubtitleDialog.SubtitleViewListener() {
                    @Override
                    public void setTextSize(int size) {
                        mController.mSubtitleView.setTextSize(size);
                    }
                    @Override
                    public void setSubtitleDelay(int milliseconds) {
                        mController.mSubtitleView.setSubtitleDelay(milliseconds);
                    }
                });
                subtitleDialog.setSearchSubtitleListener(new SubtitleDialog.SearchSubtitleListener() {
                    @Override
                    public void openSearchSubtitleDialog() {
                        SearchSubtitleDialog searchSubtitleDialog = new SearchSubtitleDialog(PlayActivity.this);
                        searchSubtitleDialog.setSubtitleLoader(new SearchSubtitleDialog.SubtitleLoader() {
                            @Override
                            public void loadSubtitle(Subtitle subtitle) {

                                runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        String zimuUrl = subtitle.getUrl();
                                        LOG.i("Remote Subtitle Url: " + zimuUrl);
                                        setSubtitle(zimuUrl);//设置字幕
                                    }
                                });
                            }
                        });
                        if(mVodInfo.playFlag.contains("Ali")||mVodInfo.playFlag.contains("parse")){
                            searchSubtitleDialog.setSearchWord(mVodInfo.playNote);
                        }else {
                            searchSubtitleDialog.setSearchWord(mVodInfo.name);
                        }
                        searchSubtitleDialog.show();
                    }
                });
                subtitleDialog.setLocalFileChooserListener(new SubtitleDialog.LocalFileChooserListener() {
                    @Override
                    public void openLocalFileChooserDialog() {
                        new ChooserDialog(PlayActivity.this)
                                .withFilter(false, false, "srt", "ass", "scc", "stl", "ttml")
                                .withStartFile("/storage/emulated/0/Download")
                                .withChosenListener(new ChooserDialog.Result() {
                                    @Override
                                    public void onChoosePath(String path, File pathFile) {
                                        LOG.i("Local Subtitle Path: " + path);
                                        setSubtitle(path);//设置字幕
                                    }
                                })
                                .build()
                                .show();
                    }
                });
                subtitleDialog.show();
            }


            @Override
            public void selectAudioTrack() {
                selectMyAudioTrack();
            }

            //音轨
            void selectMyAudioTrack() {
                AbstractPlayer mediaPlayer = mVideoView.getMediaPlayer();
                if (!(mediaPlayer instanceof IjkMediaPlayer)) {
                    return;
                }
                TrackInfo trackInfo = null;
                if (mediaPlayer instanceof IjkMediaPlayer) {
                    trackInfo = ((IjkMediaPlayer)mediaPlayer).getTrackInfo();
                }
                if (trackInfo == null) {
                    Toast.makeText(mContext, "没有音轨", Toast.LENGTH_SHORT).show();
                    return;
                }
                List<TrackInfoBean> bean = trackInfo.getAudio();
                if (bean.size() < 1) return;
                SelectDialog<TrackInfoBean> dialog = new SelectDialog<>(PlayActivity.this);
                dialog.setTip("切换音轨");
                dialog.setAdapter(new SelectDialogAdapter.SelectDialogInterface<TrackInfoBean>() {
                    @Override
                    public void click(TrackInfoBean value, int pos) {
                        try {
                            for (TrackInfoBean audio : bean) {
                                audio.selected = audio.index == value.index;
                            }
                            mediaPlayer.pause();
                            long progress = mediaPlayer.getCurrentPosition();//保存当前进度，ijk 切换轨道 会有快进几秒
                            if (mediaPlayer instanceof IjkMediaPlayer) {
                                ((IjkMediaPlayer)mediaPlayer).setTrack(value.index);
                            }
                            new Handler().postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    mediaPlayer.seekTo(progress);
                                    mediaPlayer.start();
                                }
                            }, 800);
                            dialog.dismiss();
                        } catch (Exception e) {
                            LOG.e("切换音轨出错");
                        }
                    }

                    @Override
                    public String getDisplay(TrackInfoBean val) {
                        String name = val.name.replace("AUDIO,", "");
                        name = name.replace("N/A,", "");
                        name = name.replace(" ", "");
                        return val.index + " : " + val.language + " : " + name;
                    }
                }, new DiffUtil.ItemCallback<TrackInfoBean>() {
                    @Override
                    public boolean areItemsTheSame(@NonNull @NotNull TrackInfoBean oldItem, @NonNull @NotNull TrackInfoBean newItem) {
                        return oldItem.index == newItem.index;
                    }

                    @Override
                    public boolean areContentsTheSame(@NonNull @NotNull TrackInfoBean oldItem, @NonNull @NotNull TrackInfoBean newItem) {
                        return oldItem.index == newItem.index;
                    }
                }, bean, trackInfo.getAudioSelected(false));
                dialog.show();
            }

        });
        mVideoView.setVideoController(mController);
    }

    //设置字幕
    void setSubtitle(String path) {
        if (path != null && path .length() > 0) {
            // 设置字幕
            mController.mSubtitleView.setVisibility(View.INVISIBLE);
            mController.mSubtitleView.setSubtitlePath(path);
            mController.mSubtitleView.setVisibility(View.VISIBLE);
        }
    }

    void setTip(String msg, boolean loading, boolean err) {
        runOnUiThread(new Runnable() {//影魔 解决解析偶发闪退
            @Override
            public void run() {
                mPlayLoadTip.setText(msg);
                mPlayLoadTip.setVisibility(View.VISIBLE);
                mPlayLoading.setVisibility(loading ? View.VISIBLE : View.GONE);
                mPlayLoadErr.setVisibility(err ? View.VISIBLE : View.GONE);
            }
        });
    }

    void hideTip() {
        mPlayLoadTip.setVisibility(View.GONE);
        mPlayLoading.setVisibility(View.GONE);
        mPlayLoadErr.setVisibility(View.GONE);
    }

    void errorWithRetry(String err, boolean finish) {
        if (playErrorListener != null) {
            startCheck = false;
            Log.e("DetailActivity","loadNext 1- 7" + err);
            playErrorListener.onError();
            startPlayTimer = 0L;
            playUrlTimer = 0L;
        }
    }

    void playUrl(String url, HashMap<String, String> headers) {
        if (mVodInfo == null) return;
        
        try {
            if (url.startsWith("//")) {
                url = "http:" + url;
            }
            
            // 播放前释放资源
            stopParse();
            stopLoadWebView(false);
            
            // 获取当前播放剧集
            List<VodInfo.VodSeries> seriesList = mVodInfo.seriesMap.get(mVodInfo.playFlag);
            if (seriesList != null && !seriesList.isEmpty() && mVodInfo.playIndex < seriesList.size()) {
                VodInfo.VodSeries series = seriesList.get(mVodInfo.playIndex);
                // 记录播放历史
                // ...其余代码...
            }
            
            // 其余代码...
                    } catch (Exception e) {
            LOG.e("播放地址异常", e);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mVideoView != null) {
            mVideoView.release();
            mVideoView = null;
        }
        stopParse();
        stopLoadWebView(true);
        EventBus.getDefault().unregister(this);
        
        // 清理缓存监听器
        try {
            // 停止预加载任务，释放资源
            if (preloadManager != null) {
                preloadManager.stopAllTasks();
            }
            
            // 释放AdaptiveVideoLoader的资源
            AdaptiveVideoLoader.getInstance().cancelAllLoads();
            
            LOG.i("播放器资源已释放");
                            } catch (Exception e) {
            LOG.i("释放缓存资源时出错: " + e.getMessage());
        }
    }

    private void initViewModel() {
        sourceViewModel = new ViewModelProvider(this).get(SourceViewModel.class);
        sourceViewModel.playResult.observe(this, new Observer<JSONObject>() {
            @Override
            public void onChanged(JSONObject jsonResult) {
                try {
                    if (jsonResult != null) {
                        LOG.i("收到播放地址响应：" + jsonResult.toString());
                        // 解析结果转换为播放地址
                        String url = jsonResult.optString("url", "");
                        
                        // 获取headers
                        HashMap<String, String> headers = new HashMap<>();
                        try {
                            JSONObject headerJson = jsonResult.optJSONObject("header");
                            if (headerJson != null) {
                                Iterator<String> keys = headerJson.keys();
                                while (keys.hasNext()) {
                                    String key = keys.next();
                                    String value = headerJson.optString(key);
                                    if (!TextUtils.isEmpty(value)) {
                                        headers.put(key, value);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            LOG.e("解析header错误", e);
                        }
                        
                        // 使用缓存优化播放体验
                        tryLoadFromCache(url, headers);
                    }
                } catch (Exception e) {
                    LOG.e("播放器错误", e);
                }
            }
        });
    }

    private void initData() {
        Intent intent = getIntent();
        if (intent != null && intent.getExtras() != null) {
            Bundle bundle = intent.getExtras();
            mVodInfo = (VodInfo) bundle.getSerializable("VodInfo");
        if (mVodInfo != null) {
                progressKey = mVodInfo.sourceKey + "_" + mVodInfo.id + "_" + mVodInfo.playFlag + "_" + mVodInfo.playIndex;
                
                // 初始化缓存目录
                cacheDir = new File(App.getInstance().getExternalCacheDir(), "video_cache");
                if (!cacheDir.exists()) {
                    cacheDir.mkdirs();
                }
                
                // 开始初始化缓存路径
                List<VodInfo.VodSeries> seriesList = mVodInfo.seriesMap.get(mVodInfo.playFlag);
                if (seriesList != null && !seriesList.isEmpty() && mVodInfo.playIndex < seriesList.size()) {
                    VodInfo.VodSeries series = seriesList.get(mVodInfo.playIndex);
                    initCachePath(series.url);
                }
            }
        }
    }

    private String initCachePath(String videoUrl) {
        try {
            if (videoUrl != null && videoUrl.length() > 0) {
                String fileName = MD5.encode(videoUrl);
                cachePath = cacheDir + File.separator + fileName + ".mp4";
            } else {
                cachePath = null;
            }
        } catch (Exception e) {
            cachePath = null;
            LOG.e("初始化缓存路径失败", e);
        }
        return cachePath;
    }

    private void playNext() {
        try {
            if (mVodInfo != null) {
                List<VodInfo.VodSeries> seriesList = mVodInfo.seriesMap.get(mVodInfo.playFlag);
                if (seriesList != null && !seriesList.isEmpty() && 
                    mVodInfo.playIndex + 1 < seriesList.size()) {
                    mVodInfo.playIndex += 1;
        play(false);
    }
            }
        } catch (Exception e) {
            LOG.e("播放下一集失败", e);
        }
    }

    private void playPrevious() {
        try {
            if (mVodInfo != null && mVodInfo.playIndex > 0) {
                mVodInfo.playIndex -= 1;
            play(false);
            }
        } catch (Exception e) {
            LOG.e("播放上一集失败", e);
        }
    }

    private void doParse(ParseBean parseBean) {
        try {
            // 初始化解析逻辑
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("url", parseBean.getUrl());
            
            // 使用HTTP客户端执行解析请求
            OkGo.<String>post(parseBean.getParseUrl())
                    .tag(this)
                    .upJson(jsonObject)
                    .execute(new StringCallback() {
            @Override
                        public void onSuccess(Response<String> response) {
                            String content = response.body();
                            try {
                                JSONObject json = new JSONObject(content);
                                String url = json.optString("url", "");
                                if (!TextUtils.isEmpty(url)) {
                                    HashMap<String, String> headers = new HashMap<>();
                                    // 处理headers
                                    tryLoadFromCache(url, headers);
                } else {
                                    // 解析失败
                                    LOG.i("解析失败，无有效URL");
                }
                            } catch (Exception e) {
                                LOG.e("解析JSON出错", e);
            }
            }

            @Override
                        public void onError(Response<String> response) {
                            LOG.i("解析请求错误：" + response.message());
                        }
                    });
        } catch (Exception e) {
            LOG.e("执行解析错误", e);
        }
    }

    private void stopParse() {
        try {
            // 取消所有使用当前context作为tag的请求
            OkGo.getInstance().cancelTag(this);
            // 停止加载WebView相关内容
            stopLoadWebView(false);
        } catch (Exception e) {
            LOG.i("停止解析出错：" + e.getMessage());
        }
    }

    private void stopLoadWebView(boolean destroy) {
        // 应有停止WebView的实现
        // 这里是简化版
        try {
            // 停止WebView加载
        } catch (Exception e) {
            LOG.e("停止WebView加载出错");
        }
    }

    private void play(boolean reset) {
        try {
            if (mVodInfo != null) {
                if (reset) {
                    autoRetryCount = 0;
                }
                
                // 获取播放地址
                List<VodInfo.VodSeries> seriesList = mVodInfo.seriesMap.get(mVodInfo.playFlag);
                if (seriesList == null || seriesList.isEmpty() || 
                    mVodInfo.playIndex >= seriesList.size()) {
                    return;
                }
                
                VodInfo.VodSeries vs = seriesList.get(mVodInfo.playIndex);
                
                // 如果需要解析，先执行解析
                if (vs.needParse()) {
                    // 执行解析逻辑
                    ParseBean parseBean = new ParseBean();
                    parseBean.setUrl(vs.url);
        doParse(parseBean);
        } else {
                    // 直接播放
                    HashMap<String, String> headers = vs.headers;
                    if (headers == null) {
                        headers = new HashMap<>();
                    }
                    tryLoadFromCache(vs.url, headers);
                }
            }
        } catch (Exception e) {
            LOG.e("播放错误", e);
        }
    }

    /**
     * 使用缓存系统优化视频加载
     * @param url 视频URL
     * @param headers 请求头
     */
    private void tryLoadFromCache(String url, HashMap<String, String> headers) {
        if (url == null || url.isEmpty()) {
            playUrl(url, headers);
            return;
        }
        
        // 检查缓存功能是否启用
        if (!VideoCache.isEnabled()) {
            // 缓存未启用，直接使用普通加载
            playUrl(url, headers);
            return;
        }
        
        // 尝试从缓存加载视频
        VideoCache.loadFromCache(url, new VideoCache.GetCallback() {
                        @Override
            public void onSuccess(String cacheUrl, byte[] data) {
                LOG.i("从缓存加载视频成功: " + url + ", 大小: " + (data.length / 1024) + "KB");
                
                // 此处应将缓存的数据直接传递给播放器，但当前播放器接口不支持
                // 所以仍然通过URL加载，但速度会提升（因为系统缓存）
                playUrl(url, headers);
                
                // 告知用户使用了缓存
                if (Hawk.get(HawkConfig.DEBUG_OPEN, false)) {
                    mHandler.post(() -> {
                        Toast.makeText(App.getInstance(), "正在使用缓存的视频播放", Toast.LENGTH_SHORT).show();
                    });
                }
            }
            
                                @Override
            public void onPartialData(String cacheUrl, String filePath, Map<Long, Integer> segments) {
                LOG.i("从缓存加载部分视频: " + url + ", 片段数: " + segments.size());
                
                // 部分缓存，仍使用常规播放流程，但系统会优先读取缓存的部分
                playUrl(url, headers);
            }
            
                    @Override
            public void onRangeSuccess(String cacheUrl, byte[] data, long startPos) {
                // 此回调一般不会触发，按NotFound处理
                onNotFound(cacheUrl);
                    }

                    @Override
            public void onRangeNotFound(String cacheUrl, long startPos, int length) {
                // 此回调一般不会触发，按NotFound处理
                onNotFound(cacheUrl);
                    }

                    @Override
            public void onNotFound(String cacheUrl) {
                LOG.i("缓存中未找到视频: " + url + ", 使用普通加载");
                
                // 缓存未命中，使用普通加载
                playUrl(url, headers);
                
                // 尝试预缓存此视频，方便下次播放
                if (VideoCache.isSilentCacheEnabled()) {
                    VideoCache.silentCache(url);
                }
            }
            
            @Override
            public void onError(String cacheUrl, String error) {
                LOG.i("从缓存加载视频出错: " + url + ", 错误: " + error);
                
                // 出错时回退到普通加载
                playUrl(url, headers);
            }
        });
    }

    /**
     * 预加载HLS内容，并集成缓存系统
     */
    private void preloadHlsContent(String url, HashMap<String, String> headers) {
        com.github.tvbox.osc.util.AdaptiveVideoLoader loader = com.github.tvbox.osc.util.AdaptiveVideoLoader.getInstance();
        
        // 创建HLS特定的监听器并与缓存系统集成
        com.github.tvbox.osc.util.AdaptiveVideoLoader.HlsLoadListener hlsListener = new com.github.tvbox.osc.util.AdaptiveVideoLoader.HlsLoadListener() {
        @Override
            public void onHlsSuccess(String url, com.github.tvbox.osc.util.AdaptiveVideoLoader.HlsPreloadResult result) {
                LOG.i("HLS预加载成功: 共" + result.tsUrls.size() + "个分片, 预加载了" + 
                      result.preloadedSegments.size() + "个分片");
                
                // 如果缓存功能启用，缓存m3u8和预加载的分片
                if (VideoCache.isEnabled() && com.github.tvbox.osc.util.VideoCache.isTsSegmentEnabled()) {
                    // 缓存m3u8内容
                    com.github.tvbox.osc.util.VideoCache.cacheVideo(url, result.m3u8Content.getBytes());
                    
                    // 缓存预加载的TS分片
                    if (result.tsUrls != null && result.preloadedSegments != null) {
                        for (int i = 0; i < result.preloadedSegments.size(); i++) {
                            try {
                                String tsUrl = result.tsUrls.get(i);
                                byte[] segmentData = result.preloadedSegments.get(i);
                                
                                if (segmentData != null && segmentData.length > 0) {
                                    com.github.tvbox.osc.util.VideoCache.cacheVideo(tsUrl, segmentData);
                                }
                            } catch (IndexOutOfBoundsException e) {
                                // 安全检查，忽略索引错误
                                break;
                            }
                        }
                    }
                }
            }
            
            @Override
            public void onProgress(String url, int progress) {
                LOG.i("HLS预加载进度: " + progress + "%");
            }

            @Override
            public void onSuccess(String url, byte[] data) {
                // 这个方法不应该被直接调用
                LOG.i("HLS m3u8内容已加载，大小: " + (data.length) + "字节");
                
                // 如果缓存功能启用，缓存m3u8内容
                if (VideoCache.isEnabled()) {
                    com.github.tvbox.osc.util.VideoCache.cacheVideo(url, data);
                }
            }

        @Override
            public void onError(String url, String error) {
                LOG.i("HLS预加载失败: " + error);
            }
        };
        
        // 使用HLS特定的加载方法
        loader.loadHlsVideo(url, hlsListener);
    }
    
    /**
     * 异步预加载视频首段数据，并与缓存系统集成
     */
    private void preloadFirstSegmentAsync(String url, HashMap<String, String> headers) {
        try {
            if (NetworkUtils.checkNetworkAvailable()) {
                LOG.i("启动视频首段预加载: " + url);
                
                // 检查是否为HLS格式
                boolean isHls = url.toLowerCase().contains(".m3u8") || url.toLowerCase().contains("/hls/");
                if (isHls) {
                    // HLS格式使用HLS预加载
                    preloadHlsContent(url, headers);
            return;
        }
                
                // 获取当前播放位置
                long currentPosition = 0;
                if (mVideoView != null) {
                    currentPosition = mVideoView.getCurrentPosition();
                }
                
                // 创建优先加载区域
                List<int[]> prioritySections = new ArrayList<>();
                PrioritySection[] sections = com.github.tvbox.osc.util.ParallelVideoDownloader.createPrioritySectionsForPosition(
                    url, currentPosition, false, getVideoDurationWithCurrentSpeedMs()
                );
                
                // 将PrioritySection数组转换为List<int[]>格式
                for (PrioritySection section : sections) {
                    prioritySections.add(new int[]{(int)section.startPos, (int)(section.startPos + section.length - 1)});
                }
                
                // 使用自适应视频加载器加载视频
                com.github.tvbox.osc.util.AdaptiveVideoLoader loader = com.github.tvbox.osc.util.AdaptiveVideoLoader.getInstance();
                
                // 使用预加载监听器，并集成缓存
                com.github.tvbox.osc.util.AdaptiveVideoLoader.LoadListener listener = new com.github.tvbox.osc.util.AdaptiveVideoLoader.LoadListener() {
            @Override
                    public void onProgress(String url, int progress) {
                        LOG.i("视频预加载进度: " + progress + "%");
            }

            @Override
                    public void onSuccess(String url, byte[] data) {
                        LOG.i("视频预加载完成，大小: " + (data.length / 1024) + "KB");
                        
                        // 如果缓存功能启用，缓存预加载内容
                        if (VideoCache.isEnabled()) {
                            com.github.tvbox.osc.util.VideoCache.cacheVideo(url, data);
                        }
        }

        @Override
                    public void onError(String url, String error) {
                        LOG.i("视频预加载失败: " + error);
                    }
                };
                
                // 使用分块加载，启用优先区域
                loader.loadVideoWithChunks(url, listener, prioritySections);
            }
        } catch (Exception e) {
            LOG.i("启动视频预加载出错: " + e.getMessage());
        }
    }

    /**
     * 获取视频总时长
     * 根据当前播放速度调整
     */
    private long getVideoDurationWithCurrentSpeedMs() {
        if (mVideoView != null) {
            // 获取视频原始时长
            long originalDuration = mVideoView.getDuration();
            
            // 如果播放速度不是1.0x，根据速度调整时长
            float speed = mVideoView.getSpeed();
            if (speed != 1.0f && speed > 0) {
                return (long) (originalDuration / speed);
            }
            
            return originalDuration;
        }
        return 0;
    }
}