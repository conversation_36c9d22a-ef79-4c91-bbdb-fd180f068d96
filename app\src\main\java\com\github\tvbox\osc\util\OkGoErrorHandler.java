package com.github.tvbox.osc.util;

import android.content.Context;
import android.util.Log;

import com.lzy.okgo.callback.AbsCallback;
import com.lzy.okgo.model.Response;
import com.lzy.okgo.request.base.Request;

/**
 * OkGo网络请求错误处理工具类
 */
public class OkGoErrorHandler {
    private static final String TAG = "OkGoErrorHandler";

    /**
     * 处理OkGo请求错误
     * @param context 上下文
     * @param error 错误
     * @param url 请求URL
     * @return 是否已处理错误
     */
    public static boolean handleError(Context context, Throwable error, String url) {
        if (error == null) return false;
        
        Log.e(TAG, "网络请求错误: " + error.getMessage() + ", URL: " + url);
        return false;
    }
    
    /**
     * 创建一个带错误处理的OkGo回调封装
     * @param context 上下文
     * @param callback 原始回调
     * @return 包装后的回调
     */
    public static <T> AbsCallback<T> withErrorHandling(Context context, AbsCallback<T> callback) {
        return new AbsCallback<T>() {
            @Override
            public void onStart(Request<T, ? extends Request> request) {
                callback.onStart(request);
            }
            
            @Override
            public void onSuccess(Response<T> response) {
                callback.onSuccess(response);
            }
            
            @Override
            public void onError(Response<T> response) {
                // 处理错误
                handleError(context, response.getException(), response.getRawCall().request().url().toString());
                
                // 调用原始回调的onError
                callback.onError(response);
            }
            
            @Override
            public void onFinish() {
                callback.onFinish();
            }
            
            @Override
            public T convertResponse(okhttp3.Response response) throws Throwable {
                return callback.convertResponse(response);
            }
        };
    }
} 