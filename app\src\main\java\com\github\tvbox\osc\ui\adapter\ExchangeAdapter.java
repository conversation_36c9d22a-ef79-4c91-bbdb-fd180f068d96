package com.github.tvbox.osc.ui.adapter;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.BounceInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.github.tvbox.osc.R;
import com.github.tvbox.osc.bean.Shop;
import com.github.tvbox.osc.ui.activity.ExchangeActivity.OnItemClickListener;
import java.util.List;

public class ExchangeAdapter extends RecyclerView.Adapter<ExchangeAdapter.MyViewHolder> {

    private List<Shop> shopList;
    private OnItemClickListener mOnItemClickListener;

    public ExchangeAdapter(List<Shop> shopList) {
        this.shopList = shopList;
    }
    
    // 设置点击监听器
    public void setOnItemClickListener(OnItemClickListener listener) {
        this.mOnItemClickListener = listener;
    }
    
    // 获取指定位置的商品
    public Shop getItem(int position) {
        if (shopList != null && position >= 0 && position < shopList.size()) {
            return shopList.get(position);
        }
        return null;
    }

    @NonNull
    @Override
    public MyViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_user_shop, parent, false);
        final MyViewHolder holder = new MyViewHolder(view);
        
        // 设置点击事件
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                int position = holder.getBindingAdapterPosition();
                if (position != RecyclerView.NO_POSITION && mOnItemClickListener != null) {
                    mOnItemClickListener.onItemClick(v, position);
                }
            }
        });
        
        // 设置焦点变化事件
        view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View view, boolean hasFocus) {
                // 判断是否为VIP卡片
                boolean isVip = holder.vipCardLayout.getVisibility() == View.VISIBLE;
                
                if (hasFocus) {
                    // 显示选中效果
                    holder.selectedOverlay.setVisibility(View.VISIBLE);
                    
                    // 缩放动画
                    float scale = isVip ? 1.08f : 1.05f; // VIP卡片放大更明显
                    startScaleAnimation(view, 1.0f, scale, 300);
                    
                    // 为VIP卡片添加轻微旋转效果
                    if (isVip && holder.vipCardImage != null) {
                        ObjectAnimator rotationAnimator = ObjectAnimator.ofFloat(holder.vipCardImage, "rotation", 0f, 1f, 0f, -1f, 0f);
                        rotationAnimator.setDuration(300);
                        rotationAnimator.setInterpolator(new BounceInterpolator());
                        rotationAnimator.start();
                    }
                } else {
                    // 隐藏选中效果
                    holder.selectedOverlay.setVisibility(View.INVISIBLE);
                    
                    // 缩放动画
                    float scale = isVip ? 1.08f : 1.05f;
                    startScaleAnimation(view, scale, 1.0f, 300);
                    
                    // 恢复旋转角度
                    if (isVip && holder.vipCardImage != null) {
                        ObjectAnimator rotationAnimator = ObjectAnimator.ofFloat(holder.vipCardImage, "rotation", 0f);
                        rotationAnimator.setDuration(150);
                        rotationAnimator.start();
                    }
                }
            }
        });
        
        return holder;
    }
    
    // 启动缩放动画
    private void startScaleAnimation(View view, float fromScale, float toScale, int duration) {
        AnimatorSet animatorSet = new AnimatorSet();
        ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(view, "scaleX", fromScale, toScale);
        ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(view, "scaleY", fromScale, toScale);
        scaleXAnimator.setDuration(duration);
        scaleYAnimator.setDuration(duration);
        scaleXAnimator.setInterpolator(new BounceInterpolator());
        scaleYAnimator.setInterpolator(new BounceInterpolator());
        animatorSet.playTogether(scaleXAnimator, scaleYAnimator);
        animatorSet.start();
    }

    @Override
    public void onBindViewHolder(@NonNull MyViewHolder holder, int position) {
        Shop shop = shopList.get(position);
        String title = shop.getTitle();
        String price = shop.getPrice() + " 积分";
        
        // 判断是否为VIP会员商品，如果是，则使用VIP卡片布局
        boolean isVip = shop.getTitle().contains("VIP") || 
                       shop.getTitle().contains("会员");
        
        // 设置图片适配屏幕的尺寸
        if (holder.vipCardImage != null) {
            ViewGroup.LayoutParams imageParams = holder.vipCardImage.getLayoutParams();
            if (imageParams != null) {
                imageParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
                holder.vipCardImage.setLayoutParams(imageParams);
            }
        }
        
        if (isVip) {
            // 显示VIP卡片布局，隐藏普通布局
            if (holder.vipCardLayout != null) {
                holder.vipCardLayout.setVisibility(View.VISIBLE);
            }
            if (holder.itemBg != null) {
                holder.itemBg.setVisibility(View.GONE);
            }
            
            // 设置背景透明，让VIP卡片图片显示
            if (holder.itemUserShopGroup != null) {
                holder.itemUserShopGroup.setBackgroundResource(android.R.color.transparent);
            }
            
            // 设置VIP卡片数据
            if (holder.vipCardImage != null) {
                holder.vipCardImage.setImageResource(R.drawable.vip_card_activity_bg);
            }
            if (holder.vipPriceTextView != null) {
                holder.vipPriceTextView.setText(price);
            }
            
            // 确保商品名称显示在VIP卡片的兑换按钮上
            if (holder.vipExchangeBtn != null) {
                holder.vipExchangeBtn.setText("立即兑换 " + title);
            }
        } else {
            // 普通商品卡片
            if (holder.vipCardLayout != null) {
                holder.vipCardLayout.setVisibility(View.GONE);
            }
            if (holder.itemBg != null) {
                holder.itemBg.setVisibility(View.VISIBLE);
            }
            
            // 恢复背景
            if (holder.itemUserShopGroup != null) {
                holder.itemUserShopGroup.setBackgroundResource(R.drawable.shape_user_search);
            }
            
            // 设置普通卡片数据
            if (holder.shopTitle != null) {
                holder.shopTitle.setText(title);
                holder.shopTitle.setTextColor(Color.parseColor("#FFF9C690"));
            }
            if (holder.shopPrice != null) {
                holder.shopPrice.setText(price);
                holder.shopPrice.setTextColor(Color.WHITE);
            }
        }
    }

    @Override
    public int getItemCount() {
        return shopList.size();
    }

    public class MyViewHolder extends RecyclerView.ViewHolder {
        // 普通商品布局元素
        public TextView shopTitle;
        public TextView shopPrice;
        public View itemUserShopGroup;
        public LinearLayout itemBg;
        
        // VIP会员卡布局元素
        public LinearLayout vipCardLayout;
        public ImageView vipCardImage;
        public TextView vipPriceTextView;
        public TextView vipExchangeBtn;
        
        // 选中效果
        public View selectedOverlay;

        public MyViewHolder(@NonNull View itemView) {
            super(itemView);
            // 获取普通商品布局元素
            itemUserShopGroup = itemView.findViewById(R.id.item_user_shop_group);
            
            try {
                itemBg = itemView.findViewById(R.id.item_bg);
            } catch (Exception e) {
                // 忽略可能的错误
            }
            
            try {
                shopTitle = itemView.findViewById(R.id.tv_shop_title);
            } catch (Exception e) {
                // 忽略可能的错误
            }
            
            try {
                shopPrice = itemView.findViewById(R.id.tv_shop_price);
            } catch (Exception e) {
                // 忽略可能的错误
            }
            
            // 获取VIP会员卡布局元素
            try {
                vipCardLayout = itemView.findViewById(R.id.vip_card_layout);
            } catch (Exception e) {
                // 忽略可能的错误
            }
            
            try {
                vipCardImage = itemView.findViewById(R.id.vip_card_image);
            } catch (Exception e) {
                // 忽略可能的错误
            }
            
            try {
                vipPriceTextView = itemView.findViewById(R.id.tv_shop_price_vip);
            } catch (Exception e) {
                // 忽略可能的错误
            }
            
            try {
                vipExchangeBtn = itemView.findViewById(R.id.tv_exchange_btn_vip);
            } catch (Exception e) {
                // 忽略可能的错误
            }
            
            // 获取选中效果遮罩
            try {
                selectedOverlay = itemView.findViewById(R.id.selected_overlay);
            } catch (Exception e) {
                // 忽略可能的错误
            }
        }
    }
} 