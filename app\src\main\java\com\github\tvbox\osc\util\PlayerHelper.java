package com.github.tvbox.osc.util;

import android.app.Activity;
import android.content.Context;

import com.github.tvbox.osc.api.ApiConfig;
import com.github.tvbox.osc.bean.IJKCode;
import com.github.tvbox.osc.player.IjkMediaPlayer;
import com.github.tvbox.osc.player.render.SurfaceRenderViewFactory;
import com.github.tvbox.osc.player.thirdparty.Kodi;
import com.github.tvbox.osc.player.thirdparty.MXPlayer;
import com.github.tvbox.osc.player.thirdparty.ReexPlayer;
import com.orhanobut.hawk.Hawk;

import org.json.JSONException;
import org.json.JSONObject;

import tv.danmaku.ijk.media.player.IjkLibLoader;
import xyz.doikki.videoplayer.exo.ExoMediaPlayerFactory;
import xyz.doikki.videoplayer.player.AndroidMediaPlayerFactory;
import xyz.doikki.videoplayer.player.PlayerFactory;
import xyz.doikki.videoplayer.player.VideoView;
import xyz.doikki.videoplayer.render.RenderViewFactory;
import xyz.doikki.videoplayer.render.TextureRenderViewFactory;

import java.util.ArrayList;
import java.util.HashMap;

public class PlayerHelper {
    public static void updateCfg(VideoView videoView, JSONObject playerCfg) {
        int playerType = Hawk.get(HawkConfig.PLAY_TYPE, 0);
        int renderType = Hawk.get(HawkConfig.PLAY_RENDER, 0);
        String ijkCode = Hawk.get(HawkConfig.IJK_CODEC, "软解码");
        int scale = Hawk.get(HawkConfig.PLAY_SCALE, 0);
        try {
            playerType = playerCfg.getInt("pl");
            renderType = playerCfg.getInt("pr");
            ijkCode = playerCfg.getString("ijk");
            scale = playerCfg.getInt("sc");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        IJKCode codec = ApiConfig.get().getIJKCodec(ijkCode);
        PlayerFactory playerFactory;
        if (playerType == 1) {
            playerFactory = new PlayerFactory<IjkMediaPlayer>() {
                @Override
                public IjkMediaPlayer createPlayer(Context context) {
                    return new IjkMediaPlayer(context, codec);
                }
            };
            try {
                tv.danmaku.ijk.media.player.IjkMediaPlayer.loadLibrariesOnce(new IjkLibLoader() {
                    @Override
                    public void loadLibrary(String s) throws UnsatisfiedLinkError, SecurityException {
                        try {
                            System.loadLibrary(s);
                        } catch (Throwable th) {
                            th.printStackTrace();
                        }
                    }
                });
            } catch (Throwable th) {
                th.printStackTrace();
            }
        } else if (playerType == 2) {
            playerFactory = ExoMediaPlayerFactory.create();
        } else {
            playerFactory = AndroidMediaPlayerFactory.create();
        }
        RenderViewFactory renderViewFactory = null;
        switch (renderType) {
            case 0:
            default:
                renderViewFactory = TextureRenderViewFactory.create();
                break;
            case 1:
                renderViewFactory = SurfaceRenderViewFactory.create();
                break;
        }
        videoView.setPlayerFactory(playerFactory);
        videoView.setRenderViewFactory(renderViewFactory);
        videoView.setScreenScaleType(scale);
    }

    public static void updateCfg(VideoView videoView) {
        int playType = Hawk.get(HawkConfig.PLAY_TYPE, 0);
        PlayerFactory playerFactory;
        if (playType == 1) {
            playerFactory = new PlayerFactory<IjkMediaPlayer>() {
                @Override
                public IjkMediaPlayer createPlayer(Context context) {
                    return new IjkMediaPlayer(context, null);
                }
            };
            try {
                tv.danmaku.ijk.media.player.IjkMediaPlayer.loadLibrariesOnce(new IjkLibLoader() {
                    @Override
                    public void loadLibrary(String s) throws UnsatisfiedLinkError, SecurityException {
                        try {
                            System.loadLibrary(s);
                        } catch (Throwable th) {
                            th.printStackTrace();
                        }
                    }
                });
            } catch (Throwable th) {
                th.printStackTrace();
            }
        } else if (playType == 2) {
            playerFactory = ExoMediaPlayerFactory.create();
        } else {
            playerFactory = AndroidMediaPlayerFactory.create();
        }
        int renderType = Hawk.get(HawkConfig.PLAY_RENDER, 0);
        RenderViewFactory renderViewFactory = null;
        switch (renderType) {
            case 0:
            default:
                renderViewFactory = TextureRenderViewFactory.create();
                break;
            case 1:
                renderViewFactory = SurfaceRenderViewFactory.create();
                break;
        }
        videoView.setPlayerFactory(playerFactory);
        videoView.setRenderViewFactory(renderViewFactory);
    }


    public static void init() {
        try {
            tv.danmaku.ijk.media.player.IjkMediaPlayer.loadLibrariesOnce(new IjkLibLoader() {
                @Override
                public void loadLibrary(String s) throws UnsatisfiedLinkError, SecurityException {
                    try {
                        System.loadLibrary(s);
                    } catch (Throwable th) {
                        th.printStackTrace();
                    }
                }
            });
        } catch (Throwable th) {
            th.printStackTrace();
        }
        
        // 初始化时配置视频播放器的高级缓冲策略
        configureVideoBufferStrategy();
    }
    
    /**
     * 配置视频播放器的缓冲策略
     */
    private static void configureVideoBufferStrategy() {
        // 修改默认缓冲配置，根据用户设备性能和网络状况优化
        try {
            // 针对IJK播放器的优化
            if (Hawk.contains(HawkConfig.IJK_CODEC)) {
                String ijkCode = Hawk.get(HawkConfig.IJK_CODEC, "软解码");
                IJKCode codec = ApiConfig.get().getIJKCodec(ijkCode);
                if (codec != null && codec.getName().equals("硬解码")) {
                    // 如果用户选择了硬解码，则加大缓冲区以提高性能
                    HashMap<String, String> options = codec.getOption();
                    // 设置缓冲相关选项
                    options.put("buffer-size", "1024000"); // 1MB 缓冲区
                    options.put("max-buffer-size", "5120000"); // 5MB 最大缓冲
                    options.put("min-frames", "50"); // 最小帧数
                    options.put("packet-buffering", "1"); // 启用包缓冲
                    
                    // 网络协议相关优化
                    options.put("reconnect", "1"); // 启用重连
                    options.put("framedrop", "1"); // 启用丢帧处理
                    options.put("rtsp-transport", "tcp"); // RTSP走TCP协议
                    options.put("start-on-prepared", "1"); // 准备好立即开始播放
                    options.put("probsize", "4096"); // 探测数据大小
                    
                    // 设置HLS特定参数
                    options.put("hls-multi-resolution", "1"); // 允许多分辨率
                    
                    LOG.i("配置IJK硬解码优化参数");
                }
            }
            
            // 缓存配置优化
            long cacheSize = 0;
            try {
                cacheSize = Hawk.get(HawkConfig.PLAY_CACHE, (long)0);
            } catch (Exception e) {
                LOG.e("获取缓存大小失败: " + e.getMessage());
            }
            
            if (cacheSize == 0) {
                // 默认设置合理的缓存大小
                cacheSize = 200 * 1024 * 1024; // 200MB
                Hawk.put(HawkConfig.PLAY_CACHE, cacheSize);
                LOG.i("设置视频缓存大小为: " + (cacheSize / 1024 / 1024) + "MB");
            }
        } catch (Exception e) {
            LOG.e("配置播放器缓冲策略出错: " + e.getMessage());
        }
    }
    
    /**
     * 应用网络感知的播放器参数优化
     * @param videoView 视频播放器视图
     * @param url 播放地址
     */
    public static void applyNetworkOptimization(VideoView videoView, String url) {
        if (videoView == null || url == null) return;
        
        try {
            // 获取网络优化参数
            android.os.Bundle params = NetworkRequestManager.optimizePlaybackParameters(url);
            
            // 应用缓冲参数
            videoView.skipPositionWhenPlay(0); // 重置跳转位置
            
            // 记录日志
            LOG.i("应用网络感知播放器参数优化，URL: " + url);
        } catch (Exception e) {
            LOG.e("应用网络优化参数出错: " + e.getMessage());
        }
    }

    public static String getPlayerName(int playType) {
        if (playType == 1) {
            return "IJK播放器";
        } else if (playType == 2) {
            return "Exo播放器";
        } else if (playType == 10) {
            return "MXPlayer";
        } else if (playType == 11) {
            return "Reex";
        } else {
            return "系统播放器";
        }
    }

    public static String getRenderName(int renderType) {
        if (renderType == 1) {
            return "SurfaceView";
        } else {
            return "TextureView";
        }
    }

    public static String getScaleName(int screenScaleType) {
        String scaleText = "默认";
        switch (screenScaleType) {
            case VideoView.SCREEN_SCALE_DEFAULT:
                scaleText = "默认";
                break;
            case VideoView.SCREEN_SCALE_16_9:
                scaleText = "16:9";
                break;
            case VideoView.SCREEN_SCALE_4_3:
                scaleText = "4:3";
                break;
            case VideoView.SCREEN_SCALE_MATCH_PARENT:
                scaleText = "填充";
                break;
            case VideoView.SCREEN_SCALE_ORIGINAL:
                scaleText = "原始";
                break;
            case VideoView.SCREEN_SCALE_CENTER_CROP:
                scaleText = "裁剪";
                break;
        }
        return scaleText;
    }

    public static Boolean runExternalPlayer(int playerType, Activity activity, String url, String title, String subtitle, HashMap<String, String> headers) {
        boolean callResult = false;
        switch (playerType) {
            case 10: {
                callResult = MXPlayer.run(activity, url, title, subtitle, headers);
                break;
            }
            case 11: {
                callResult = ReexPlayer.run(activity, url, title, subtitle, headers);
                break;
            }
            case 12: {
                callResult = Kodi.run(activity, url, title, subtitle, headers);
                break;
            }
        }
        return callResult;
    }

    private static HashMap<Integer, Boolean> mPlayersExistInfo = null;
    public static HashMap<Integer, Boolean> getPlayersExistInfo() {
        if (mPlayersExistInfo == null) {
            HashMap<Integer, Boolean> playersExist = new HashMap<>();
            playersExist.put(0, true);
            playersExist.put(1, true);
            playersExist.put(2, true);
            playersExist.put(10, MXPlayer.getPackageInfo() != null);
            playersExist.put(11, ReexPlayer.getPackageInfo() != null);
            playersExist.put(12, Kodi.getPackageInfo() != null);
            mPlayersExistInfo = playersExist;
        }
        return mPlayersExistInfo;
    }

    public static ArrayList<Integer> getExistPlayerTypes() {
        HashMap<Integer, Boolean> playersExistInfo = getPlayersExistInfo();
        ArrayList<Integer> existPlayers = new ArrayList<>();
        for(Integer playerType : playersExistInfo.keySet()) {
            if (playersExistInfo.get(playerType)) {
                existPlayers.add(playerType);
            }
        }
        return existPlayers;
    }

    public static String getDisplaySpeed(long speed) {
        if(speed > 1048576)
            return (speed / 1048576) + "Mb/s";
        else if(speed > 1024)
            return (speed / 1024) + "Kb/s";
        else
            return speed > 0?speed + "B/s":"";
    }

    /**
     * 根据播放器类型获取播放器工厂
     * @param playerType 播放器类型
     * @return 播放器工厂实例
     */
    public static PlayerFactory getPlayerFactory(int playerType) {
        PlayerFactory playerFactory;
        if (playerType == 1) {
            playerFactory = new PlayerFactory<IjkMediaPlayer>() {
                @Override
                public IjkMediaPlayer createPlayer(Context context) {
                    return new IjkMediaPlayer(context, null);
                }
            };
            try {
                tv.danmaku.ijk.media.player.IjkMediaPlayer.loadLibrariesOnce(new IjkLibLoader() {
                    @Override
                    public void loadLibrary(String s) throws UnsatisfiedLinkError, SecurityException {
                        try {
                            System.loadLibrary(s);
                        } catch (Throwable th) {
                            th.printStackTrace();
                        }
                    }
                });
            } catch (Throwable th) {
                th.printStackTrace();
            }
        } else if (playerType == 2) {
            playerFactory = ExoMediaPlayerFactory.create();
        } else {
            playerFactory = AndroidMediaPlayerFactory.create();
        }
        return playerFactory;
    }

    /**
     * 根据渲染类型获取渲染视图工厂
     * @param renderType 渲染类型
     * @return 渲染视图工厂实例
     */
    public static RenderViewFactory getRenderViewFactory(int renderType) {
        RenderViewFactory renderViewFactory;
        switch (renderType) {
            case 0:
            default:
                renderViewFactory = TextureRenderViewFactory.create();
                break;
            case 1:
                renderViewFactory = SurfaceRenderViewFactory.create();
                break;
        }
        return renderViewFactory;
    }
}
