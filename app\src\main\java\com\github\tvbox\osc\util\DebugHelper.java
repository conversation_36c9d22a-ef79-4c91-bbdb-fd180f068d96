package com.github.tvbox.osc.util;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.github.tvbox.osc.R;

import java.lang.ref.WeakReference;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 调试辅助类，用于查看点击事件是否正常工作
 */
public class DebugHelper {
    private static final String TAG = "DebugHelper";
    private static Handler mainHandler = new Handler(Looper.getMainLooper());
    
    // 自定义Toast视图
    private static WeakReference<View> customToastView = null;
    private static WeakReference<TextView> customToastText = null;
    private static WeakReference<Context> lastContext = null;
    private static WindowManager windowManager = null;
    private static AtomicBoolean isToastShowing = new AtomicBoolean(false);
    
    /**
     * 显示调试信息，不产生堆栈跟踪
     * @param context 上下文
     * @param message 消息
     */
    public static void showToast(final Context context, final String message) {
        if (context == null) {
            Log.e(TAG, "Context is null, cannot show toast: " + message);
            return;
        }
        
        try {
            // 确保在UI线程中执行
            if (Looper.myLooper() == Looper.getMainLooper()) {
                showCustomToast(context, message, 2000); // 显示2秒
            } else {
                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        showCustomToast(context, message, 2000);
                    }
                });
            }
            // 只记录日志，不产生堆栈
            Log.d(TAG, "显示消息: " + message);
        } catch (Exception e) {
            Log.e(TAG, "显示Toast出错: " + e.getMessage());
        }
    }
    
    /**
     * 显示完全自定义的Toast消息，不使用系统Toast
     */
    private static void showCustomToast(Context context, String message, int duration) {
        try {
            // 如果正在显示，先取消之前的
            if (isToastShowing.get()) {
                hideCustomToast();
            }
            
            // 标记为正在显示
            isToastShowing.set(true);
            
            // 检查是否是Activity上下文
            boolean isActivity = context instanceof Activity;
            
            // 创建或复用视图
            if (customToastView == null || customToastView.get() == null) {
                // 创建自定义视图
                View toastLayout = LayoutInflater.from(context).inflate(R.layout.toast_custom, null);
                TextView textView = toastLayout.findViewById(R.id.toast_text);
                
                customToastView = new WeakReference<>(toastLayout);
                customToastText = new WeakReference<>(textView);
                
                // 设置透明度为0，以便动画效果
                toastLayout.setAlpha(0f);
            }
            
            // 设置消息文本
            if (customToastText != null && customToastText.get() != null) {
                customToastText.get().setText(message);
            }
            
            final View toastView = customToastView.get();
            if (toastView == null) {
                isToastShowing.set(false);
                return;
            }
            
            // 如果是Activity上下文，尝试添加到Activity的装饰视图
            if (isActivity) {
                Activity activity = (Activity) context;
                try {
                    // 获取Activity的根视图
                    ViewGroup rootView = activity.getWindow().getDecorView().findViewById(android.R.id.content);
                    if (rootView != null) {
                        // 设置视图参数
                        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                                ViewGroup.LayoutParams.WRAP_CONTENT,
                                ViewGroup.LayoutParams.WRAP_CONTENT
                        );
                        params.gravity = Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL;
                        params.bottomMargin = 150; // 距离底部的距离
                        
                        // 添加视图到Activity根视图
                        if (toastView.getParent() == null) {
                            rootView.addView(toastView, params);
                            
                            // 淡入动画
                            ObjectAnimator fadeInAnim = ObjectAnimator.ofFloat(toastView, "alpha", 0f, 1f);
                            fadeInAnim.setDuration(200);
                            fadeInAnim.start();
                            
                            // 设置定时器，在指定时间后移除视图
                            mainHandler.postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    // 淡出动画
                                    ObjectAnimator fadeOutAnim = ObjectAnimator.ofFloat(toastView, "alpha", 1f, 0f);
                                    fadeOutAnim.setDuration(200);
                                    fadeOutAnim.addListener(new AnimatorListenerAdapter() {
                                        @Override
                                        public void onAnimationEnd(Animator animation) {
                                            if (toastView.getParent() != null) {
                                                ((ViewGroup) toastView.getParent()).removeView(toastView);
                                            }
                                            isToastShowing.set(false);
                                        }
                                    });
                                    fadeOutAnim.start();
                                }
                            }, duration);
                            return;
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, "添加到Activity视图失败: " + e.getMessage());
                    // 继续尝试WindowManager方式
                }
            }
            
            // 如果Activity方式失败或不是Activity，使用WindowManager
            if (windowManager == null || (lastContext != null && lastContext.get() != context)) {
                windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
                lastContext = new WeakReference<>(context);
            }
            
            // 配置WindowManager参数
            WindowManager.LayoutParams params = new WindowManager.LayoutParams();
            params.height = WindowManager.LayoutParams.WRAP_CONTENT;
            params.width = WindowManager.LayoutParams.WRAP_CONTENT;
            params.format = PixelFormat.TRANSLUCENT;
            params.windowAnimations = android.R.style.Animation_Toast;
            params.gravity = Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL;
            params.y = 150; // 距离底部的距离
            
            // 根据Android版本设置不同的参数
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                params.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                params.type = WindowManager.LayoutParams.TYPE_TOAST;
            } else {
                params.type = WindowManager.LayoutParams.TYPE_PHONE;
            }
            
            // 设置旗标
            params.flags = WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
                    | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                    | WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE;
            
            if (toastView.getParent() == null) {
                try {
                    // 添加视图到窗口
                    windowManager.addView(toastView, params);
                    
                    // 淡入动画
                    ObjectAnimator fadeInAnim = ObjectAnimator.ofFloat(toastView, "alpha", 0f, 1f);
                    fadeInAnim.setDuration(200);
                    fadeInAnim.start();
                    
                    // 设置定时器，在指定时间后移除视图
                    mainHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            // 淡出动画
                            ObjectAnimator fadeOutAnim = ObjectAnimator.ofFloat(toastView, "alpha", 1f, 0f);
                            fadeOutAnim.setDuration(200);
                            fadeOutAnim.addListener(new AnimatorListenerAdapter() {
                                @Override
                                public void onAnimationEnd(Animator animation) {
                                    hideCustomToast();
                                }
                            });
                            fadeOutAnim.start();
                        }
                    }, duration);
                } catch (Exception e) {
                    Log.e(TAG, "无法添加自定义Toast视图: " + e.getMessage());
                    isToastShowing.set(false);
                    
                    // 最后的备选方案，使用Activity的Handler显示简单文本
                    if (isActivity) {
                        final Activity activity = (Activity) context;
                        activity.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                try {
                                    // 在Activity底部显示简单文本
                                    TextView textView = new TextView(activity);
                                    textView.setText(message);
                                    textView.setTextColor(Color.WHITE);
                                    textView.setBackgroundResource(R.drawable.toast_bg);
                                    textView.setPadding(30, 15, 30, 15);
                                    
                                    FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(
                                            ViewGroup.LayoutParams.WRAP_CONTENT,
                                            ViewGroup.LayoutParams.WRAP_CONTENT
                                    );
                                    lp.gravity = Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL;
                                    lp.bottomMargin = 150;
                                    
                                    ViewGroup rootView = activity.findViewById(android.R.id.content);
                                    rootView.addView(textView, lp);
                                    
                                    // 2秒后移除
                                    mainHandler.postDelayed(new Runnable() {
                                        @Override
                                        public void run() {
                                            rootView.removeView(textView);
                                        }
                                    }, 2000);
                                } catch (Exception e2) {
                                    Log.e(TAG, "简单文本显示失败: " + e2.getMessage());
                                }
                            }
                        });
                    }
                }
            } else {
                isToastShowing.set(false);
            }
        } catch (Exception e) {
            Log.e(TAG, "显示自定义Toast出错: " + e.getMessage());
            isToastShowing.set(false);
        }
    }
    
    /**
     * 隐藏自定义Toast视图
     */
    private static void hideCustomToast() {
        if (windowManager != null && customToastView != null && customToastView.get() != null && customToastView.get().getParent() != null) {
            try {
                windowManager.removeView(customToastView.get());
            } catch (Exception e) {
                Log.e(TAG, "移除自定义Toast视图出错: " + e.getMessage());
            } finally {
                isToastShowing.set(false);
            }
        }
    }
    
    /**
     * 记录调试日志
     * @param message 消息
     */
    public static void logDebug(String message) {
        Log.d(TAG, message);
    }
    
    /**
     * 记录错误日志
     * @param message 消息
     * @param e 异常
     */
    public static void logError(String message, Exception e) {
        Log.e(TAG, message + (e != null ? ": " + e.getMessage() : ""));
        if (e != null) {
            e.printStackTrace();
        }
    }
} 