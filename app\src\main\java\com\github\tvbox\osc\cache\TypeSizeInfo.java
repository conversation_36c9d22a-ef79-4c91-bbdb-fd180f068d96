package com.github.tvbox.osc.cache;

/**
 * 缓存类型大小信息类
 * 用于按类型查询缓存大小统计
 */
public class TypeSizeInfo {
    // 缓存类型
    public int type;
    
    // 总大小
    public long totalSize;
    
    public TypeSizeInfo(int type, long totalSize) {
        this.type = type;
        this.totalSize = totalSize;
    }
    
    @Override
    public String toString() {
        return "TypeSizeInfo{" +
                "type=" + type +
                ", totalSize=" + totalSize +
                '}';
    }
} 