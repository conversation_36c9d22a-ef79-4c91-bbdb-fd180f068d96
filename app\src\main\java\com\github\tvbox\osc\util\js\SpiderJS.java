package com.github.tvbox.osc.util.js;

import android.content.Context;
import android.util.Log;

import com.github.catvod.crawler.Spider;
import com.github.tvbox.quickjs.JSArray;
import com.github.tvbox.quickjs.JSModule;
import com.github.tvbox.quickjs.JSObject;
import com.github.tvbox.quickjs.QuickJSContext;

import java.util.HashMap;
import java.util.List;
import java.util.UUID;

public class SpiderJS extends Spider {

    private String key;
    private String js;
    private String ext;
    private JSObject jsObject = null;
    private JSEngine.JSThread jsThread = null;

    public SpiderJS(String key, String js, String ext) {
        this.key = key;
        this.js = js;
        this.ext = ext;
    }

    void checkLoaderJS() {
        if (jsThread == null) {
            jsThread = JSEngine.getInstance().getJSThread();
        }
        if (jsObject == null && jsThread != null) {
            try {
                jsThread.postVoid((ctx, globalThis) -> {
                    try {
                        String moduleKey = "__" + UUID.randomUUID().toString().replace("-", "") + "__";
                        Log.d("SpiderJS", "Loading JS module: " + js);
                        String jsContent = JSEngine.getInstance().loadModule(js);
                        
                        // 检查脚本内容是否有效
                        if (jsContent == null || jsContent.isEmpty()) {
                            Log.e("SpiderJS", "Failed to load JS content for " + js);
                            // 如果无法加载脚本，可以尝试使用默认实现
                            jsObject = createDefaultJsObject(ctx, moduleKey);
                            return null;
                        }
                        
                        try {
                            if (js.contains(".js?")) {
                                int spIdx = js.indexOf(".js?");
                                String[] query = js.substring(spIdx + 4).split("&|=");
                                js = js.substring(0, spIdx);
                                for (int i = 0; i < query.length; i += 2) {
                                    if (i + 1 < query.length) { // 确保有足够的键值对
                                        String key = query[i];
                                        String val = query[i + 1];
                                        String sub = JSModule.convertModuleName(js, val);
                                        String content = JSEngine.getInstance().loadModule(sub);
                                        if (content != null && !content.isEmpty()) {
                                            jsContent = jsContent.replace("__" + key.toUpperCase() + "__", content);
                                        }
                                    }
                                }
                            }
                        } catch (Exception e) {
                            Log.e("SpiderJS", "Error parsing JS query: " + e.getMessage(), e);
                        }
                        
                        // 替换导出语句
                        if(jsContent.contains("export default{")){
                            jsContent = jsContent.replace("export default{", "globalThis." + moduleKey+" ={");
                        }else if(jsContent.contains("export default {")){
                            jsContent = jsContent.replace("export default {", "globalThis." + moduleKey+" ={");
                        }else {
                            jsContent = jsContent.replace("__JS_SPIDER__", "globalThis." + moduleKey);
                        }
                        
                        // 评估模块
                        try {
                            ctx.evaluateModule(jsContent, js);
                        } catch (Exception e) {
                            Log.e("SpiderJS", "Error evaluating module: " + e.getMessage(), e);
                            jsObject = createDefaultJsObject(ctx, moduleKey);
                            return null;
                        }
                        
                        // 获取JS对象并调用init
                        jsObject = (JSObject) ctx.getProperty(globalThis, moduleKey);
                        
                        if (jsObject != null) {
                            try {
                                // 修改: 使用getProperty方法检查属性是否存在，而不是contains
                                Object initFunc = jsObject.getProperty("init");
                                if (initFunc != null) {
                                    jsObject.getJSFunction("init").call(ext);
                                } else {
                                    Log.w("SpiderJS", "Init method not found in JS module");
                                }
                            } catch (Exception e) {
                                Log.e("SpiderJS", "Error calling init: " + e.getMessage(), e);
                            }
                        } else {
                            Log.e("SpiderJS", "JS module object is null, moduleKey=" + moduleKey);
                            jsObject = createDefaultJsObject(ctx, moduleKey);
                        }
                    } catch (Exception e) {
                        Log.e("SpiderJS", "General error: " + e.getMessage(), e);
                    }
                    return null;
                });
            } catch (Throwable throwable) {
                Log.e("SpiderJS", "Thread error: " + throwable.getMessage(), throwable);
            }
        }
    }

    // 创建默认的JS对象，当无法加载模块时使用
    private JSObject createDefaultJsObject(QuickJSContext ctx, String moduleKey) {
        try {
            Log.d("SpiderJS", "Creating default JS object for " + key);
            ctx.evaluate("globalThis." + moduleKey + " = {" +
                    "init: function(ext) { }," +
                    "home: function(filter) { return '{\"class\": []}'; }," +
                    "homeVod: function() { return '{\"list\": []}'; }," +
                    "category: function(tid, pg, filter, extend) { return '{\"list\": []}'; }," +
                    "detail: function(ids) { return '{\"list\": []}'; }," +
                    "play: function(flag, id, flags) { return '{\"parse\": 0, \"url\": \"\"}'; }," +
                    "search: function(key) { return '{\"list\": []}'; }" +
                    "};");
            return (JSObject) ctx.getProperty(ctx.getGlobalObject(), moduleKey);
        } catch (Exception e) {
            Log.e("SpiderJS", "Error creating default JS object: " + e.getMessage(), e);
            return null;
        }
    }

    String postFunc(String func, Object... args) {
        checkLoaderJS();
        if (jsObject != null) {
            try {
                // 修改: 使用getProperty方法检查属性是否存在，而不是contains
                Object funcObj = jsObject.getProperty(func);
                if (funcObj != null) {
                    return jsThread.post((ctx, globalThis) -> {
                        try {
                            return (String) jsObject.getJSFunction(func).call(args);
                        } catch (Exception e) {
                            Log.e("SpiderJS", "Error calling function " + func + ": " + e.getMessage(), e);
                            // 返回空结果
                            switch (func) {
                                case "home":
                                    return "{\"class\": []}";
                                case "homeVod":
                                case "category":
                                case "search":
                                    return "{\"list\": []}";
                                case "detail":
                                    return "{\"list\": []}";
                                case "play":
                                    return "{\"parse\": 0, \"url\": \"\"}";
                                default:
                                    return "";
                            }
                        }
                    });
                } else {
                    Log.w("SpiderJS", "Function " + func + " not found in JS module");
                    // 返回默认结果
                    return getDefaultResult(func);
                }
            } catch (Throwable throwable) {
                Log.e("SpiderJS", "Error in postFunc " + func + ": " + throwable.getMessage(), throwable);
                return getDefaultResult(func);
            }
        } else {
            Log.e("SpiderJS", "JS module object is null when calling " + func);
            return getDefaultResult(func);
        }
    }

    // 获取默认结果
    private String getDefaultResult(String func) {
        switch (func) {
            case "home":
                return "{\"class\": []}";
            case "homeVod":
            case "category":
            case "search":
                return "{\"list\": []}";
            case "detail":
                return "{\"list\": []}";
            case "play":
                return "{\"parse\": 0, \"url\": \"\"}";
            default:
                return "";
        }
    }

    @Override
    public void init(Context context, String extend) {
        super.init(context, extend);
        checkLoaderJS();
    }

    @Override
    public String homeContent(boolean filter) {
        return postFunc("home", filter);
    }

    @Override
    public String homeVideoContent() {
        return postFunc("homeVod");
    }

    @Override
    public String categoryContent(String tid, String pg, boolean filter, HashMap<String, String> extend) {
        try {
            JSObject obj = jsThread.post((ctx, globalThis) -> {
                JSObject o = ctx.createNewJSObject();
                if (extend != null) {
                    for (String s : extend.keySet()) {
                        o.setProperty(s, extend.get(s));
                    }
                }
                return o;
            });
            return postFunc("category", tid, pg, filter, obj);
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return "";

    }

    @Override
    public String detailContent(List<String> ids) {
        return postFunc("detail", ids.get(0));
    }

    @Override
    public String playerContent(String flag, String id, List<String> vipFlags) {
        try {
            JSArray array = jsThread.post((ctx, globalThis) -> {
                JSArray arr = ctx.createNewJSArray();
                if (vipFlags != null) {
                    for (int i = 0; i < vipFlags.size(); i++) {
                        arr.set(vipFlags.get(i), i);
                    }
                }
                return arr;
            });
            return postFunc("play", flag, id, array);
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return "";
    }

    @Override
    public String searchContent(String key, boolean quick) {
        return postFunc("search", key, quick);
    }
}
