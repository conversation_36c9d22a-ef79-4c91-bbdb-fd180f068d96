package com.github.tvbox.osc.base;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.res.Resources;
import android.os.Bundle;
import android.os.Build;
import android.util.Log;

import androidx.multidex.MultiDexApplication;

//import com.baidu.mobstat.StatService;
import com.github.tvbox.osc.bean.VodInfo;
import com.github.tvbox.osc.cache.CacheHelper;
import com.github.tvbox.osc.callback.EmptyCallback;
import com.github.tvbox.osc.callback.LoadingCallback;
import com.github.tvbox.osc.data.AppDataManager;
import com.github.tvbox.osc.server.ControlManager;
import com.github.tvbox.osc.util.FileUtils;
import com.github.tvbox.osc.util.AppManager;
import com.github.tvbox.osc.util.EpgUtil;
import com.github.tvbox.osc.util.HawkConfig;
import com.github.tvbox.osc.util.LOG;
import com.github.tvbox.osc.util.OkGoHelper;
import com.github.tvbox.osc.util.PlayerHelper;
import com.github.tvbox.osc.util.js.JSEngine;
import com.kingja.loadsir.core.LoadSir;
import com.orhanobut.hawk.Hawk;
import com.p2p.P2PClass;
import com.tencent.mmkv.MMKV;
import com.github.tvbox.osc.util.LogUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.ref.WeakReference;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import me.jessyan.autosize.AutoSizeConfig;
import me.jessyan.autosize.unit.Subunits;

/**
 * <AUTHOR>
 * @date :2020/12/17
 * @description:
 */
public class App extends MultiDexApplication {
    private static App instance;
    private static P2PClass p;
    public static String burl;
    public static Activity sActivity;

    @Override
    public void onCreate() {
        super.onCreate();
        instance = this;
        
        // 设置全局异常处理器
        setupCrashHandler();
        
        try {
            // 基础初始化
            initParams();
            OkGoHelper.init();
            EpgUtil.init();//获取EPG
            MMKV.initialize(this);
            
            // 数据库初始化必须先于缓存
            AppDataManager.init();
            
            // 控制管理器初始化
            ControlManager.init(this);
            
            // 缓存系统初始化 (放在数据库初始化之后)
            try {
                CacheHelper.init();
            } catch (Exception e) {
                LOG.e("缓存系统初始化失败: " + e.getMessage());
            }
            
            // UI初始化
            LoadSir.beginBuilder()
                    .addCallback(new EmptyCallback())
                    .addCallback(new LoadingCallback())
                    .commit();
            
            // 自动适配初始化
            AutoSizeConfig.getInstance().setCustomFragment(true).getUnitsManager()
                    .setSupportDP(false)
                    .setSupportSP(false)
                    .setSupportSubunits(Subunits.MM);
            
            // 播放器初始化
            PlayerHelper.init();
            
            // JS引擎初始化
            JSEngine.getInstance().create();
            
            // 清理播放器缓存
            FileUtils.cleanPlayerCache();

            //线路选择
            this.registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
                @Override
                public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
                    Log.d("YWK",activity+"onActivityCreated");
                }

                @Override
                public void onActivityStarted(Activity activity) {
                    Log.d("YWK",activity+"onActivityStarted");
                    sActivity=activity;

                }

                @Override
                public void onActivityResumed(Activity activity) {

                }

                @Override
                public void onActivityPaused(Activity activity) {

                }

                @Override
                public void onActivityStopped(Activity activity) {

                }

                @Override
                public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

                }

                @Override
                public void onActivityDestroyed(Activity activity) {

                }
            });
        } catch (Exception e) {
            LOG.e("应用初始化失败: " + e.getMessage());
        }
    }

    /**
     * 设置全局异常处理器，用于捕获和记录崩溃日志
     */
    private void setupCrashHandler() {
        final Thread.UncaughtExceptionHandler defaultHandler = Thread.getDefaultUncaughtExceptionHandler();
        
        Thread.setDefaultUncaughtExceptionHandler(new Thread.UncaughtExceptionHandler() {
            @Override
            public void uncaughtException(Thread thread, Throwable ex) {
                try {
                    // 记录崩溃日志到文件
                    saveCrashLog(ex);
                    
                    // 延迟一秒让日志写入完成
                    Thread.sleep(1000);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                
                // 调用默认的异常处理器
                if (defaultHandler != null) {
                    defaultHandler.uncaughtException(thread, ex);
                }
            }
        });
    }
    
    /**
     * 保存崩溃日志到文件
     * @param ex 异常对象
     */
    private void saveCrashLog(Throwable ex) {
        try {
            // 获取崩溃日志目录
            File logDir = new File(getExternalFilesDir(null), "crash_logs");
            if (!logDir.exists()) {
                logDir.mkdirs();
            }
            
            // 生成日志文件名
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault());
            String fileName = "crash_" + sdf.format(new Date()) + ".log";
            File logFile = new File(logDir, fileName);
            
            // 写入日志
            FileOutputStream fos = new FileOutputStream(logFile);
            
            // 写入设备和应用信息
            StringBuilder sb = new StringBuilder();
            sb.append("设备信息：\n");
            sb.append("品牌：").append(Build.BRAND).append("\n");
            sb.append("型号：").append(Build.MODEL).append("\n");
            sb.append("安卓版本：").append(Build.VERSION.RELEASE).append("\n");
            sb.append("API级别：").append(Build.VERSION.SDK_INT).append("\n");
            sb.append("\n异常信息：\n");
            
            // 获取完整的异常堆栈
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            ex.printStackTrace(pw);
            sb.append(sw.toString());
            
            fos.write(sb.toString().getBytes());
            fos.close();
            
            LogUtils.e("CrashHandler", "已保存崩溃日志到：" + logFile.getAbsolutePath());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static Context getAppContext() {
        return instance;
    }

    public static Resources getAppResources() {
        return instance.getResources();
    }

    public static Activity getActivity(){
        return sActivity;
    }

/*    private void baidu(){
        StatService.setAuthorizedState(this, true);
        StatService.setAppKey(HawkConfig.SEN_SUS);
        StatService.setAppChannel(this, HawkConfig.APP_Channel, true);
        StatService.setForTv(this, true);
        StatService.start(this);
    }*/

    private void initParams() {
        // Hawk
        Hawk.init(this).build();
        Hawk.put(HawkConfig.DEBUG_OPEN, false);
        if (!Hawk.contains(HawkConfig.PLAY_TYPE)) {
            Hawk.put(HawkConfig.PLAY_TYPE, 1);
        }
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        JSEngine.getInstance().destroy();
        
        // 停止缓存维护
        CacheHelper.stopMaintenance();
    }

    public static P2PClass getp2p() {
        try {
            if (p == null) {
                p = new P2PClass(instance.getExternalCacheDir().getAbsolutePath());
            }
            return p;
        } catch (Exception e) {
            LOG.e(e.toString());
            return null;
        }
    }

    public static App getInstance() {
        return instance;
    }
    private VodInfo vodInfo;
    public void setVodInfo(VodInfo vodinfo){
        this.vodInfo = vodinfo;
    }
    public VodInfo getVodInfo(){
        return this.vodInfo;
    }

    public Activity getCurrentActivity() {
        return AppManager.getInstance().currentActivity();
    }
}