package com.github.tvbox.osc.cache;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.github.tvbox.osc.data.AppDataManager;
import com.github.tvbox.osc.base.App;
import com.github.tvbox.osc.util.FileUtils;
import com.github.tvbox.osc.util.HawkConfig;
import com.github.tvbox.osc.util.LogUtils;
import com.orhanobut.hawk.Hawk;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ConcurrentHashMap;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import java.io.File;
import java.io.StringWriter;
import java.io.PrintWriter;

/**
 * 高效缓存管理器
 * 提供了更丰富的缓存操作和管理功能，包括：
 * 1. 缓存过期控制
 * 2. 基于LRU策略的缓存淘汰
 * 3. 缓存大小限制和监控
 * 4. 缓存压缩
 * 5. 异步操作
 */
public class CacheManager {
    private static final String TAG = "CacheManager";
    
    // 默认缓存过期时间：24小时
    private static final long DEFAULT_EXPIRE_TIME = 24 * 60 * 60 * 1000;
    
    // 缓存总大小限制：50MB
    public static final long MAX_CACHE_SIZE = 2048 * 1024 * 1024;
    
    // 缓存清理阈值：当缓存大小超过此值时触发清理
    private static final long CACHE_CLEAN_THRESHOLD = (long)(MAX_CACHE_SIZE * 0.8);
    
    // 单次清理的缓存项数量
    private static final int CLEAN_BATCH_SIZE = 20;
    
    // 缓存类型常量
    public static final int CACHE_TYPE_COMMON = 0;       // 通用缓存
    public static final int CACHE_TYPE_API = 1;          // API数据缓存
    public static final int CACHE_TYPE_IMAGE = 2;        // 图片缓存
    public static final int CACHE_TYPE_VIDEO = 3;        // 视频信息缓存
    
    // 是否开启缓存压缩
    private static boolean enableCompression = true;
    
    // 后台线程池，用于异步操作
    private static final Executor backgroundExecutor = Executors.newFixedThreadPool(2);
    
    // 主线程Handler，用于回调
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    private static CacheManager instance;
    private final ExecutorService executorService;
    private final Context context;
    
    private CacheManager(Context context) {
        this.context = context.getApplicationContext();
        this.executorService = Executors.newSingleThreadExecutor();
    }
    
    public static CacheManager getInstance() {
        if (instance == null) {
            synchronized (CacheManager.class) {
                if (instance == null) {
                    instance = new CacheManager(App.getInstance());
                }
            }
        }
        return instance;
    }
    
    /**
     * 获取缓存目录
     */
    public File getCacheDir() {
        File cacheDir;
        if (android.os.Environment.MEDIA_MOUNTED.equals(android.os.Environment.getExternalStorageState())) {
            cacheDir = new File(context.getExternalCacheDir(), "cache");
        } else {
            cacheDir = new File(context.getCacheDir(), "cache");
        }
        if (!cacheDir.exists()) {
            cacheDir.mkdirs();
        }
        return cacheDir;
    }
    
    /**
     * 获取配置的最大缓存大小
     * 默认为50MB，可通过设置修改
     */
    public static long getMaxCacheSize() {
        try {
            // 修正Integer到Long的转换问题，同时兼容MB和字节两种单位
            Object maxSizeObj = Hawk.get(HawkConfig.CACHE_MAX_SIZE, MAX_CACHE_SIZE);
            long maxSize;
            
            if (maxSizeObj instanceof Integer) {
                // 如果是Integer，显式转换为long
                maxSize = ((Integer)maxSizeObj).longValue();
            } else if (maxSizeObj instanceof Long) {
                // 如果已经是Long，直接使用
                maxSize = (Long)maxSizeObj;
            } else {
                // 其他类型，使用默认值
                maxSize = MAX_CACHE_SIZE;
            }
            
            // 兼容性检查：如果值小于10MB，认为是以MB为单位，需要转换为字节
            if (maxSize > 0 && maxSize < 10 * 1024 * 1024) {
                maxSize = maxSize * 1024 * 1024;
            }
            
            return maxSize;
        } catch (Exception e) {
            Log.e(TAG, "获取最大缓存大小出错", e);
            return MAX_CACHE_SIZE; // 出错返回默认值
        }
    }
    
    /**
     * 获取缓存清理阈值
     * 默认为最大缓存大小的80%
     */
    public static long getCacheCleanThreshold() {
        return (long)(getMaxCacheSize() * 0.8);
    }
    
    /**
     * 反序列化，把二进制数据转换成java对象
     */
    private static Object toObject(byte[] data) {
        if (data == null || data.length == 0) {
            return null;
        }
        
        ByteArrayInputStream bais = null;
        ObjectInputStream ois = null;
        GZIPInputStream gis = null;
        
        try {
            bais = new ByteArrayInputStream(data);
            
            // 如果是压缩数据，先解压
            if (enableCompression && isCompressed(data)) {
                gis = new GZIPInputStream(bais);
                ois = new ObjectInputStream(gis);
            } else {
                ois = new ObjectInputStream(bais);
            }
            
            return ois.readObject();
        } catch (Exception e) {
            Log.e(TAG, "反序列化数据失败", e);
        } finally {
            try {
                if (ois != null) ois.close();
                if (gis != null) gis.close();
                if (bais != null) bais.close();
            } catch (Exception ignore) {
                Log.e(TAG, "关闭流失败", ignore);
            }
        }
        return null;
    }

    /**
     * 序列化存储数据，转换成二进制
     * 支持压缩
     */
    private static <T> byte[] toByteArray(T body) {
        if (body == null) {
            return new byte[0];
        }
        
        ByteArrayOutputStream baos = null;
        ObjectOutputStream oos = null;
        GZIPOutputStream gos = null;
        
        try {
            baos = new ByteArrayOutputStream();
            
            // 如果启用压缩，使用GZIP压缩
            if (enableCompression && shouldCompress(body)) {
                gos = new GZIPOutputStream(baos);
                oos = new ObjectOutputStream(gos);
            } else {
                oos = new ObjectOutputStream(baos);
            }
            
            oos.writeObject(body);
            oos.flush();
            
            if (gos != null) {
                gos.finish();
            }
            
            return baos.toByteArray();
        } catch (Exception e) {
            Log.e(TAG, "序列化数据失败", e);
        } finally {
            try {
                if (oos != null) oos.close();
                if (gos != null) gos.close();
                if (baos != null) baos.close();
            } catch (Exception e) {
                Log.e(TAG, "关闭流失败", e);
            }
        }
        return new byte[0];
    }
    
    /**
     * 判断数据是否应该压缩
     * 小数据不压缩，大数据才压缩
     */
    private static boolean shouldCompress(Object obj) {
        // 如果对象是字符串且长度小于1KB，不压缩
        if (obj instanceof String && ((String) obj).length() < 1024) {
            return false;
        }
        return true;
    }
    
    /**
     * 判断数据是否已经压缩
     */
    private static boolean isCompressed(byte[] data) {
        if (data == null || data.length < 2) {
            return false;
        }
        // GZIP文件头标识：0x1F 0x8B
        return (data[0] == (byte) 0x1F && data[1] == (byte) 0x8B);
    }
    
    /**
     * 设置是否启用压缩
     */
    public static void setEnableCompression(boolean enable) {
        enableCompression = enable;
    }

    /**
     * 删除缓存
     */
    public static <T> void delete(String key, T body) {
        if (TextUtils.isEmpty(key)) {
            return;
        }
        
        try {
            Cache cache = new Cache();
            cache.key = key;
            
            AppDataManager.get().getCacheDao().delete(cache);
            Log.d(TAG, "删除缓存: " + key);
        } catch (Exception e) {
            Log.e(TAG, "删除缓存失败: " + key, e);
        }
    }
    
    /**
     * 异步删除缓存
     */
    public static <T> void deleteAsync(final String key, final T body) {
        if (TextUtils.isEmpty(key)) {
            return;
        }
        
        backgroundExecutor.execute(() -> delete(key, body));
    }

    /**
     * 保存缓存，使用默认过期时间
     */
    public static <T> void save(String key, T body) {
        save(key, body, DEFAULT_EXPIRE_TIME);
    }
    
    /**
     * 保存缓存，指定过期时间
     */
    public static <T> void save(String key, T body, long expireTime) {
        save(key, body, expireTime, CACHE_TYPE_COMMON);
    }
    
    /**
     * 保存缓存，指定过期时间和缓存类型
     */
    public static <T> void save(String key, T body, long expireTime, int type) {
        if (TextUtils.isEmpty(key) || body == null) {
            return;
        }
        
        try {
            byte[] data = toByteArray(body);
            Cache cache = new Cache(key, data);
            cache.setExpireTime(expireTime);
            cache.type = type;
            
            AppDataManager.get().getCacheDao().save(cache);
            Log.d(TAG, "保存缓存: " + key + ", 大小: " + cache.size + "字节");
            
            // 检查缓存大小，如果超过阈值则清理
            checkCacheSizeAndClean();
        } catch (Exception e) {
            Log.e(TAG, "保存缓存失败: " + key, e);
        }
    }
    
    /**
     * 异步保存缓存
     */
    public static <T> void saveAsync(final String key, final T body) {
        saveAsync(key, body, DEFAULT_EXPIRE_TIME);
    }
    
    /**
     * 异步保存缓存，指定过期时间
     */
    public static <T> void saveAsync(final String key, final T body, final long expireTime) {
        saveAsync(key, body, expireTime, CACHE_TYPE_COMMON);
    }
    
    /**
     * 异步保存缓存，指定过期时间和缓存类型
     */
    public static <T> void saveAsync(final String key, final T body, final long expireTime, final int type) {
        if (TextUtils.isEmpty(key) || body == null) {
            return;
        }
        
        backgroundExecutor.execute(() -> save(key, body, expireTime, type));
    }

    /**
     * 获取缓存
     */
    public static Object getCache(String key) {
        if (TextUtils.isEmpty(key)) {
            return null;
        }
        
        try {
            Cache cache = AppDataManager.get().getCacheDao().getCache(key);
            if (cache == null || cache.data == null) {
                return null;
            }
            
            // 检查缓存是否过期
            if (cache.isExpired()) {
                delete(key, null);
                return null;
            }
            
            // 更新访问时间和计数
            cache.updateAccessTime();
            cache.accessCount++;
            AppDataManager.get().getCacheDao().update(cache);
            
            return toObject(cache.data);
        } catch (Exception e) {
            Log.e(TAG, "获取缓存失败: " + key, e);
            return null;
        }
    }
    
    /**
     * 异步获取缓存
     */
    public static <T> void getCacheAsync(final String key, final CacheCallback<T> callback) {
        if (TextUtils.isEmpty(key)) {
            if (callback != null) {
                mainHandler.post(() -> callback.onGetCache(null));
            }
            return;
        }
        
        backgroundExecutor.execute(() -> {
            final Object obj = getCache(key);
            if (callback != null) {
                mainHandler.post(() -> {
                    try {
                        callback.onGetCache((T) obj);
                    } catch (ClassCastException e) {
                        Log.e(TAG, "缓存类型转换错误", e);
                        callback.onGetCache(null);
                    }
                });
            }
        });
    }
    
    /**
     * 检查缓存是否存在
     */
    public static boolean contains(String key) {
        if (TextUtils.isEmpty(key)) {
            return false;
        }
        
        Cache cache = AppDataManager.get().getCacheDao().getCache(key);
        return cache != null && !cache.isExpired();
    }
    
    /**
     * 清理过期缓存
     */
    public static void clearExpiredCache() {
        try {
            long currentTime = System.currentTimeMillis();
            int count = AppDataManager.get().getCacheDao().deleteExpiredCache(currentTime);
            Log.d(TAG, "清理过期缓存: " + count + "项");
        } catch (Exception e) {
            Log.e(TAG, "清理过期缓存失败", e);
        }
    }
    
    /**
     * 异步清理过期缓存
     */
    public static void clearExpiredCacheAsync() {
        backgroundExecutor.execute(CacheManager::clearExpiredCache);
    }
    
    /**
     * 清空所有缓存
     */
    public static void clearAllCache() {
        try {
            int count = AppDataManager.get().getCacheDao().clearAllCache();
            Log.d(TAG, "清空所有缓存: " + count + "项");
        } catch (Exception e) {
            Log.e(TAG, "清空缓存失败", e);
        }
    }
    
    /**
     * 异步清空所有缓存
     */
    public static void clearAllCacheAsync() {
        backgroundExecutor.execute(CacheManager::clearAllCache);
    }
    
    /**
     * 根据类型清理缓存
     */
    public static void clearCacheByType(int type) {
        try {
            List<Cache> caches = AppDataManager.get().getCacheDao().getCacheByType(type);
            if (caches != null && !caches.isEmpty()) {
                AppDataManager.get().getCacheDao().deleteAll(caches);
                Log.d(TAG, "清理类型" + type + "缓存: " + caches.size() + "项");
            }
        } catch (Exception e) {
            Log.e(TAG, "清理类型缓存失败", e);
        }
    }
    
    /**
     * 异步根据类型清理缓存
     */
    public static void clearCacheByTypeAsync(final int type) {
        backgroundExecutor.execute(() -> clearCacheByType(type));
    }
    
    /**
     * 根据键前缀清理缓存
     */
    public static void clearCacheByKeyPrefix(String keyPrefix) {
        if (TextUtils.isEmpty(keyPrefix)) {
            return;
        }
        
        try {
            int count = AppDataManager.get().getCacheDao().deleteByKeyPrefix(keyPrefix);
            Log.d(TAG, "清理前缀为" + keyPrefix + "的缓存: " + count + "项");
        } catch (Exception e) {
            Log.e(TAG, "清理前缀缓存失败", e);
        }
    }
    
    /**
     * 异步根据键前缀清理缓存
     */
    public static void clearCacheByKeyPrefixAsync(final String keyPrefix) {
        if (TextUtils.isEmpty(keyPrefix)) {
            return;
        }
        
        backgroundExecutor.execute(() -> clearCacheByKeyPrefix(keyPrefix));
    }
    
    /**
     * 获取缓存总大小
     */
    public static long getTotalCacheSize() {
        try {
            // 计算数据库缓存大小
            long dbSize = AppDataManager.get().getCacheDao().getTotalSize();
            
            // 计算文件缓存大小
            File cacheDir = getInstance().getCacheDir();
            long fileSize = FileUtils.getFolderSize(cacheDir);
            
            return dbSize + fileSize;
        } catch (Exception e) {
            Log.e(TAG, "获取总缓存大小失败", e);
            return 0;
        }
    }
    
    /**
     * 获取缓存项数量
     */
    public static int getCacheCount() {
        try {
            return AppDataManager.get().getCacheDao().getCacheCount();
        } catch (Exception e) {
            Log.e(TAG, "获取缓存数量失败", e);
            return 0;
        }
    }
    
    /**
     * 获取最少使用的缓存项
     * 用于LRU缓存淘汰策略
     */
    public static List<Cache> getLeastRecentUsedCache(int count) {
        try {
            // 使用安全方法只获取键和基本信息，不返回大的BLOB数据
            List<Cache> cacheKeysOnly = AppDataManager.get().getCacheDao().getLeastRecentUsedCacheKeys(count);
            
            // 为兼容现有代码，直接返回只有键的Cache对象列表
            return cacheKeysOnly;
        } catch (Exception e) {
            Log.e(TAG, "获取最少使用缓存失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取低优先级的缓存
     * @param count 获取数量
     * @return 低优先级缓存列表
     */
    public static List<Cache> getLowPriorityCache(int count) {
        try {
            // 使用安全方法只获取键和基本信息
            List<Cache> cacheKeysOnly = AppDataManager.get().getCacheDao().getLowPriorityCacheKeys(count);
            
            // 如果需要完整数据内容，可以遍历获取每个缓存项
            // List<Cache> fullCaches = new ArrayList<>();
            // for (Cache cacheKey : cacheKeysOnly) {
            //     try {
            //         Cache fullCache = AppDataManager.get().getCacheDao().getCacheByKey(cacheKey.key);
            //         if (fullCache != null) {
            //             fullCaches.add(fullCache);
            //         }
            //     } catch (Exception e) {
            //         Log.w(TAG, "获取完整缓存项失败: " + cacheKey.key);
            //     }
            // }
            // return fullCaches;
            
            // 为兼容现有代码，直接返回只有键的Cache对象列表
            return cacheKeysOnly;
        } catch (Exception e) {
            Log.e(TAG, "获取低优先级缓存失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 设置缓存优先级
     * @param key 缓存键
     * @param priority 优先级 (0-10，值越大优先级越高)
     */
    public static void setCachePriority(String key, int priority) {
        if (TextUtils.isEmpty(key)) {
            return;
        }
        
        try {
            Cache cache = AppDataManager.get().getCacheDao().getCache(key);
            if (cache != null) {
                cache.priority = Math.min(10, Math.max(0, priority));
                AppDataManager.get().getCacheDao().update(cache);
            }
        } catch (Exception e) {
            Log.e(TAG, "设置缓存优先级失败", e);
        }
    }
    
    /**
     * 增加缓存访问次数
     * @param key 缓存键
     */
    public static void incrementAccessCount(String key) {
        if (TextUtils.isEmpty(key)) {
            return;
        }
        
        try {
            Cache cache = AppDataManager.get().getCacheDao().getCache(key);
            if (cache != null) {
                cache.accessCount++;
                AppDataManager.get().getCacheDao().update(cache);
            }
        } catch (Exception e) {
            Log.e(TAG, "增加缓存访问次数失败", e);
        }
    }
    
    /**
     * 检查缓存大小并清理
     * 如果缓存总大小超过阈值，则清理低优先级的缓存
     */
    private static void checkCacheSizeAndClean() {
        try {
            backgroundExecutor.execute(() -> {
                try {
                    long totalSize = getTotalCacheSize();
                    long maxSize = getMaxCacheSize();
                    long threshold = getCacheCleanThreshold();
                    
                    if (totalSize > threshold) {
                        // 缓存大小超过阈值，需要清理
                        Log.i(TAG, "缓存大小(" + (totalSize / 1024 / 1024) + "MB)超过阈值(" + 
                              (threshold / 1024 / 1024) + "MB)，开始清理");
                        
                        // 首先查询需要删除的缓存键
                        List<Cache> lowPriorityKeysOnly = AppDataManager.get().getCacheDao().getLowPriorityCacheKeys(CLEAN_BATCH_SIZE / 2);
                        
                        if (lowPriorityKeysOnly != null && !lowPriorityKeysOnly.isEmpty()) {
                            Log.i(TAG, "找到" + lowPriorityKeysOnly.size() + "个低优先级缓存项可清理");
                            
                            // 单独处理每个缓存项
                            for (Cache cacheKey : lowPriorityKeysOnly) {
                                try {
                                    // 安全删除，忽略可能的异常
                                    Cache cache = new Cache();
                                    cache.key = cacheKey.key;
                                    AppDataManager.get().getCacheDao().delete(cache);
                                    Log.d(TAG, "已删除缓存: " + cacheKey.key + ", 大小: " + 
                                          (cacheKey.size / 1024) + "KB");
                                } catch (Exception e) {
                                    Log.w(TAG, "删除缓存项时出错: " + e.getMessage());
                                }
                            }
                            
                            // 检查是否需要继续清理
                            long newSize = getTotalCacheSize();
                            if (newSize > threshold && newSize > totalSize - 1024 * 1024) {
                                // 如果清理效果不明显（小于1MB），使用时间策略清理更多缓存
                                Log.i(TAG, "清理效果不明显，使用时间策略清理更多缓存");
                                
                                try {
                                    // 清理7天前的缓存
                                    long weekAgo = System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000;
                                    int deletedCount = AppDataManager.get().getCacheDao().deleteOldCache(weekAgo);
                                    Log.i(TAG, "已清理" + deletedCount + "个7天前的缓存项");
                                } catch (Exception e) {
                                    Log.e(TAG, "清理旧缓存时出错", e);
                                }
                            }
                        } else {
                            // 没有低优先级的缓存，尝试清理过期缓存
                            Log.i(TAG, "没有找到低优先级缓存，尝试清理过期缓存");
                            clearExpiredCache();
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, "检查缓存大小失败", e);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "启动缓存检查任务失败", e);
        }
    }
    
    /**
     * 缓存回调接口
     */
    public interface CacheCallback<T> {
        void onGetCache(T cacheObject);
    }

    /**
     * 智能缓存预加载策略
     * 根据用户观看习惯自动预加载可能会观看的内容
     */
    public static class SmartPreloader {
        // 观看习惯统计
        private static final Map<String, Integer> categoryWatchCount = new HashMap<>();
        private static final Map<String, Long> lastWatchTime = new HashMap<>();
        private static final int MAX_PRELOAD_VIDEOS = 3; // 每个类别最多预加载数量
        
        /**
         * 记录用户观看行为，用于分析习惯
         * @param vodInfo 当前观看的视频信息
         */
        public static void recordWatchBehavior(String category, String videoId) {
            // 更新类别观看计数
            Integer count = categoryWatchCount.getOrDefault(category, 0);
            categoryWatchCount.put(category, count + 1);
            
            // 更新最后观看时间
            lastWatchTime.put(category, System.currentTimeMillis());
            
            // 存储观看习惯数据到持久化存储
            saveWatchBehaviorData();
        }
        
        /**
         * 持久化存储观看习惯数据
         */
        private static void saveWatchBehaviorData() {
            try {
                // 使用Hawk保存数据
                com.orhanobut.hawk.Hawk.put("smart_cache_category_counts", categoryWatchCount);
                com.orhanobut.hawk.Hawk.put("smart_cache_last_watch_times", lastWatchTime);
            } catch (Exception e) {
                Log.e(TAG, "保存观看习惯数据失败", e);
            }
        }
        
        /**
         * 加载观看习惯数据
         */
        public static void loadWatchBehaviorData() {
            try {
                Map<String, Integer> savedCounts = com.orhanobut.hawk.Hawk.get("smart_cache_category_counts", new HashMap<>());
                Map<String, Long> savedTimes = com.orhanobut.hawk.Hawk.get("smart_cache_last_watch_times", new HashMap<>());
                
                if (savedCounts != null) {
                    categoryWatchCount.putAll(savedCounts);
                }
                
                if (savedTimes != null) {
                    lastWatchTime.putAll(savedTimes);
                }
            } catch (Exception e) {
                Log.e(TAG, "加载观看习惯数据失败", e);
            }
        }
        
        /**
         * 获取推荐预加载的视频类别
         * 按照观看频率和最近观看时间排序
         */
        public static List<String> getRecommendedCategoriesToPreload() {
            // 加载历史数据
            loadWatchBehaviorData();
            
            // 计算每个类别的得分 (观看次数 * 时间衰减因子)
            Map<String, Double> categoryScores = new HashMap<>();
            long currentTime = System.currentTimeMillis();
            
            for (String category : categoryWatchCount.keySet()) {
                int count = categoryWatchCount.get(category);
                long lastTime = lastWatchTime.getOrDefault(category, 0L);
                
                // 时间衰减因子: 最近一周内观看的权重最高
                double timeFactor = 1.0;
                long daysSinceLastWatch = (currentTime - lastTime) / (1000 * 60 * 60 * 24);
                
                if (daysSinceLastWatch <= 1) {
                    timeFactor = 1.0; // 1天内
                } else if (daysSinceLastWatch <= 3) {
                    timeFactor = 0.8; // 3天内
                } else if (daysSinceLastWatch <= 7) {
                    timeFactor = 0.6; // 一周内
                } else if (daysSinceLastWatch <= 14) {
                    timeFactor = 0.4; // 两周内
                } else {
                    timeFactor = 0.2; // 更早
                }
                
                double score = count * timeFactor;
                categoryScores.put(category, score);
            }
            
            // 按得分排序
            List<Map.Entry<String, Double>> sortedEntries = new ArrayList<>(categoryScores.entrySet());
            sortedEntries.sort((e1, e2) -> e2.getValue().compareTo(e1.getValue()));
            
            // 返回排序后的类别列表
            List<String> result = new ArrayList<>();
            for (Map.Entry<String, Double> entry : sortedEntries) {
                result.add(entry.getKey());
                if (result.size() >= 3) { // 最多返回前3个
                    break;
                }
            }
            
            return result;
        }
        
        /**
         * 智能预加载视频
         * 根据用户习惯和网络状态预加载内容
         */
        public static void smartPreloadVideos(Context context) {
            // 检查网络状态，避免在移动数据网络下预加载
            if (!isWifiConnected(context)) {
                Log.d(TAG, "非WiFi网络，跳过智能预加载");
                return;
            }
            
            // 检查空闲状态，只在设备空闲和充电时预加载
            if (!isDeviceIdle(context)) {
                Log.d(TAG, "设备非空闲状态，跳过智能预加载");
                return;
            }
            
            // 获取推荐预加载的类别
            List<String> recommendedCategories = getRecommendedCategoriesToPreload();
            
            // 使用预加载管理器执行预加载
            for (String category : recommendedCategories) {
                // 为每个类别预加载最近更新的视频
                preloadCategoryVideos(category, MAX_PRELOAD_VIDEOS);
            }
        }
        
        /**
         * 预加载指定类别的视频
         */
        private static void preloadCategoryVideos(String category, int count) {
            LogUtils.d(TAG, "开始预加载类别: " + category + "，数量: " + count);
            // 这里实现具体的预加载逻辑，可以调用API获取最近更新的视频列表
            // 然后使用预加载管理器进行预加载
            // 示例伪代码，具体实现需要与真实API对接
            // ApiClient.getLatestVideos(category, count, videos -> {
            //     for (Video video : videos) {
            //         PreloadManager.getInstance().preloadVideo(video.getId(), 1);
            //     }
            // });
        }
        
        /**
         * 检查是否连接WiFi
         */
        private static boolean isWifiConnected(Context context) {
            try {
                android.net.ConnectivityManager cm = (android.net.ConnectivityManager) 
                        context.getSystemService(Context.CONNECTIVITY_SERVICE);
                android.net.NetworkInfo networkInfo = cm.getActiveNetworkInfo();
                return networkInfo != null && networkInfo.isConnected() && 
                        networkInfo.getType() == android.net.ConnectivityManager.TYPE_WIFI;
            } catch (Exception e) {
                return false;
            }
        }
        
        /**
         * 检查设备是否处于空闲状态
         */
        private static boolean isDeviceIdle(Context context) {
            try {
                // 检查是否在充电
                android.os.BatteryManager bm = (android.os.BatteryManager) 
                        context.getSystemService(Context.BATTERY_SERVICE);
                if (bm != null) {
                    int status = bm.getIntProperty(android.os.BatteryManager.BATTERY_PROPERTY_STATUS);
                    boolean isCharging = status == android.os.BatteryManager.BATTERY_STATUS_CHARGING || 
                            status == android.os.BatteryManager.BATTERY_STATUS_FULL;
                    
                    // 只在充电时预加载
                    if (!isCharging) {
                        return false;
                    }
                }
                
                // 检查是否屏幕关闭
                android.os.PowerManager pm = (android.os.PowerManager) 
                        context.getSystemService(Context.POWER_SERVICE);
                if (pm != null) {
                    boolean isScreenOn = pm.isInteractive();
                    return !isScreenOn; // 屏幕关闭时视为空闲
                }
                
                return false;
            } catch (Exception e) {
                return false;
            }
        }
    }
    
    /**
     * 缓存优先级管理
     * 为不同类型的内容设置缓存优先级
     */
    public static class CachePriorityManager {
        // 优先级级别
        public static final int PRIORITY_LOW = 0;
        public static final int PRIORITY_NORMAL = 1;
        public static final int PRIORITY_HIGH = 2;
        public static final int PRIORITY_CRITICAL = 3;
        
        // 存储内容优先级
        private static final Map<String, Integer> contentPriorities = new ConcurrentHashMap<>();
        
        /**
         * 设置内容缓存优先级
         * @param key 缓存键
         * @param priority 优先级级别
         */
        public static void setContentPriority(String key, int priority) {
            contentPriorities.put(key, priority);
            // 持久化保存优先级
            saveContentPriorities();
        }
        
        /**
         * 获取内容优先级
         * @param key 缓存键
         * @return 优先级，默认为普通优先级
         */
        public static int getContentPriority(String key) {
            return contentPriorities.getOrDefault(key, PRIORITY_NORMAL);
        }
        
        /**
         * 持久化保存内容优先级
         */
        private static void saveContentPriorities() {
            try {
                // 只保存高优先级的内容以节省空间
                Map<String, Integer> highPriorityItems = new HashMap<>();
                for (Map.Entry<String, Integer> entry : contentPriorities.entrySet()) {
                    if (entry.getValue() > PRIORITY_NORMAL) {
                        highPriorityItems.put(entry.getKey(), entry.getValue());
                    }
                }
                com.orhanobut.hawk.Hawk.put("cache_priorities", highPriorityItems);
            } catch (Exception e) {
                Log.e(TAG, "保存缓存优先级失败", e);
            }
        }
        
        /**
         * 加载内容优先级
         */
        public static void loadContentPriorities() {
            try {
                Map<String, Integer> savedPriorities = com.orhanobut.hawk.Hawk.get("cache_priorities", new HashMap<>());
                if (savedPriorities != null) {
                    contentPriorities.putAll(savedPriorities);
                }
            } catch (Exception e) {
                Log.e(TAG, "加载缓存优先级失败", e);
            }
        }
        
        /**
         * 根据优先级选择清理的缓存项
         * 优先清理低优先级的内容
         * @param itemsToClean 需要清理的候选项列表
         * @param count 需要清理的数量
         * @return 按优先级排序后的清理列表
         */
        public static List<Cache> selectItemsToClean(List<Cache> itemsToClean, int count) {
            // 先加载优先级数据
            loadContentPriorities();
            
            // 按优先级分组
            Map<Integer, List<Cache>> priorityGroups = new HashMap<>();
            for (Cache cache : itemsToClean) {
                int priority = getContentPriority(cache.key);
                List<Cache> group = priorityGroups.getOrDefault(priority, new ArrayList<>());
                group.add(cache);
                priorityGroups.put(priority, group);
            }
            
            // 从低优先级到高优先级选择清理项
            List<Cache> result = new ArrayList<>();
            for (int priority = PRIORITY_LOW; priority <= PRIORITY_CRITICAL; priority++) {
                List<Cache> group = priorityGroups.getOrDefault(priority, new ArrayList<>());
                
                // 按时间排序，先清理最旧的
                group.sort((a, b) -> Long.compare(a.createTime, b.createTime));
                
                // 添加到结果集，直到达到要求的数量
                for (Cache cache : group) {
                    result.add(cache);
                    if (result.size() >= count) {
                        return result;
                    }
                }
            }
            
            return result;
        }
        
        /**
         * 提升经常访问内容的优先级
         * @param key 缓存键
         */
        public static void increaseAccessPriority(String key) {
            int currentPriority = getContentPriority(key);
            if (currentPriority < PRIORITY_CRITICAL) {
                setContentPriority(key, currentPriority + 1);
            }
            // 记录访问时间
            com.orhanobut.hawk.Hawk.put("cache_last_access_" + key, System.currentTimeMillis());
        }
    }
    
    /**
     * 智能缓存清理策略
     * 根据使用频率、优先级和空间需求智能清理缓存
     */
    public static class SmartCacheCleaner {
        // 缓存清理模式
        public static final int CLEAN_MODE_AUTO = 0;      // 自动清理
        public static final int CLEAN_MODE_AGGRESSIVE = 1; // 积极清理
        public static final int CLEAN_MODE_MINIMAL = 2;    // 最小清理
        
        // 上次清理时间
        private static long lastCleanTime = 0;
        
        /**
         * 智能清理缓存
         * @param mode 清理模式
         * @return 清理的缓存大小
         */
        public static long smartCleanCache(int mode) {
            // 根据清理模式确定清理阈值
            long maxCacheSize = 2L * 1024 * 1024 * 1024; // 默认最大缓存为2GB
            try {
                // 修正Integer到Long的转换问题
                Object maxSizeObj = Hawk.get(HawkConfig.CACHE_MAX_SIZE, 2L);
                long maxSizeValue;
                
                if (maxSizeObj instanceof Integer) {
                    // 如果是Integer，显式转换为long
                    maxSizeValue = ((Integer)maxSizeObj).longValue();
                } else if (maxSizeObj instanceof Long) {
                    // 如果已经是Long，直接使用
                    maxSizeValue = (Long)maxSizeObj;
                } else {
                    // 其他类型，使用默认值
                    maxSizeValue = 2L;
                }
                
                maxCacheSize = maxSizeValue * 1024 * 1024 * 1024;
                LogUtils.d(TAG, "设置最大缓存大小: " + maxSizeValue + "GB (" + maxCacheSize + "字节)");
            } catch (Exception e) {
                LogUtils.e(TAG, "获取最大缓存大小配置失败", e);
            }
            
            long totalCacheSize = AppDataManager.get().getCacheDao().getTotalSize();
            long cleanThreshold;
            
            switch (mode) {
                case CLEAN_MODE_AGGRESSIVE:
                    cleanThreshold = (long)(maxCacheSize * 0.5); // 清理到50%
                    break;
                case CLEAN_MODE_MINIMAL:
                    cleanThreshold = (long)(maxCacheSize * 0.9); // 清理到90%
                    break;
                case CLEAN_MODE_AUTO:
                default:
                    cleanThreshold = (long)(maxCacheSize * 0.8); // 默认清理到80%
                    break;
            }
            
            // 如果缓存大小未超过阈值，无需清理
            if (totalCacheSize <= cleanThreshold) {
                return 0;
            }
            
            // 计算需要清理的空间
            long needSize = totalCacheSize - cleanThreshold;
            return smartCleanCache(needSize);
        }
        
        /**
         * 智能清理缓存
         * 优先清理过期缓存，然后清理低优先级缓存，最后清理最近最少使用的缓存
         * @param needSize 需要的空间大小
         * @return 清理的空间大小
         */
        public static long smartCleanCache(long needSize) {
            LogUtils.d("CacheManager", "smartCleanCache: needSize = " + needSize);
            if (needSize <= 0) {
                return 0;
            }
            
            // 获取当前总缓存大小
            long totalSize = AppDataManager.get().getCacheDao().getTotalSize();
            LogUtils.d("CacheManager", "smartCleanCache: currentSize = " + totalSize);
            
            // 清理前的缓存大小
            long beforeSize = totalSize;
            
            // 清除过期缓存
            AppDataManager.get().getCacheDao().deleteExpiredCache(System.currentTimeMillis());
            
            // 重新获取缓存大小
            totalSize = AppDataManager.get().getCacheDao().getTotalSize();
            long freedSize = beforeSize - totalSize;
            LogUtils.d("CacheManager", "smartCleanCache: freed by expired = " + freedSize);
            
            // 如果清理的空间足够，直接返回
            if (freedSize >= needSize) {
                return freedSize;
            }
            
            // 确定清理模式，默认为自动清理
            int cleanMode = CLEAN_MODE_AUTO;
            
            // 获取缓存大小比例确定清理模式
            long maxCacheSize = getMaxCacheSize();
            double usedRatio = (double)totalSize / maxCacheSize;
            if (usedRatio > 0.9) {
                // 如果缓存使用超过90%，使用积极清理模式
                cleanMode = CLEAN_MODE_AGGRESSIVE;
            } else if (usedRatio < 0.7) {
                // 如果缓存使用低于70%，使用最小清理模式
                cleanMode = CLEAN_MODE_MINIMAL;
            }
            
            // 尝试清理低优先级缓存项
            int batchSize = cleanMode == CLEAN_MODE_AGGRESSIVE ? 50 : 20;
            Log.d(TAG, "尝试清理低优先级缓存，批次大小: " + batchSize);
            
            // 使用安全方法获取低优先级缓存键
            List<Cache> lowPriorityItems = AppDataManager.get().getCacheDao().getLowPriorityCacheKeys(batchSize);
            if (lowPriorityItems == null || lowPriorityItems.isEmpty()) {
                Log.d(TAG, "没有找到低优先级缓存项");
                return freedSize;
            }
            
            long cacheSizeBefore = getTotalCacheSize();
            for (Cache cache : lowPriorityItems) {
                try {
                    // 直接使用key删除，无需加载完整数据
                    Cache toDelete = new Cache();
                    toDelete.key = cache.key;
                    AppDataManager.get().getCacheDao().delete(toDelete);
                    Log.d(TAG, "已清理缓存: " + cache.key + ", 估计大小: " + (cache.size / 1024) + "KB");
                } catch (Exception e) {
                    Log.w(TAG, "清理缓存项时出错: " + e.getMessage());
                }
            }
            
            // 计算实际释放的空间
            long cacheSizeAfter = getTotalCacheSize();
            long cleaned = cacheSizeBefore > cacheSizeAfter ? cacheSizeBefore - cacheSizeAfter : 0;
            freedSize += cleaned;
            
            Log.i(TAG, "本次清理释放了 " + (cleaned / 1024 / 1024) + "MB 空间");
            
            // 检查是否已经清理足够空间
            if (freedSize >= needSize) {
                Log.i(TAG, "已清理足够空间: " + (freedSize / 1024 / 1024) + "MB");
                return freedSize;
            }
            
            // 如果空间不足且处于积极清理模式，尝试清理更老的缓存
            if (cleanMode == CLEAN_MODE_AGGRESSIVE && freedSize < needSize) {
                long oldCacheCleaned = cleanOldCache();
                freedSize += oldCacheCleaned;
                Log.i(TAG, "额外清理旧缓存: " + (oldCacheCleaned / 1024 / 1024) + "MB");
            }
            
            // 更新最后清理时间
            lastCleanTime = System.currentTimeMillis();
            Hawk.put("cache_last_clean_time", lastCleanTime);
            
            return freedSize;
        }
        
        /**
         * 自动清理旧缓存
         * 根据配置的天数自动清理
         * @return 清理的缓存大小
         */
        public static long cleanOldCache() {
            try {
                LogUtils.d(TAG, "开始检查是否需要自动清理旧缓存");
                boolean autoClean = com.orhanobut.hawk.Hawk.get(com.github.tvbox.osc.util.HawkConfig.CACHE_VIDEO_AUTO_CLEAN, false);
                if (!autoClean) {
                    LogUtils.d(TAG, "自动清理功能已关闭");
                    return 0;
                }
                
                // 获取配置的自动清理天数
                int autoDays = com.orhanobut.hawk.Hawk.get(com.github.tvbox.osc.util.HawkConfig.CACHE_AUTO_CLEAN_DAYS, 7);
                if (autoDays <= 0) {
                    LogUtils.d(TAG, "自动清理天数配置无效: " + autoDays);
                    return 0;
                }
                
                LogUtils.d(TAG, "开始自动清理" + autoDays + "天前的缓存");
                
                long cleanThreshold = System.currentTimeMillis() - (autoDays * 24 * 60 * 60 * 1000L);
                LogUtils.d(TAG, "清理时间阈值: " + cleanThreshold + ", 当前时间: " + System.currentTimeMillis());
                
                // 分批处理，避免一次加载过多数据
                long freedSpace = 0;
                
                try {
                    // 使用时间截断条件查询，而不是一次加载所有旧缓存
                    LogUtils.d(TAG, "开始获取清理前的缓存大小");
                    long beforeClean = AppDataManager.get().getCacheDao().getTotalSize();
                    LogUtils.d(TAG, "清理前的缓存大小: " + beforeClean);
                    
                    // 使用按时间删除而不是先查询再删除
                    LogUtils.d(TAG, "开始执行删除旧缓存操作");
                    int deleted = AppDataManager.get().getCacheDao().deleteOldCache(cleanThreshold);
                    LogUtils.d(TAG, "删除操作完成，删除的缓存项数: " + deleted);
                    
                    LogUtils.d(TAG, "开始获取清理后的缓存大小");
                    long afterClean = AppDataManager.get().getCacheDao().getTotalSize();
                    LogUtils.d(TAG, "清理后的缓存大小: " + afterClean);
                    
                    freedSpace = beforeClean - afterClean;
                    LogUtils.d(TAG, "释放的空间大小: " + freedSpace);
                } catch (Exception e) {
                    LogUtils.e(TAG, "执行缓存操作时出错", e);
                    StringWriter sw = new StringWriter();
                    e.printStackTrace(new PrintWriter(sw));
                    LogUtils.e(TAG, "详细错误堆栈: " + sw.toString());
                }
                
                if (freedSpace > 0) {
                    int deleted = (int)(freedSpace / 1024); // 估算删除的项数
                    LogUtils.d(TAG, "自动清理了约" + deleted + "项旧缓存，释放空间: " + freedSpace / 1024 / 1024 + "MB");
                }
                
                return freedSpace;
            } catch (Exception e) {
                LogUtils.e(TAG, "自动清理旧缓存出错", e);
                StringWriter sw = new StringWriter();
                e.printStackTrace(new PrintWriter(sw));
                LogUtils.e(TAG, "详细错误堆栈: " + sw.toString());
                return 0;
            }
        }
        
        /**
         * 获取上次清理时间
         */
        public static long getLastCleanTime() {
            if (lastCleanTime == 0) {
                lastCleanTime = com.orhanobut.hawk.Hawk.get("cache_last_clean_time", 0L);
            }
            return lastCleanTime;
        }
        
        /**
         * 检查是否需要执行自动清理
         * 每天最多执行一次自动清理
         */
        public static boolean shouldRunAutoClean() {
            long lastTime = getLastCleanTime();
            long now = System.currentTimeMillis();
            
            // 检查是否已过24小时
            return (now - lastTime) > (24 * 60 * 60 * 1000L);
        }
    }

    public List<Cache> getAllCache() {
        List<Cache> caches = new ArrayList<>();
        try {
            File cacheDir = new File(App.getInstance().getCacheDir(), "cache");
            if (cacheDir.exists()) {
                File[] files = cacheDir.listFiles();
                if (files != null) {
                    for (File file : files) {
                        if (file.isFile()) {
                            Cache cache = new Cache();
                            cache.key = file.getName();
                            cache.data = FileUtils.readFile(file);
                            caches.add(cache);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return caches;
    }

    /**
     * 获取当前缓存大小
     * 简化版实现，返回估算值
     */
    public static long getCacheSize() {
        try {
            return getTotalCacheSize();
        } catch (Exception e) {
            Log.e(TAG, "获取缓存大小失败", e);
            return 0;
        }
    }
    
    /**
     * 清理所有缓存
     * 简化版实现
     */
    public static void clearCache() {
        try {
            getInstance().clearAllCache();
        } catch (Exception e) {
            Log.e(TAG, "清理缓存失败", e);
        }
    }
}
