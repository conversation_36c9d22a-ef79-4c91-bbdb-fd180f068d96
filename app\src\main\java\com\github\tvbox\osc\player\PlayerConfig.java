package com.github.tvbox.osc.player;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 播放器配置类
 * 用于存储和管理播放器相关配置
 */
public class PlayerConfig {
    private JSONObject config;

    /**
     * 创建默认配置
     */
    public PlayerConfig() {
        config = new JSONObject();
        try {
            // 默认播放器类型 (0:系统, 1:IJK, 2:Exo)
            config.put("pl", 0);
            // 默认解码 (软解码/硬解码)
            config.put("ijk", "软解码");
            // 默认渲染类型
            config.put("pr", 0);
            // 默认缩放类型
            config.put("sc", 0);
            // 默认播放速度
            config.put("sp", 1.0f);
            // 开始播放时间点 (秒)
            config.put("st", 0);
            // 结束播放时间点 (秒)
            config.put("et", 0);
            // 是否启用进度保存
            config.put("saveProgress", true);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 使用现有JSONObject创建配置
     */
    public PlayerConfig(JSONObject config) {
        this.config = config;
    }

    /**
     * 获取内部JSONObject
     */
    public JSONObject getConfig() {
        return config;
    }

    /**
     * 设置播放器类型
     * @param playerType 播放器类型 (0:系统, 1:IJK, 2:Exo)
     */
    public void setPlayerType(int playerType) {
        try {
            config.put("pl", playerType);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取播放器类型
     */
    public int getPlayerType() {
        return config.optInt("pl", 0);
    }

    /**
     * 设置解码器类型
     */
    public void setDecoderType(String decoder) {
        try {
            config.put("ijk", decoder);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取解码器类型
     */
    public String getDecoderType() {
        return config.optString("ijk", "软解码");
    }

    /**
     * 设置渲染器类型
     */
    public void setRenderType(int renderType) {
        try {
            config.put("pr", renderType);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取渲染器类型
     */
    public int getRenderType() {
        return config.optInt("pr", 0);
    }

    /**
     * 设置画面缩放类型
     */
    public void setScaleType(int scaleType) {
        try {
            config.put("sc", scaleType);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取画面缩放类型
     */
    public int getScaleType() {
        return config.optInt("sc", 0);
    }

    /**
     * 设置播放速度
     */
    public void setSpeed(float speed) {
        try {
            config.put("sp", speed);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取播放速度
     */
    public float getSpeed() {
        return (float) config.optDouble("sp", 1.0);
    }

    /**
     * 设置开始播放时间点
     */
    public void setStartTime(int startTime) {
        try {
            config.put("st", startTime);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取开始播放时间点
     */
    public int getStartTime() {
        return config.optInt("st", 0);
    }

    /**
     * 设置结束播放时间点
     */
    public void setEndTime(int endTime) {
        try {
            config.put("et", endTime);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取结束播放时间点
     */
    public int getEndTime() {
        return config.optInt("et", 0);
    }

    /**
     * 重置播放速度
     */
    public void resetSpeed() {
        try {
            config.put("sp", 1.0f);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取是否启用进度保存
     */
    public boolean isSaveProgressEnabled() {
        return config.optBoolean("saveProgress", true);
    }

    /**
     * 设置是否启用进度保存
     */
    public void setSaveProgressEnabled(boolean enabled) {
        try {
            config.put("saveProgress", enabled);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取整数配置项
     */
    public int getInt(String key) {
        return config.optInt(key, 0);
    }

    /**
     * 获取字符串配置项
     */
    public String getString(String key) {
        return config.optString(key, "");
    }

    /**
     * 获取浮点数配置项
     */
    public double getDouble(String key) {
        return config.optDouble(key, 0.0);
    }

    /**
     * 获取布尔配置项
     */
    public boolean getBoolean(String key) {
        return config.optBoolean(key, false);
    }

    /**
     * 设置配置项
     */
    public void put(String key, Object value) {
        try {
            config.put(key, value);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
} 