package com.github.tvbox.osc.util;

import android.content.Context;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import com.github.tvbox.osc.api.ApiConfig;
import com.github.tvbox.osc.base.App;
import com.github.tvbox.osc.cache.CacheManager;
import com.github.tvbox.osc.event.RefreshEvent;
import com.lzy.okgo.OkGo;
import com.lzy.okgo.callback.AbsCallback;
import com.lzy.okgo.model.HttpHeaders;
import com.lzy.okgo.model.HttpParams;
import com.lzy.okgo.model.Priority;
import com.lzy.okgo.model.Response;
import com.lzy.okgo.request.GetRequest;
import com.lzy.okgo.request.PostRequest;
import com.orhanobut.hawk.Hawk;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.PriorityBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import okhttp3.OkHttpClient;

/**
 * 高级网络请求管理器
 * 提供请求合并、优先级控制、重试机制和智能缓存功能
 */
public class NetworkRequestManager {
    private static final String TAG = "NetworkRequestManager";
    private static volatile NetworkRequestManager instance;
    
    // 线程池用于执行网络请求和相关处理
    private final ExecutorService executorService;
    private final ScheduledExecutorService scheduledExecutor;
    
    // 请求队列与集合
    private final PriorityBlockingQueue<RequestTask> requestQueue;
    private final Map<String, List<RequestCallback>> mergedRequests;
    private final Map<String, AtomicInteger> requestRetries;
    private final Map<String, Long> requestTimestamps;
    
    // 性能监控数据
    private final Map<String, Long> requestStartTimes;
    private final Map<String, Integer> requestSizes;
    private long totalRequestTime = 0;
    private int totalRequestCount = 0;
    private int succeededCount = 0;
    private int failedCount = 0;
    private int cachedCount = 0;
    
    // 配置参数
    private static final int MAX_RETRY_COUNT = 3;
    private static final long RETRY_DELAY_MS = 1000; // 重试延迟时间（毫秒）
    private static final int MAX_MERGE_WINDOW_MS = 50; // 请求合并窗口时间（毫秒）
    private static final long CACHE_EXPIRE_TIME = 5 * 60 * 1000; // 缓存过期时间（5分钟）
    private static final int QUEUE_PROCESSOR_DELAY = 100; // 队列处理延迟（毫秒）
    
    // 请求优先级
    public enum RequestPriority {
        LOW,
        NORMAL,
        HIGH,
        IMMEDIATE
    }
    
    private NetworkRequestManager() {
        // 初始化线程池和数据结构
        executorService = Executors.newFixedThreadPool(4);
        scheduledExecutor = Executors.newScheduledThreadPool(2);
        requestQueue = new PriorityBlockingQueue<>(50, 
                (o1, o2) -> o2.priority.ordinal() - o1.priority.ordinal());
        mergedRequests = new ConcurrentHashMap<>();
        requestRetries = new ConcurrentHashMap<>();
        requestTimestamps = new ConcurrentHashMap<>();
        requestStartTimes = new ConcurrentHashMap<>();
        requestSizes = new ConcurrentHashMap<>();
        
        // 启动请求处理线程
        startQueueProcessor();
        
        // 定期清理过期记录
        scheduledExecutor.scheduleAtFixedRate(this::cleanupExpiredRecords, 
                30, 30, TimeUnit.MINUTES);
    }
    
    private void startQueueProcessor() {
        scheduledExecutor.scheduleWithFixedDelay(() -> {
            try {
                processRequestQueue();
            } catch (Exception e) {
                LOG.e("处理请求队列时出错: " + e.getMessage());
            }
        }, 0, QUEUE_PROCESSOR_DELAY, TimeUnit.MILLISECONDS);
    }
    
    private void processRequestQueue() {
        try {
            // 处理请求队列中的任务
            while (!requestQueue.isEmpty()) {
                RequestTask task = requestQueue.poll();
                if (task != null) {
                    executeRequest(task);
                }
            }
        } catch (Exception e) {
            LOG.e("处理请求队列时出错: " + e.getMessage());
        }
    }
    
    private void executeRequest(RequestTask task) {
        // 检查是否有缓存
        String cacheKey = buildCacheKey(task.url, task.params);
        if (task.useCache) {
            // 尝试从缓存获取
            String cachedResponse = (String) CacheManager.getCache(cacheKey);
            if (cachedResponse != null) {
                LOG.i("使用缓存数据: " + cacheKey);
                task.startTime = System.currentTimeMillis();
                
                // 增加缓存计数
                cachedCount++;
                
                // 异步通知监听器
                notifyCallbacks(task.url, cachedResponse, null, true);
                
                // 在后台刷新缓存
                task.isRefreshCache = true;
            }
            
            // 如果没有缓存或需要刷新，继续执行网络请求
            if (cachedResponse == null || task.isRefreshCache) {
                executeNetworkRequest(task);
            } else {
                // 使用缓存，不再执行网络请求
                return;
            }
        } else {
            // 不使用缓存，直接执行网络请求
            executeNetworkRequest(task);
        }
    }
    
    /**
     * 执行网络请求
     */
    private void executeNetworkRequest(RequestTask task) {
        // 记录请求开始时间
        task.startTime = System.currentTimeMillis();
        requestStartTimes.put(task.url, task.startTime);
        
        try {
            LOG.i("执行请求" + (task.isRefreshCache ? "(后台刷新)" : "") 
                   + ": " + task.url + " 优先级: " + task.priority);
            
            // 根据请求方法创建请求
            if ("GET".equalsIgnoreCase(task.method)) {
                GetRequest<String> request = OkGo.get(task.url);
                
                // 设置参数
                if (task.params != null && !task.params.isEmpty()) {
                    request.params(task.params);
                }
                
                // 设置请求头
                if (task.headers != null && !task.headers.isEmpty()) {
                    HttpHeaders headers = new HttpHeaders();
                    for (Map.Entry<String, String> entry : task.headers.entrySet()) {
                        headers.put(entry.getKey(), entry.getValue());
                    }
                    request.headers(headers);
                }
                
                // 执行请求
                request.execute(new NetworkCallback(task));
            } else if ("POST".equalsIgnoreCase(task.method)) {
                PostRequest<String> request = OkGo.post(task.url);
                
                // 设置参数
                if (task.params != null && !task.params.isEmpty()) {
                    request.params(task.params);
                }
                
                // 设置请求头
                if (task.headers != null && !task.headers.isEmpty()) {
                    HttpHeaders headers = new HttpHeaders();
                    for (Map.Entry<String, String> entry : task.headers.entrySet()) {
                        headers.put(entry.getKey(), entry.getValue());
                    }
                    request.headers(headers);
                }
                
                // 执行请求
                request.execute(new NetworkCallback(task));
            } else {
                // 不支持的请求方法
                notifyCallbacks(task.url, null, new IllegalArgumentException("不支持的请求方法: " + task.method), false);
            }
        } catch (Exception e) {
            LOG.e("请求执行失败: " + e.getMessage());
            notifyCallbacks(task.url, null, e, false);
        }
    }
    
    private class NetworkCallback extends AbsCallback<String> {
        private final RequestTask task;
        
        NetworkCallback(RequestTask task) {
            this.task = task;
        }
        
        @Override
        public void onSuccess(Response<String> response) {
            long endTime = System.currentTimeMillis();
            long requestTime = endTime - task.startTime;
            
            // 更新统计信息
            totalRequestTime += requestTime;
            totalRequestCount++;
            succeededCount++;
            
            if (task.url != null) {
                requestRetries.remove(task.url);
            }
            
            try {
                String result = response.body();
                
                // 记录请求大小
                if (result != null) {
                    requestSizes.put(task.url, result.length());
                    LOG.i("请求成功: " + task.url + " 耗时: " + requestTime + "ms 大小: " + result.length() + "字节");
                }
                
                // 如果启用了缓存，保存到缓存
                if (task.useCache && result != null && !result.isEmpty()) {
                    String cacheKey = buildCacheKey(task.url, task.params);
                    // 保存到缓存
                    CacheManager.save(cacheKey, result);
                }
                
                // 只有在非缓存刷新模式下才通知回调
                if (!task.isRefreshCache) {
                    notifyCallbacks(task.url, result, null, false);
                }
            } catch (Exception e) {
                LOG.e("处理响应时出错: " + e.getMessage());
                notifyCallbacks(task.url, null, e, false);
            }
        }
        
        @Override
        public void onError(Response<String> response) {
            long endTime = System.currentTimeMillis();
            long requestTime = endTime - task.startTime;
            
            // 更新统计信息
            totalRequestCount++;
            failedCount++;
            
            // 记录错误
            LOG.e("请求失败: " + task.url + " 耗时: " + requestTime + "ms 错误: " + response.message());
            
            // 尝试重试
            retryIfNeeded(task);
        }
        
        @Override
        public String convertResponse(okhttp3.Response response) throws Throwable {
            return response.body() != null ? response.body().string() : null;
        }
    }
    
    private void retryIfNeeded(RequestTask task) {
        if (task.url == null) return;
        
        // 获取当前重试次数
        AtomicInteger retryCount = requestRetries.get(task.url);
        if (retryCount == null) {
            retryCount = new AtomicInteger(0);
            requestRetries.put(task.url, retryCount);
        }
        
        // 检查是否可以重试
        int currentRetry = retryCount.incrementAndGet();
        if (currentRetry <= MAX_RETRY_COUNT) {
            LOG.i("准备重试请求: " + task.url + " 第" + currentRetry + "次");
            
            // 使用指数退避策略
            long delay = RETRY_DELAY_MS * (long)Math.pow(2, currentRetry - 1);
            
            scheduledExecutor.schedule(() -> {
                LOG.i("执行重试请求: " + task.url);
                executeRequest(task);
            }, delay, TimeUnit.MILLISECONDS);
        } else {
            LOG.e("请求失败，已达到最大重试次数: " + task.url);
            
            // 通知失败回调
            notifyCallbacks(task.url, null, new Exception("请求失败，已达到最大重试次数"), false);
        }
    }
    
    private void notifyCallbacks(String url, String result, Exception error, boolean fromCache) {
        if (url == null) return;
        
        List<RequestCallback> callbacks = mergedRequests.get(url);
        if (callbacks != null) {
            // 复制列表避免并发修改异常
            List<RequestCallback> callbacksCopy = new ArrayList<>(callbacks);
            
            for (RequestCallback callback : callbacksCopy) {
                try {
                    if (error == null) {
                        callback.onSuccess(result, fromCache);
                    } else {
                        callback.onError(error);
                    }
                } catch (Exception e) {
                    LOG.e("执行回调时出错: " + e.getMessage());
                }
            }
            
            // 清理回调列表
            mergedRequests.remove(url);
        }
    }
    
    private String buildCacheKey(String url, Map<String, String> params) {
        StringBuilder sb = new StringBuilder(url);
        if (params != null && !params.isEmpty()) {
            // 按键排序，确保相同参数不同顺序生成相同的缓存键
            List<Map.Entry<String, String>> entries = new ArrayList<>(params.entrySet());
            Collections.sort(entries, Comparator.comparing(Map.Entry::getKey));
            
            sb.append('?');
            boolean first = true;
            for (Map.Entry<String, String> entry : entries) {
                if (!first) {
                    sb.append('&');
                }
                first = false;
                sb.append(entry.getKey()).append('=').append(entry.getValue());
            }
        }
        return sb.toString();
    }
    
    private void cleanupExpiredRecords() {
        long now = System.currentTimeMillis();
        
        // 清理过期的请求时间戳
        Iterator<Map.Entry<String, Long>> tsIterator = requestTimestamps.entrySet().iterator();
        while (tsIterator.hasNext()) {
            Map.Entry<String, Long> entry = tsIterator.next();
            if (now - entry.getValue() > 10 * 60 * 1000) { // 10分钟前的记录
                tsIterator.remove();
            }
        }
        
        // 清理长时间未完成的请求开始时间
        Iterator<Map.Entry<String, Long>> stIterator = requestStartTimes.entrySet().iterator();
        while (stIterator.hasNext()) {
            Map.Entry<String, Long> entry = stIterator.next();
            if (now - entry.getValue() > 5 * 60 * 1000) { // 5分钟前开始的请求
                stIterator.remove();
            }
        }
        
        // 清理过期的请求大小统计
        Iterator<Map.Entry<String, Integer>> szIterator = requestSizes.entrySet().iterator();
        while (szIterator.hasNext()) {
            Map.Entry<String, Integer> entry = szIterator.next();
            if (!requestTimestamps.containsKey(entry.getKey())) {
                szIterator.remove();
            }
        }
        
        // 清理过期的重试次数
        Iterator<Map.Entry<String, AtomicInteger>> rtIterator = requestRetries.entrySet().iterator();
        while (rtIterator.hasNext()) {
            Map.Entry<String, AtomicInteger> entry = rtIterator.next();
            if (!requestTimestamps.containsKey(entry.getKey())) {
                rtIterator.remove();
            }
        }
    }
    
    public static NetworkRequestManager getInstance() {
        if (instance == null) {
            synchronized (NetworkRequestManager.class) {
                if (instance == null) {
                    instance = new NetworkRequestManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 发送GET请求
     */
    public void get(String url, RequestCallback callback) {
        get(url, null, null, RequestPriority.NORMAL, true, callback);
    }
    
    /**
     * 发送带参数的GET请求
     */
    public void get(String url, Map<String, String> params, RequestCallback callback) {
        get(url, params, null, RequestPriority.NORMAL, true, callback);
    }
    
    /**
     * 发送完整参数的GET请求
     */
    public void get(String url, Map<String, String> params, Map<String, String> headers, 
                   RequestPriority priority, boolean useCache, RequestCallback callback) {
        // 构建请求任务
        RequestTask task = new RequestTask();
        task.url = url;
        task.method = "GET";
        task.params = params;
        task.headers = headers;
        task.priority = priority;
        task.useCache = useCache;
        
        // 添加回调到合并列表
        List<RequestCallback> callbacks = mergedRequests.computeIfAbsent(url, k -> new ArrayList<>());
        callbacks.add(callback);
        
        // 添加到请求队列
        requestQueue.offer(task);
    }
    
    /**
     * 发送POST请求
     */
    public void post(String url, RequestCallback callback) {
        post(url, null, null, RequestPriority.NORMAL, true, callback);
    }
    
    /**
     * 发送带参数的POST请求
     */
    public void post(String url, Map<String, String> params, RequestCallback callback) {
        post(url, params, null, RequestPriority.NORMAL, true, callback);
    }
    
    /**
     * 发送完整参数的POST请求
     */
    public void post(String url, Map<String, String> params, Map<String, String> headers, 
                    RequestPriority priority, boolean useCache, RequestCallback callback) {
        // 构建请求任务
        RequestTask task = new RequestTask();
        task.url = url;
        task.method = "POST";
        task.params = params;
        task.headers = headers;
        task.priority = priority;
        task.useCache = useCache;
        
        // 添加回调到合并列表
        List<RequestCallback> callbacks = mergedRequests.computeIfAbsent(url, k -> new ArrayList<>());
        callbacks.add(callback);
        
        // 添加到请求队列
        requestQueue.offer(task);
    }
    
    /**
     * 取消请求
     */
    public void cancel(String url) {
        OkGo.getInstance().cancelTag(url);
        mergedRequests.remove(url);
    }
    
    /**
     * 获取性能统计信息
     */
    public Map<String, Object> getPerformanceStats() {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("totalRequests", totalRequestCount);
        stats.put("succeededRequests", succeededCount);
        stats.put("failedRequests", failedCount);
        stats.put("cachedRequests", cachedCount);
        
        if (totalRequestCount > 0) {
            stats.put("averageRequestTime", totalRequestTime / totalRequestCount);
        } else {
            stats.put("averageRequestTime", 0);
        }
        
        stats.put("currentQueueSize", requestQueue.size());
        stats.put("activeRequests", requestStartTimes.size());
        
        return stats;
    }
    
    /**
     * 清除统计数据
     */
    public void clearStats() {
        totalRequestTime = 0;
        totalRequestCount = 0;
        succeededCount = 0;
        failedCount = 0;
        cachedCount = 0;
    }
    
    /**
     * 请求任务类
     */
    private static class RequestTask {
        String url;
        String method;
        Map<String, String> params;
        Map<String, String> headers;
        RequestPriority priority;
        boolean useCache;
        long startTime;
        boolean isRefreshCache = false;
    }
    
    /**
     * 请求回调接口
     */
    public interface RequestCallback {
        void onSuccess(String response, boolean fromCache);
        void onError(Exception e);
    }
    
    /**
     * 获取视频网络状况并智能调整缓冲策略
     * @param videoUrl 视频URL
     * @return 建议的缓冲策略
     */
    public static BufferStrategy getNetworkBufferStrategy(String videoUrl) {
        // 检测当前网络状况
        ConnectivityManager cm = (ConnectivityManager) App.getInstance()
            .getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
        
        // 默认策略
        BufferStrategy strategy = new BufferStrategy();
        
        // 网络不可用，使用最保守策略
        if (activeNetwork == null || !activeNetwork.isConnected()) {
            strategy.bufferTimeMs = 8000; // 8秒缓冲
            strategy.initialBufferMs = 5000; // 5秒初始缓冲
            strategy.networkSpeed = 0;
            return strategy;
        }
        
        // 根据网络类型调整策略
        if (activeNetwork.getType() == ConnectivityManager.TYPE_WIFI) {
            strategy.bufferTimeMs = 3000; // WiFi下3秒缓冲
            strategy.initialBufferMs = 1000; // 1秒初始缓冲
            strategy.maxCacheSize = 200 * 1024 * 1024; // 200MB缓存
        } else {
            // 移动网络
            strategy.bufferTimeMs = 5000; // 移动网络5秒缓冲
            strategy.initialBufferMs = 3000; // 3秒初始缓冲
            strategy.maxCacheSize = 80 * 1024 * 1024; // 80MB缓存
        }
        
        // 检测是否为直播流
        if (isLiveStream(videoUrl)) {
            strategy.bufferTimeMs = Math.min(strategy.bufferTimeMs, 2000); // 直播流缓冲时间更短
            strategy.isLiveStream = true;
        }
        
        // 估算网络速度（此处可以根据实际测速数据或历史记录优化）
        long networkSpeed = estimateNetworkSpeed();
        strategy.networkSpeed = networkSpeed;
        
        if (networkSpeed > 8 * 1024 * 1024) { // 8 Mbps以上
            strategy.bufferTimeMs = Math.min(strategy.bufferTimeMs, 1000);
            strategy.initialBufferMs = Math.min(strategy.initialBufferMs, 500);
        }
        
        return strategy;
    }
    
    /**
     * 检测是否为直播流
     */
    private static boolean isLiveStream(String url) {
        if (url == null) return false;
        
        String lowerUrl = url.toLowerCase();
        // 常见直播流特征
        return lowerUrl.contains("live") || 
               lowerUrl.contains("rtmp://") || 
               lowerUrl.contains("/live/") ||
               lowerUrl.contains("?type=live") ||
               lowerUrl.contains("&type=live");
    }
    
    /**
     * 估算当前网络速度
     * @return 估算的网络速度 (bps)
     */
    public static long estimateNetworkSpeed() {
        // 获取当前网络类型
        String networkType = NetworkUtils.getNetworkTypeName();
        
        // 根据网络类型提供基础估计
        long baseEstimate;
        if (NetworkUtils.isWifiConnection()) {
            baseEstimate = 5 * 1024 * 1024; // WiFi: 5 Mbps
        } else if (NetworkUtils.isMobileConnection()) {
            // 尝试区分移动网络类型
            if (networkType.contains("4G") || networkType.contains("5G")) {
                baseEstimate = 2 * 1024 * 1024; // 4G/5G: 2 Mbps
            } else if (networkType.contains("3G")) {
                baseEstimate = 1 * 1024 * 1024; // 3G: 1 Mbps
            } else {
                baseEstimate = 512 * 1024; // 2G/其他: 512 Kbps
            }
        } else {
            baseEstimate = 1 * 1024 * 1024; // 默认1 Mbps
        }
        
        // 尝试从之前的下载记录中获取更精确的估计
        SharedPreferences prefs = App.getInstance().getSharedPreferences("network_speed", Context.MODE_PRIVATE);
        long recordedSpeed = prefs.getLong("last_speed", 0);
        
        // 如果有历史记录且不是太久以前的，结合历史数据和基础估计
        long lastSpeedTime = prefs.getLong("last_speed_time", 0);
        if (recordedSpeed > 0 && (System.currentTimeMillis() - lastSpeedTime) < 24 * 60 * 60 * 1000) {
            // 结合历史记录和基础估计，给予历史记录更高的权重
            return (long)((recordedSpeed * 0.7) + (baseEstimate * 0.3));
        }
        
        // 无有效历史记录，返回基础估计
        return baseEstimate;
    }
    
    /**
     * 记录网络速度测量结果
     * @param bytesPerSecond 测量的每秒字节数
     */
    public static void recordNetworkSpeed(long bytesPerSecond) {
        if (bytesPerSecond <= 0) return;
        
        try {
            SharedPreferences prefs = App.getInstance().getSharedPreferences("network_speed", Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();
            editor.putLong("last_speed", bytesPerSecond);
            editor.putLong("last_speed_time", System.currentTimeMillis());
            editor.apply();
            
            LOG.i("记录网络速度: " + PlayerHelper.getDisplaySpeed(bytesPerSecond));
        } catch (Exception e) {
            LOG.e("记录网络速度出错: " + e.getMessage());
        }
    }
    
    /**
     * 检查当前播放性能，动态调整播放参数
     * @param url 当前播放URL
     * @return 优化后的播放参数
     */
    public static Bundle optimizePlaybackParameters(String url) {
        Bundle params = new Bundle();
        
        // 获取网络策略
        BufferStrategy strategy = getNetworkBufferStrategy(url);
        
        // 设置播放器参数
        params.putLong("buffer_time_ms", strategy.bufferTimeMs);
        params.putLong("initial_buffer_ms", strategy.initialBufferMs);
        params.putBoolean("is_live_stream", strategy.isLiveStream);
        
        // 如果是高速网络，开启预加载下一集
        if (strategy.networkSpeed > 5 * 1024 * 1024) { // 5 Mbps以上
            params.putBoolean("preload_next", true);
        }
        
        // 优先使用的解码器
        params.putString("preferred_decoder", "hardware"); // 优先硬解码
        
        // 根据网络状况设置默认清晰度
        if (strategy.networkSpeed < 2 * 1024 * 1024) { // 低于2Mbps
            params.putString("preferred_quality", "low");
        } else if (strategy.networkSpeed < 5 * 1024 * 1024) { // 2-5Mbps
            params.putString("preferred_quality", "medium");
        } else { // 高于5Mbps
            params.putString("preferred_quality", "high");
        }
        
        return params;
    }
    
    /**
     * 视频缓冲策略类
     */
    public static class BufferStrategy {
        public long bufferTimeMs = 5000; // 缓冲时间
        public long initialBufferMs = 3000; // 初始缓冲
        public boolean isLiveStream = false; // 是否为直播流
        public long maxCacheSize = 100 * 1024 * 1024; // 默认100MB缓存大小
        public long networkSpeed = 0; // 估算的网络速度 (bps)
        
        // 获取视频播放器设置选项
        public Bundle getPlayerOptions() {
            Bundle options = new Bundle();
            options.putLong("buffer_time_ms", bufferTimeMs);
            options.putLong("initial_buffer_ms", initialBufferMs);
            options.putBoolean("is_live_stream", isLiveStream);
            options.putLong("max_cache_size", maxCacheSize);
            return options;
        }
    }
} 