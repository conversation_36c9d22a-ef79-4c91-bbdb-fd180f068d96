<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <!--调整顶部左右边距
        android:paddingLeft="@dimen/vs_20"
        android:paddingRight="@dimen/vs_20"
        调底下的，不是调这个备注信息
        -->
    <LinearLayout
        android:id="@+id/topLayout"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/vs_30"
        android:paddingRight="@dimen/vs_30"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/vs_60"
        android:layout_marginTop="@dimen/vs_20"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/tvFind"
            android:layout_width="@dimen/vs_40"
            android:layout_height="@dimen/vs_40"
            android:background="@drawable/shape_top_navigation_search"
            android:focusable="true"
            android:nextFocusLeft="@+id/tvName"
            android:nextFocusDown="@+id/mGridViewCategory"/>

        <ImageView
            android:id="@+id/llHistory"
            android:layout_width="@dimen/vs_40"
            android:layout_height="@dimen/vs_40"
            android:background="@drawable/shape_top_navigation_history"
            android:focusable="true"
            android:layout_marginLeft="@dimen/vs_10"
            android:nextFocusDown="@+id/mGridViewCategory"/>

        <ImageView
            android:id="@+id/tvMenu"
            android:layout_width="@dimen/vs_40"
            android:layout_height="@dimen/vs_40"
            android:background="@drawable/shape_top_navigation_myuser"
            android:focusable="true"
            android:layout_marginLeft="@dimen/vs_10"
            android:nextFocusDown="@+id/mGridViewCategory" />

        <LinearLayout
            android:id="@+id/gongGao_root"
            android:gravity="center"
            android:background="@drawable/shape_setting_model_focus88"
            android:nextFocusDown="@+id/mGridViewCategory"
            android:paddingLeft="@dimen/vs_15"
            android:paddingTop="@dimen/vs_3"
            android:paddingRight="@dimen/vs_15"
            android:paddingBottom="@dimen/vs_3"
            android:focusable="true"
            android:layout_width="@dimen/vs_420"
            android:layout_height="@dimen/vs_40"
            android:layout_marginLeft="@dimen/vs_20">

            <ImageView
                android:gravity="center"
                android:layout_width="@dimen/vs_30"
                android:layout_height="@dimen/vs_30"
                android:src="@drawable/ic_gonggao"  />

            <TextView
                android:textSize="@dimen/ts_18"
                android:textColor="#fffeba98"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/vs_5"
                android:text="最新消息"  />

            <View
                android:background="@color/color_3D3D3D"
                android:layout_width="1.0dip"
                android:layout_height="fill_parent"
                android:layout_marginLeft="@dimen/vs_5"
                android:layout_marginTop="@dimen/vs_8"
                android:layout_marginRight="@dimen/vs_5"
                android:layout_marginBottom="@dimen/vs_8" />

            <com.github.tvbox.osc.ui.tv.widget.AlwaysMarqueeTextView
                android:id="@+id/gonggao"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:ellipsize="marquee"
                android:gravity="left|center_vertical"
                android:marqueeRepeatLimit="marquee_forever"
                android:paddingLeft="0dp"
                android:paddingRight="0dp"
                android:singleLine="true"
                android:text=""
                android:textColor="#fffeba98"
                android:textSize="@dimen/ts_20" />


        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_update"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/vs_2"
            android:gravity="center"
            android:layout_marginRight="@dimen/vs_20"
            android:layout_marginEnd="@dimen/vs_20">

            <TextView
                android:id="@+id/tv_update_msg"
                android:gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="准备中..."
                android:textColor="@color/white"/>
        </LinearLayout>

        <View
            android:layout_width="0.0dip"
            android:layout_height="fill_parent"
            android:layout_weight="1.0" />

        <ImageView
            android:id="@+id/tvWifi"
            android:layout_width="@dimen/vs_40"
            android:layout_height="@dimen/vs_40"
            android:layout_marginStart="@dimen/vs_5"
            android:layout_marginLeft="@dimen/vs_5"
            android:layout_marginEnd="@dimen/vs_5"
            android:layout_marginRight="@dimen/vs_5"
            android:background="@drawable/button_dialog_vod"
            android:clickable="true"
            android:focusable="true"
            android:padding="@dimen/vs_5"
            android:src="@drawable/hm_wifi"/>

        <ImageView
            android:id="@+id/tvxianlu"
            android:layout_width="@dimen/vs_40"
            android:layout_height="@dimen/vs_40"
            android:layout_marginStart="@dimen/vs_5"
            android:layout_marginLeft="@dimen/vs_5"
            android:layout_marginEnd="@dimen/vs_5"
            android:layout_marginRight="@dimen/vs_5"
            android:background="@drawable/button_dialog_vod"
            android:clickable="true"
            android:focusable="true"
            android:padding="@dimen/vs_5"
            android:src="@drawable/line" />

        <ImageView
            android:id="@+id/tvDrawer"
            android:layout_width="@dimen/vs_40"
            android:layout_height="@dimen/vs_40"
            android:layout_marginStart="@dimen/vs_5"
            android:layout_marginLeft="@dimen/vs_5"
            android:layout_marginEnd="@dimen/vs_5"
            android:layout_marginRight="@dimen/vs_5"
            android:background="@drawable/button_dialog_vod"
            android:clickable="true"
            android:focusable="true"
            android:padding="@dimen/vs_5"
            android:src="@drawable/hm_drawer" />

        <TextView
            android:id="@+id/tvDate"
            android:layout_width="wrap_content"
            android:layout_height="fill_parent"
            android:layout_marginLeft="@dimen/vs_5"
            android:layout_marginRight="@dimen/vs_5"
            android:gravity="center|right"
            android:focusable="false"
            android:textColor="#FFFFFFFF"
            android:textSize="@dimen/ts_20"
            android:textStyle="bold"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            tools:text="10：10" />

        <TextView
            android:id="@+id/tvName"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:clickable="true"
            android:focusable="true"
            android:focusableInTouchMode="false"
            android:gravity="left|center_vertical"
            android:nextFocusRight="@+id/tvFind"
            android:nextFocusDown="@+id/mGridViewCategory"
            android:text="@string/app_name"
            android:textAlignment="gravity"
            android:textColor="@color/font_home_selector"
            android:textSize="@dimen/ts_22"
            android:paddingLeft="10dp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="@dimen/vs_0"
            android:layout_height="match_parent"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:gravity="left|center_vertical"
            android:textAlignment="gravity"
            android:textColor="@color/font_home_selector"
            android:textSize="@dimen/ts_30"
            android:textStyle="bold" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentLayout"
        android:orientation="vertical"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:layout_width="@dimen/vs_0"
        android:layout_height="@dimen/vs_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/topLayout">

        <View android:background="@color/white"
            android:layout_width="fill_parent"
            android:layout_height="@dimen/vs_1"
            android:layout_marginTop="@dimen/vs_1"
            android:layout_marginBottom="@dimen/vs_1" />

        <!--调整分类边距
            android:paddingLeft="@dimen/vs_20"左边距
            android:paddingRight="@dimen/vs_50"右边距
            调底下的，不是调这个备注信息
                -->
        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridViewCategory"
            android:layout_gravity="left"
            android:paddingLeft="@dimen/vs_30"
            android:paddingRight="@dimen/vs_30"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/vs_10"
            android:layout_marginBottom="@dimen/vs_10"
            app:tv_selectedItemIsCentered="true" />

        <com.github.tvbox.osc.ui.tv.widget.NoScrollViewPager
            android:id="@+id/mViewPager"
            android:layout_width="fill_parent"
            android:layout_height="fill_parent" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>