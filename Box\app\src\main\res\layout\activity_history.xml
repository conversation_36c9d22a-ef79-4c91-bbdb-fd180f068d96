<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/vs_50"
        android:paddingRight="@dimen/vs_50"
        android:paddingBottom="@dimen/vs_10"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/vs_60"
        android:layout_marginTop="@dimen/vs_10"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/ls_finishHome"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/vs_40"
            android:background="@drawable/shape_setting_model_focus"
            android:paddingLeft="@dimen/vs_6"
            android:paddingTop="@dimen/vs_2"
            android:paddingRight="@dimen/vs_6"
            android:paddingBottom="@dimen/vs_2"
            android:focusable="true"
            android:gravity="center"
            android:nextFocusDown="@+id/tvPlay"
            android:text="返回 "
            android:textSize="@dimen/ts_24"
            android:textColor="@color/color_FFFFFF"
            android:drawablePadding="@dimen/vs_5"
            app:drawableLeftCompat="@drawable/ic_home_99" />

        <TextView
            android:id="@+id/lsSearch"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/vs_40"
            android:layout_marginLeft="@dimen/vs_20"
            android:background="@drawable/shape_setting_model_focus"
            android:paddingLeft="@dimen/vs_6"
            android:paddingTop="@dimen/vs_2"
            android:paddingRight="@dimen/vs_6"
            android:paddingBottom="@dimen/vs_2"
            android:focusable="true"
            android:gravity="center"
            android:nextFocusDown="@+id/tvPlay"
            android:text="搜索 "
            android:textSize="@dimen/ts_24"
            android:textColor="@color/color_FFFFFF"
            android:drawablePadding="@dimen/vs_5"
            app:drawableLeftCompat="@drawable/ic_sousuo_99" />

        <TextView
            android:id="@+id/lsUserHome"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/vs_40"
            android:layout_marginLeft="@dimen/vs_20"
            android:background="@drawable/shape_setting_model_focus"
            android:paddingLeft="@dimen/vs_6"
            android:paddingTop="@dimen/vs_2"
            android:paddingRight="@dimen/vs_6"
            android:paddingBottom="@dimen/vs_2"
            android:focusable="true"
            android:gravity="center"
            android:nextFocusDown="@+id/tvPlay"
            android:text="我的"
            android:textSize="@dimen/ts_24"
            android:textColor="@color/color_FFFFFF"
            android:drawablePadding="@dimen/vs_5"
            app:drawableLeftCompat="@drawable/icon_user" />


        <LinearLayout
            android:gravity="center"
            android:id="@+id/gongGao_root"
            android:background="@drawable/shape_setting_model_focus88"
            android:paddingLeft="@dimen/vs_15"
            android:paddingTop="@dimen/vs_3"
            android:paddingRight="@dimen/vs_15"
            android:paddingBottom="@dimen/vs_3"
            android:nextFocusDown="@id/tvPlay"
            android:layout_width="@dimen/vs_420"
            android:layout_height="@dimen/vs_40"
            android:layout_marginLeft="@dimen/vs_20">

            <ImageView
                android:gravity="center"
                android:layout_width="@dimen/vs_30"
                android:layout_height="@dimen/vs_30"
                android:src="@drawable/ic_gonggao" />

            <TextView
                android:textSize="@dimen/ts_18"
                android:textColor="#fffeba98"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/vs_5"
                android:text="最新消息" />

            <View
                android:background="@color/color_3D3D3D"
                android:layout_width="1.0dip"
                android:layout_height="fill_parent"
                android:layout_marginLeft="@dimen/vs_5"
                android:layout_marginTop="@dimen/vs_8"
                android:layout_marginRight="@dimen/vs_5"
                android:layout_marginBottom="@dimen/vs_8" />

            <com.github.tvbox.osc.ui.tv.widget.AlwaysMarqueeTextView
                android:id="@+id/lsgonggao"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:ellipsize="marquee"
                android:gravity="left|center_vertical"
                android:marqueeRepeatLimit="marquee_forever"
                android:paddingLeft="0dp"
                android:paddingRight="0dp"
                android:singleLine="true"
                android:text=""
                android:textColor="#fffeba98"
                android:textSize="@dimen/ts_24" />

        </LinearLayout>

        <Space
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tvDelTip"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/vs_50"
            android:layout_gravity="center"
            android:layout_marginRight="@dimen/vs_10"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:gravity="center"
            android:text="请选择要删除的数据"
            android:textColor="@color/color_FF0057"
            android:textSize="@dimen/ts_20"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvDel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/shape_user_focus"
            android:focusable="true"
            android:gravity="center"
            android:padding="@dimen/vs_10"
            android:text="删除记录"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_20" />
    </LinearLayout>

    <View
        android:background="@color/color_6CFFFFFF"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/vs_1"
        android:layout_marginTop="@dimen/vs_1"
        android:layout_marginBottom="@dimen/vs_3" />

    <com.owen.tvrecyclerview.widget.TvRecyclerView
        android:id="@+id/mGridView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingLeft="@dimen/vs_60"
        android:paddingTop="@dimen/vs_10"
        android:paddingRight="@dimen/vs_60"
        android:paddingBottom="@dimen/vs_10"
        app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
        app:tv_selectedItemIsCentered="true"
        app:tv_verticalSpacingWithMargins="@dimen/vs_10" />
</LinearLayout>