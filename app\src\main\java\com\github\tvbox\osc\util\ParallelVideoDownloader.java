package com.github.tvbox.osc.util;

import android.text.TextUtils;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.PriorityBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 视频并行分段下载工具
 * 通过将视频文件分成多个小块同时下载，加速视频加载
 * 支持自适应分块大小和智能分段排序
 */
public class ParallelVideoDownloader {
    private static final String TAG = "ParallelDownloader";
    
    // 默认分块大小：1MB (现在通过自适应视频加载器动态决定)
    private static final int DEFAULT_CHUNK_SIZE = 1024 * 1024; 
    // 默认分块数量：5段并发下载 (现在通过自适应视频加载器动态决定)
    private static final int DEFAULT_CHUNK_COUNT = 5; 
    
    /**
     * 并行分段下载视频文件，加速视频加载 (使用自适应视频加载器)
     * @param url 视频文件URL
     * @param listener 下载回调
     */
    public static void downloadWithChunks(String url, VideoDownloadListener listener) {
        downloadWithChunks(url, null, listener);
    }

    /**
     * 并行分段下载视频文件，加速视频加载 (使用自适应视频加载器)
     * @param url 视频文件URL
     * @param headers 请求头
     * @param listener 下载回调  
     */
    public static void downloadWithChunks(String url, HashMap<String, String> headers, VideoDownloadListener listener) {
        if (listener == null) {
            LOG.e("下载监听器为空");
            return;
        }
        
        // 使用自适应视频加载器
        AdaptiveVideoLoader loader = AdaptiveVideoLoader.getInstance();
        
        // 添加监听器
        AdaptiveVideoLoaderAdapter adapterListener = new AdaptiveVideoLoaderAdapter(listener);
        loader.addListener(adapterListener);
        
        // 判断是否为HLS格式
        if (isHlsVideo(url)) {
            // HLS格式特殊处理
            downloadHlsVideo(url, headers, listener);
            return;
        }
        
        // 使用智能分块加载视频
        loader.loadVideoWithChunks(url, headers, adapterListener);
    }
    
    /**
     * 优先加载视频的特定区域 (例如前几秒的预览帧)
     * @param url 视频文件URL
     * @param listener 下载回调
     * @param prioritySections 优先区域列表
     */
    public static void downloadWithPrioritySections(String url, VideoDownloadListener listener, 
                                                  List<PrioritySection> prioritySections) {
        downloadWithPrioritySections(url, null, listener, prioritySections);
    }

    /**
     * 优先加载视频的特定区域 (例如前几秒的预览帧)
     * @param url 视频文件URL
     * @param headers 请求头
     * @param listener 下载回调
     * @param prioritySections 优先区域列表
     */
    public static void downloadWithPrioritySections(String url, HashMap<String, String> headers, 
                                                  VideoDownloadListener listener,
                                                  List<PrioritySection> prioritySections) {
        if (listener == null) {
            LOG.e("下载监听器为空");
            return;
        }
        
        // 使用自适应视频加载器
        AdaptiveVideoLoader loader = AdaptiveVideoLoader.getInstance();
        
        // 添加监听器
        AdaptiveVideoLoaderAdapter adapterListener = new AdaptiveVideoLoaderAdapter(listener);
        loader.addListener(adapterListener);
        
        // 判断是否为HLS格式
        if (isHlsVideo(url)) {
            // HLS格式特殊处理
            downloadHlsVideo(url, headers, listener);
                    return;
                }
                
        // 使用智能分块加载视频，包含优先区域
        loader.loadVideoWithPrioritySections(url, headers, adapterListener, prioritySections);
    }
    
    /**
     * 下载HLS格式视频
     * @param url m3u8 URL
     * @param listener 下载回调
     */
    public static void downloadHlsVideo(String url, VideoDownloadListener listener) {
        downloadHlsVideo(url, null, listener);
    }

    /**
     * 下载HLS格式视频
     * @param url m3u8 URL
     * @param headers 请求头
     * @param listener 下载回调
     */
    public static void downloadHlsVideo(String url, HashMap<String, String> headers, VideoDownloadListener listener) {
        if (listener == null) {
            LOG.e("下载监听器为空");
                    return;
                }
                
        // 使用自适应视频加载器
        AdaptiveVideoLoader loader = AdaptiveVideoLoader.getInstance();
        
        // 使用HLS特定的监听器
        loader.loadHlsVideo(url, headers, new AdaptiveVideoLoader.HlsLoadListener() {
            @Override
            public void onHlsSuccess(String url, AdaptiveVideoLoader.HlsPreloadResult result) {
                LOG.i("HLS视频预加载成功: TS分片数=" + result.tsUrls.size() + 
                      ", 预加载分片数=" + result.preloadedSegments.size());
                
                // 创建包含m3u8内容和预加载分片的结果
                HlsDownloadResult hlsResult = new HlsDownloadResult(
                    result.m3u8Content, 
                    result.tsUrls, 
                    result.preloadedSegments
                );
                
                // 通知成功
                listener.onCompleteHls(hlsResult);
            }
            
            @Override
            public void onProgress(String url, int progress) {
                listener.onProgress(progress);
            }
            
            @Override
            public void onSuccess(String url, byte[] data) {
                // 不应该直接调用此方法，应该调用onHlsSuccess
                LOG.e("错误：HLS加载器不应直接调用普通onSuccess");
                listener.onError(new IOException("HLS处理错误"));
            }
            
            @Override
            public void onError(String url, String error) {
                listener.onError(new IOException("HLS加载错误: " + error));
            }
        });
    }
    
    /**
     * 取消正在进行的下载
     * @param url 要取消的下载URL
     */
    public static void cancelDownload(String url) {
        if (TextUtils.isEmpty(url)) return;
        
        AdaptiveVideoLoader.getInstance().cancelLoad(url);
    }
    
    /**
     * 检查是否为HLS格式视频
     */
    public static boolean isHlsVideo(String url) {
        if (TextUtils.isEmpty(url)) return false;
        
        String lowerUrl = url.toLowerCase();
        return lowerUrl.endsWith(".m3u8") || 
               lowerUrl.contains(".m3u8?") || 
               lowerUrl.contains("/hls/") || 
               lowerUrl.contains("=m3u8");
    }
    
    /**
     * 优先区段定义类
     */
    public static class PrioritySection {
        public final long startPos;    // 起始位置
        public final long length;      // 长度
        public final int priority;     // 优先级（越小越优先）
        
        public PrioritySection(long startPos, long length, int priority) {
            this.startPos = startPos;
            this.length = length;
            this.priority = priority;
        }
    }
    
    /**
     * 根据视频URL和当前播放位置创建优先加载区域
     * @param url 视频URL
     * @param currentPosition 当前播放位置(ms)
     * @param isLiveStream 是否为直播流
     * @param videoDuration 视频总时长(ms)
     * @return 优先加载区域
     */
    public static PrioritySection[] createPrioritySectionsForPosition(String url, long currentPosition, boolean isLiveStream, long videoDuration) {
        // 获取总体文件大小（如果是HLS则为0）
        long fileSize = 0;
        if (!isHlsVideo(url)) {
            fileSize = getFileSizeFromUrl(url);
        }
        
        // 如果无法获取文件大小或者是直播流，使用保守策略
        if (fileSize <= 0 || isLiveStream) {
            // 使用固定大小的预加载区块
            PrioritySection preBuffer = new PrioritySection(0, 2 * 1024 * 1024, 0); // 前2MB
            return new PrioritySection[]{preBuffer};
        }
        
        // 根据当前时间戳以及视频总时长计算当前大致位置
        long estimatedBytePosition = 0;
        if (videoDuration > 0) {
            // 估算当前字节位置
            estimatedBytePosition = (long) (((double) currentPosition / videoDuration) * fileSize);
        }
        
        // 防止位置超过文件大小
        estimatedBytePosition = Math.min(estimatedBytePosition, fileSize - 1);
        estimatedBytePosition = Math.max(0, estimatedBytePosition);
        
        // 网络状况感知 - 从AdaptiveVideoLoader获取网络健康评分
        int networkHealthScore = AdaptiveVideoLoader.getInstance().getNetworkHealthScore();
        boolean isHighEndDevice = AdaptiveVideoLoader.getInstance().isHighEndDevice();
        
        // 根据网络状况和设备性能调整预加载大小
        long preBufferSize = calculatePreBufferSize(networkHealthScore, isHighEndDevice);
        long midBufferSize = calculateMidBufferSize(networkHealthScore, isHighEndDevice);
        long postBufferSize = calculatePostBufferSize(networkHealthScore, isHighEndDevice);
        
        // 创建多级优先区域
        List<PrioritySection> sections = new ArrayList<>();
        
        // 1. 最高优先级：当前播放点 -10秒 到 +30秒 (粗略估算，每秒约100KB)
        long preSeconds = 10;
        long postSeconds = 30;
        
        // 每秒大致的字节数 (这是一个粗略估计，实际应根据视频比特率计算)
        long bytesPerSecond = fileSize / Math.max(1, videoDuration / 1000);
        
        // 计算关键缓冲区的起始和结束位置
        long criticalStart = Math.max(0, estimatedBytePosition - preSeconds * bytesPerSecond);
        long criticalEnd = Math.min(fileSize - 1, estimatedBytePosition + postSeconds * bytesPerSecond);
        long criticalLength = criticalEnd - criticalStart + 1;
        
        // 添加关键缓冲区 (最高优先级为0)
        sections.add(new PrioritySection(criticalStart, criticalLength, 0));
        
        // 2. 中等优先级：当前播放点 +30秒 到 +3分钟
        // 计算中间缓冲区 (优先级为10)
        long midStart = criticalEnd + 1;
        if (midStart < fileSize) {
            // 中间缓冲区大小 (通常为2-5MB，取决于网络状况)
            long midEnd = Math.min(fileSize - 1, midStart + midBufferSize - 1);
            long midLength = midEnd - midStart + 1;
            
            // 如果中间缓冲区有效，添加它
            if (midLength > 0) {
                sections.add(new PrioritySection(midStart, midLength, 10));
            }
        }
        
        // 3. 低优先级：预加载文件头部信息 (为了快速的视频信息解析)
        // 文件头优先级为20
        if (estimatedBytePosition > preBufferSize * 2) {
            // 只有当当前位置离文件头部足够远时，才添加文件头部预加载
            sections.add(new PrioritySection(0, preBufferSize, 20));
        }
        
        // 4. 最低优先级：额外的后向缓冲区，主要用于用户往后跳跃的情况
        // 后缓冲区优先级为30
        if (estimatedBytePosition + postBufferSize < fileSize) {
            long backBufferStart = midStart + midBufferSize;
            if (backBufferStart < fileSize) {
                long backBufferLength = Math.min(postBufferSize, fileSize - backBufferStart);
                if (backBufferLength > 0) {
                    sections.add(new PrioritySection(backBufferStart, backBufferLength, 30));
                }
            }
        }
        
        // 将列表转换为数组
        return sections.toArray(new PrioritySection[0]);
    }
    
    /**
     * 根据网络健康评分和设备性能计算预缓冲区大小
     */
    private static long calculatePreBufferSize(int networkHealthScore, boolean isHighEndDevice) {
        long baseSize = 1 * 1024 * 1024; // 基础大小1MB
        
        // 网络健康评分影响：0-100分，分数越高预缓冲区越大
        float networkFactor = networkHealthScore / 100.0f * 2.0f; // 0-2.0
        
        // 设备性能影响：高端设备允许更大的缓冲区
        float deviceFactor = isHighEndDevice ? 1.5f : 1.0f;
        
        // 计算最终大小，确保在合理范围内
        long calculatedSize = (long)(baseSize * networkFactor * deviceFactor);
        
        // 限制在512KB到4MB之间
        return Math.max(512 * 1024, Math.min(4 * 1024 * 1024, calculatedSize));
    }
    
    /**
     * 计算中间缓冲区大小
     */
    private static long calculateMidBufferSize(int networkHealthScore, boolean isHighEndDevice) {
        long baseSize = 2 * 1024 * 1024; // 基础大小2MB
        
        // 网络健康评分影响
        float networkFactor = networkHealthScore / 100.0f * 3.0f; // 0-3.0
        
        // 设备性能影响
        float deviceFactor = isHighEndDevice ? 1.5f : 1.0f;
        
        // 计算最终大小
        long calculatedSize = (long)(baseSize * networkFactor * deviceFactor);
        
        // 限制在1MB到10MB之间
        return Math.max(1 * 1024 * 1024, Math.min(10 * 1024 * 1024, calculatedSize));
    }
    
    /**
     * 计算后缓冲区大小
     */
    private static long calculatePostBufferSize(int networkHealthScore, boolean isHighEndDevice) {
        long baseSize = 1 * 1024 * 1024; // 基础大小1MB
        
        // 对后缓冲区来说，网络状况影响更大
        float networkFactor = networkHealthScore / 100.0f * 4.0f; // 0-4.0
        
        // 设备性能影响
        float deviceFactor = isHighEndDevice ? 1.2f : 0.8f;
        
        // 计算最终大小
        long calculatedSize = (long)(baseSize * networkFactor * deviceFactor);
        
        // 限制在512KB到8MB之间
        return Math.max(512 * 1024, Math.min(8 * 1024 * 1024, calculatedSize));
    }
    
    /**
     * 获取文件大小
     * @param url 文件URL
     * @return 文件大小（字节），失败返回-1
     */
    private static long getFileSizeFromUrl(String url) {
        OkHttpClient client = OkGoHelper.getDefaultClient();
        Request request = new Request.Builder()
            .url(url)
            .head() // 只请求头部信息
            .build();
        
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                String contentLength = response.header("Content-Length");
                if (!TextUtils.isEmpty(contentLength)) {
                    return Long.parseLong(contentLength);
                }
            }
        } catch (Exception e) {
            LOG.e("获取文件大小失败: " + e.getMessage());
        }
        
        return -1;
    }
    
    /**
     * 自适应加载器到下载监听器的适配器
     */
    private static class AdaptiveVideoLoaderAdapter implements AdaptiveVideoLoader.LoadListener {
        private final VideoDownloadListener originalListener;
        
        public AdaptiveVideoLoaderAdapter(VideoDownloadListener listener) {
            this.originalListener = listener;
        }
        
        @Override
        public void onProgress(String url, int progress) {
            originalListener.onProgress(progress);
        }
        
        @Override
        public void onSuccess(String url, byte[] data) {
            originalListener.onComplete(data);
            
            // 下载完成后移除监听器
            AdaptiveVideoLoader.getInstance().removeListener(this);
        }
        
        @Override
        public void onError(String url, String error) {
            originalListener.onError(new IOException(error));
            
            // 出错后移除监听器
            AdaptiveVideoLoader.getInstance().removeListener(this);
        }
    }
    
    /**
     * HLS下载结果类
     */
    public static class HlsDownloadResult {
        public String m3u8Content;            // m3u8文件内容
        public List<String> tsUrls;           // TS分片URL列表
        public List<byte[]> preloadedSegments; // 预加载的TS分片数据
        
        public HlsDownloadResult(String m3u8Content, List<String> tsUrls, List<byte[]> preloadedSegments) {
            this.m3u8Content = m3u8Content;
            this.tsUrls = tsUrls;
            this.preloadedSegments = preloadedSegments;
        }
    }
    
    /**
     * 视频下载监听器接口
     */
    public interface VideoDownloadListener {
        // 下载进度更新
        void onProgress(int progress);
        
        // 下载完成
        void onComplete(byte[] data);
        
        // HLS格式下载完成
        void onCompleteHls(HlsDownloadResult result);
        
        // 下载错误
        void onError(Exception e);
    }
} 