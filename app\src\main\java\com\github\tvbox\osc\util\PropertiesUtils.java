package com.github.tvbox.osc.util;

import android.util.Log;
import java.io.FileInputStream;
import java.util.Properties;
import java.util.HashMap;
import java.util.Map;

//零熙QQ：1007713299
public class PropertiesUtils {
    private static final String TAG = "PropertiesUtils";
    private static Properties properties = new Properties();
    private static Map<String, String> propertiesMap = new HashMap<>();

    public static void load(String filePath){
        FileInputStream inputStream = null;
        try {
            inputStream = new FileInputStream(filePath);
            properties.load(inputStream);
            
            // 清除旧的数据
            propertiesMap.clear();
            
            // 处理属性，移除注释部分
            for (Object key : properties.keySet()) {
                String keyStr = key.toString();
                String valueStr = properties.getProperty(keyStr, "");
                
                // 处理可能包含注释的属性值
                int commentIndex = valueStr.indexOf('#');
                if (commentIndex > 0) {
                    // 只保留注释前的部分
                    valueStr = valueStr.substring(0, commentIndex).trim();
                }
                
                // 存储处理后的数据
                propertiesMap.put(keyStr, valueStr);
                Log.d(TAG, "加载配置: " + keyStr + " = " + valueStr);
            }
            
            inputStream.close();
        } catch (Exception e) {
            Log.e(TAG, "配置文件加载错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static String getProperties(String key){
       // 优先从处理后的Map中获取
       if (propertiesMap.containsKey(key)) {
           return propertiesMap.get(key);
       }
       return properties.getProperty(key,"");
    }
}
//零熙QQ：1007713299