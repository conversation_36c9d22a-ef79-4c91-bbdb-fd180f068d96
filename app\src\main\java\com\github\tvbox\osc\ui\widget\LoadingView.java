package com.github.tvbox.osc.ui.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.DrawableRes;

import com.github.tvbox.osc.R;
import com.github.tvbox.osc.util.LOG;

/**
 * 增强版加载视图
 * 支持加载动画、错误提示、网络状态和预加载进度
 */
public class LoadingView extends FrameLayout {
    // 常量定义
    private static final int ANIM_DURATION = 250; // 动画持续时间(毫秒)
    
    // UI组件
    private LinearLayout loadingLayout;
    private LinearLayout errorLayout;
    private LinearLayout networkStatusLayout;
    private TextView loadingText;
    private TextView loadingDetailText;
    private TextView errorText;
    private TextView errorDetailText;
    private TextView networkText;
    private ProgressBar preloadProgressBar;
    private Button btnRetry;
    private ImageView errorIcon;
    private ProgressBar progressBar;
    
    // 状态
    private boolean isShowing = false;
    private boolean isNetworkStatusShowing = false;
    private State currentState = State.LOADING;
    
    // 回调
    private OnClickCallback retryCallback;
    private OnClickListener retryClickListener;
    
    // 状态枚举
    public enum State {
        LOADING,
        ERROR,
        SUCCESS
    }

    public LoadingView(@NonNull Context context) {
        this(context, null);
    }

    public LoadingView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public LoadingView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }
    
    private void init(Context context) {
        LOG.i("初始化增强版加载视图");
        
        // 加载布局
        LayoutInflater.from(context).inflate(R.layout.view_loading, this, true);
        
        // 查找视图
        loadingLayout = findViewById(R.id.loadingLayout);
        errorLayout = findViewById(R.id.errorLayout);
        networkStatusLayout = findViewById(R.id.networkStatusLayout);
        loadingText = findViewById(R.id.loadingText);
        loadingDetailText = findViewById(R.id.loadingDetailText);
        errorText = findViewById(R.id.errorText);
        errorDetailText = findViewById(R.id.errorDetailText);
        networkText = findViewById(R.id.networkText);
        preloadProgressBar = findViewById(R.id.preloadProgressBar);
        btnRetry = findViewById(R.id.btnRetry);
        errorIcon = findViewById(R.id.errorIcon);
        progressBar = findViewById(R.id.progressBar);
        
        // 设置重试按钮点击事件
        btnRetry.setOnClickListener(v -> {
            if (retryCallback != null) {
                setState(State.LOADING);
                retryCallback.onClick();
            }
            if (retryClickListener != null) {
                retryClickListener.onClick(v);
            }
        });
        
        // 初始隐藏
        setVisibility(GONE);
    }
    
    /**
     * 设置加载视图的状态
     * @param state 要设置的状态
     */
    public void setState(State state) {
        if (currentState == state) return;
        
        currentState = state;
        
        switch (state) {
            case LOADING:
                loadingLayout.setVisibility(VISIBLE);
                errorLayout.setVisibility(GONE);
                break;
                
            case ERROR:
                loadingLayout.setVisibility(GONE);
                errorLayout.setVisibility(VISIBLE);
                break;
                
            case SUCCESS:
                // 成功状态时隐藏加载视图
                hide();
                return;
        }
        
        // 如果当前未显示，则显示
        if (!isShowing) {
            show();
        }
    }
    
    /**
     * 设置加载状态（简化版）
     * @param isLoading 是否为加载状态
     */
    public void setLoadingState(boolean isLoading) {
        if (isLoading) {
            setState(State.LOADING);
        } else {
            // 注意：这里不设置为SUCCESS以避免自动隐藏
            loadingLayout.setVisibility(isLoading ? VISIBLE : GONE);
        }
    }
    
    /**
     * 设置错误状态（简化版）
     * @param isError 是否为错误状态
     */
    public void setErrorState(boolean isError) {
        errorLayout.setVisibility(isError ? VISIBLE : GONE);
        if (isError) {
            loadingLayout.setVisibility(GONE);
        }
    }
    
    /**
     * 设置错误图标
     * @param resId 图标资源ID
     */
    public void setErrorIcon(@DrawableRes int resId) {
        errorIcon.setImageResource(resId);
    }
    
    /**
     * 设置重试按钮可见性
     * @param visible 是否可见
     */
    public void setRetryVisible(boolean visible) {
        btnRetry.setVisibility(visible ? VISIBLE : GONE);
    }
    
    /**
     * 设置加载详情文本可见性
     * @param visible 是否可见
     */
    public void setLoadingDetailVisible(boolean visible) {
        loadingDetailText.setVisibility(visible ? VISIBLE : GONE);
    }
    
    /**
     * 设置重试按钮点击监听器（使用标准的OnClickListener）
     * @param listener 点击监听器
     */
    public void setRetryClickListener(OnClickListener listener) {
        this.retryClickListener = listener;
    }
    
    /**
     * 显示加载视图
     */
    public void show() {
        if (isShowing) return;
        isShowing = true;
        
        setVisibility(VISIBLE);
        // 渐变动画
        ObjectAnimator animator = ObjectAnimator.ofFloat(this, "alpha", 0f, 1f);
        animator.setDuration(ANIM_DURATION);
        animator.start();
    }
    
    /**
     * 隐藏加载视图
     */
    public void hide() {
        if (!isShowing) return;
        isShowing = false;
        
        // 渐变动画
        ObjectAnimator animator = ObjectAnimator.ofFloat(this, "alpha", 1f, 0f);
        animator.setDuration(ANIM_DURATION);
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                setVisibility(GONE);
            }
        });
        animator.start();
    }
    
    /**
     * 拦截触摸事件，只有点击到实际内容区域才处理，其他区域不拦截
     */
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        // 检查触摸点是否在加载布局、错误布局或网络状态布局内
        if (isInViewBounds(loadingLayout, event) || 
            isInViewBounds(errorLayout, event) || 
            isInViewBounds(networkStatusLayout, event)) {
            return super.onTouchEvent(event);
        }
        
        // 如果不在任何内容区域内，则不拦截事件，让事件传递给下层视图
        return false;
    }
    
    /**
     * 检查触摸点是否在视图内
     */
    private boolean isInViewBounds(View view, MotionEvent event) {
        if (view.getVisibility() != VISIBLE) return false;
        
        int[] location = new int[2];
        view.getLocationOnScreen(location);
        
        return event.getRawX() >= location[0] && 
               event.getRawX() <= location[0] + view.getWidth() &&
               event.getRawY() >= location[1] && 
               event.getRawY() <= location[1] + view.getHeight();
    }
    
    /**
     * 设置加载文本
     * @param text 加载文本
     */
    public void setLoadingText(String text) {
        loadingText.setText(text);
    }
    
    /**
     * 设置加载详情文本
     * @param detailText 加载详情文本
     */
    public void setLoadingDetailText(String detailText) {
        if (detailText == null || detailText.isEmpty()) {
            loadingDetailText.setVisibility(GONE);
        } else {
            loadingDetailText.setText(detailText);
            loadingDetailText.setVisibility(VISIBLE);
        }
    }
    
    /**
     * 设置错误文本
     * @param text 错误文本
     */
    public void setErrorText(String text) {
        errorText.setText(text);
    }
    
    /**
     * 设置错误详情文本
     * @param detailText 错误详情文本
     */
    public void setErrorDetailText(String detailText) {
        if (detailText == null || detailText.isEmpty()) {
            errorDetailText.setVisibility(GONE);
        } else {
            errorDetailText.setText(detailText);
            errorDetailText.setVisibility(VISIBLE);
        }
    }
    
    /**
     * 设置重试回调
     * @param callback 重试回调
     */
    public void setRetryCallback(OnClickCallback callback) {
        this.retryCallback = callback;
    }
    
    /**
     * 显示网络状态提示
     * @param status 网络状态文本
     * @param isError 是否为错误状态
     */
    public void showNetworkStatus(String status, boolean isError) {
        networkText.setText(status);
        
        // 设置错误状态的颜色
        if (isError) {
            networkStatusLayout.setBackgroundResource(R.drawable.shape_network_status_error);
        } else {
            networkStatusLayout.setBackgroundResource(R.drawable.shape_network_status);
        }
        
        // 如果未显示，则显示
        if (!isNetworkStatusShowing) {
            isNetworkStatusShowing = true;
            networkStatusLayout.setVisibility(VISIBLE);
            networkStatusLayout.setAlpha(0f);
            
            ObjectAnimator animator = ObjectAnimator.ofFloat(networkStatusLayout, "alpha", 0f, 1f);
            animator.setDuration(ANIM_DURATION);
            animator.start();
            
            // 3秒后自动隐藏
            postDelayed(this::hideNetworkStatus, 3000);
        }
    }
    
    /**
     * 隐藏网络状态提示
     */
    public void hideNetworkStatus() {
        if (!isNetworkStatusShowing) return;
        isNetworkStatusShowing = false;
        
        ObjectAnimator animator = ObjectAnimator.ofFloat(networkStatusLayout, "alpha", 1f, 0f);
        animator.setDuration(ANIM_DURATION);
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                networkStatusLayout.setVisibility(GONE);
            }
        });
        animator.start();
    }
    
    /**
     * 显示预加载进度
     * @param show 是否显示
     */
    public void showPreloadProgress(boolean show) {
        preloadProgressBar.setVisibility(show ? VISIBLE : GONE);
    }
    
    /**
     * 设置预加载进度
     * @param progress 进度值(0-100)
     */
    public void setPreloadProgress(int progress) {
        if (preloadProgressBar.getVisibility() != VISIBLE) {
            preloadProgressBar.setVisibility(VISIBLE);
        }
        
        preloadProgressBar.setProgress(progress);
    }
    
    /**
     * 快捷显示错误状态
     * @param errorMsg 错误信息
     * @param detailMsg 详细错误信息
     */
    public void showError(String errorMsg, String detailMsg) {
        setErrorText(errorMsg);
        setErrorDetailText(detailMsg);
        setState(State.ERROR);
    }
    
    /**
     * 快捷显示加载状态
     * @param loadingMsg 加载信息
     * @param detailMsg 详细加载信息
     */
    public void showLoading(String loadingMsg, String detailMsg) {
        setLoadingText(loadingMsg);
        setLoadingDetailText(detailMsg);
        setState(State.LOADING);
    }
    
    /**
     * 设置加载状态
     * @param state 加载状态
     */
    public void setLoadingState(State state) {
        setState(state);
    }
    
    /**
     * 设置重试监听器
     * @param listener 重试回调
     */
    public void setOnRetryListener(OnClickCallback listener) {
        this.retryCallback = listener;
    }
    
    /**
     * 点击回调接口
     */
    public interface OnClickCallback {
        void onClick();
    }
    
    /**
     * 显示Toast样式的简短提示，不遮挡内容
     * @param message 提示消息
     * @param isError 是否是错误信息
     */
    public void showToast(String message, boolean isError) {
        if (message == null || message.trim().isEmpty()) {
            return;
        }
        
        // 使用Android原生Toast，不影响界面交互
        Toast toast = Toast.makeText(getContext(), message, Toast.LENGTH_SHORT);
        
        // 可以自定义Toast位置，例如显示在底部
        // toast.setGravity(Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL, 0, 200);
        
        toast.show();
    }
} 