package com.github.tvbox.osc.util;

import android.content.Context;
import android.text.TextUtils;
import android.widget.Toast;

import com.github.tvbox.osc.api.ApiConfig;
import com.github.tvbox.osc.base.App;
import com.orhanobut.hawk.Hawk;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 本地配置文件管理器
 * 用于管理本地JSON配置文件的添加、读取、验证等功能
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
public class LocalConfigManager {
    private static final String TAG = "LocalConfigManager";
    private static final String LOCAL_CONFIG_DIR = "local_configs";
    private static final String LOCAL_CONFIG_LIST_KEY = "local_config_list";
    
    /**
     * 本地配置文件信息
     */
    public static class LocalConfigInfo {
        public String name;        // 配置名称
        public String filePath;    // 文件路径
        public String description; // 描述
        public long createTime;    // 创建时间
        public long fileSize;      // 文件大小
        public boolean isValid;    // 是否有效
        
        public LocalConfigInfo(String name, String filePath, String description) {
            this.name = name;
            this.filePath = filePath;
            this.description = description;
            this.createTime = System.currentTimeMillis();
            this.isValid = true;
        }
    }
    
    /**
     * 获取本地配置目录
     */
    public static File getLocalConfigDir() {
        File dir = new File(App.getInstance().getFilesDir(), LOCAL_CONFIG_DIR);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dir;
    }
    
    /**
     * 添加本地配置文件
     * @param sourceFile 源文件
     * @param configName 配置名称
     * @param description 描述
     * @return 是否成功
     */
    public static boolean addLocalConfig(File sourceFile, String configName, String description) {
        if (sourceFile == null || !sourceFile.exists()) {
            LOG.e("源文件不存在");
            return false;
        }
        
        if (TextUtils.isEmpty(configName)) {
            configName = sourceFile.getName();
        }
        
        try {
            // 验证JSON格式
            String content = readFileContent(sourceFile);
            if (!isValidConfigJson(content)) {
                LOG.e("配置文件格式无效");
                return false;
            }
            
            // 复制文件到本地配置目录
            File targetFile = new File(getLocalConfigDir(), configName + ".json");
            if (copyFile(sourceFile, targetFile)) {
                // 保存配置信息
                LocalConfigInfo configInfo = new LocalConfigInfo(configName, targetFile.getAbsolutePath(), description);
                configInfo.fileSize = targetFile.length();
                saveLocalConfigInfo(configInfo);
                
                LOG.i("本地配置添加成功: " + configName);
                return true;
            }
        } catch (Exception e) {
            LOG.e("添加本地配置失败: " + e.getMessage());
        }
        
        return false;
    }
    
    /**
     * 从文本内容创建本地配置
     * @param jsonContent JSON内容
     * @param configName 配置名称
     * @param description 描述
     * @return 是否成功
     */
    public static boolean createLocalConfigFromText(String jsonContent, String configName, String description) {
        if (TextUtils.isEmpty(jsonContent) || TextUtils.isEmpty(configName)) {
            return false;
        }
        
        try {
            // 验证JSON格式
            if (!isValidConfigJson(jsonContent)) {
                LOG.e("配置内容格式无效");
                return false;
            }
            
            // 保存到文件
            File targetFile = new File(getLocalConfigDir(), configName + ".json");
            if (writeFileContent(targetFile, jsonContent)) {
                // 保存配置信息
                LocalConfigInfo configInfo = new LocalConfigInfo(configName, targetFile.getAbsolutePath(), description);
                configInfo.fileSize = targetFile.length();
                saveLocalConfigInfo(configInfo);
                
                LOG.i("本地配置创建成功: " + configName);
                return true;
            }
        } catch (Exception e) {
            LOG.e("创建本地配置失败: " + e.getMessage());
        }
        
        return false;
    }
    
    /**
     * 获取本地配置列表
     */
    public static List<LocalConfigInfo> getLocalConfigList() {
        List<LocalConfigInfo> configs = Hawk.get(LOCAL_CONFIG_LIST_KEY, new ArrayList<LocalConfigInfo>());
        
        // 验证文件是否还存在
        List<LocalConfigInfo> validConfigs = new ArrayList<>();
        for (LocalConfigInfo config : configs) {
            File file = new File(config.filePath);
            if (file.exists()) {
                config.isValid = true;
                config.fileSize = file.length();
                validConfigs.add(config);
            } else {
                LOG.w("配置文件不存在，已移除: " + config.name);
            }
        }
        
        // 更新列表
        if (validConfigs.size() != configs.size()) {
            Hawk.put(LOCAL_CONFIG_LIST_KEY, validConfigs);
        }
        
        return validConfigs;
    }
    
    /**
     * 删除本地配置
     */
    public static boolean deleteLocalConfig(LocalConfigInfo configInfo) {
        try {
            File file = new File(configInfo.filePath);
            if (file.exists()) {
                file.delete();
            }
            
            List<LocalConfigInfo> configs = getLocalConfigList();
            configs.removeIf(config -> config.filePath.equals(configInfo.filePath));
            Hawk.put(LOCAL_CONFIG_LIST_KEY, configs);
            
            LOG.i("本地配置删除成功: " + configInfo.name);
            return true;
        } catch (Exception e) {
            LOG.e("删除本地配置失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 使用本地配置
     */
    public static String useLocalConfig(LocalConfigInfo configInfo) {
        try {
            File file = new File(configInfo.filePath);
            if (!file.exists()) {
                LOG.e("配置文件不存在: " + configInfo.filePath);
                return null;
            }
            
            String content = readFileContent(file);
            if (isValidConfigJson(content)) {
                String localUrl = "clan://localhost/" + file.getName();
                LOG.i("使用本地配置: " + configInfo.name + " -> " + localUrl);
                return localUrl;
            } else {
                LOG.e("配置文件格式无效: " + configInfo.name);
                return null;
            }
        } catch (Exception e) {
            LOG.e("使用本地配置失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 验证JSON配置格式
     */
    private static boolean isValidConfigJson(String jsonContent) {
        if (TextUtils.isEmpty(jsonContent)) {
            return false;
        }
        
        try {
            // 基本JSON格式验证
            com.google.gson.JsonParser.parseString(jsonContent);
            
            // 检查必要字段
            return jsonContent.contains("sites") || jsonContent.contains("spider") || jsonContent.contains("parses");
        } catch (Exception e) {
            LOG.e("JSON格式验证失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 保存配置信息
     */
    private static void saveLocalConfigInfo(LocalConfigInfo configInfo) {
        List<LocalConfigInfo> configs = getLocalConfigList();
        
        // 检查是否已存在同名配置
        configs.removeIf(config -> config.name.equals(configInfo.name));
        configs.add(0, configInfo); // 添加到列表开头
        
        // 限制最大数量
        if (configs.size() > 50) {
            configs = configs.subList(0, 50);
        }
        
        Hawk.put(LOCAL_CONFIG_LIST_KEY, configs);
    }
    
    /**
     * 读取文件内容
     */
    private static String readFileContent(File file) throws IOException {
        FileInputStream fis = new FileInputStream(file);
        byte[] buffer = new byte[(int) file.length()];
        fis.read(buffer);
        fis.close();
        return new String(buffer, StandardCharsets.UTF_8);
    }
    
    /**
     * 写入文件内容
     */
    private static boolean writeFileContent(File file, String content) {
        try {
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(content.getBytes(StandardCharsets.UTF_8));
            fos.close();
            return true;
        } catch (IOException e) {
            LOG.e("写入文件失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 复制文件
     */
    private static boolean copyFile(File source, File target) {
        try {
            FileInputStream fis = new FileInputStream(source);
            FileOutputStream fos = new FileOutputStream(target);
            
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fis.read(buffer)) > 0) {
                fos.write(buffer, 0, length);
            }
            
            fis.close();
            fos.close();
            return true;
        } catch (IOException e) {
            LOG.e("复制文件失败: " + e.getMessage());
            return false;
        }
    }
}
