package com.github.tvbox.osc.player;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.github.tvbox.osc.util.LOG;
import com.github.tvbox.osc.util.PlayerHelper;
import com.github.tvbox.osc.player.PlayerConfig;

import xyz.doikki.videoplayer.player.AbstractPlayer;
import xyz.doikki.videoplayer.player.VideoView;

public class MyVideoView extends VideoView {
    public MyVideoView(@NonNull Context context) {
        super(context, null);
    }

    public MyVideoView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs, 0);
    }

    public MyVideoView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public AbstractPlayer getMediaPlayer() {
        return mMediaPlayer;
    }

    /**
     * 设置播放器配置
     */
    public void setPlayerConfig(PlayerConfig config) {
        if (config == null) return;
        
        try {
            // 设置播放器类型
            int playerType = config.getPlayerType();
            if (playerType >= 0) {
                setPlayerFactory(PlayerHelper.getPlayerFactory(playerType));
            }
            
            // 设置渲染器类型
            int renderType = config.getRenderType();
            if (renderType >= 0) {
                setRenderViewFactory(PlayerHelper.getRenderViewFactory(renderType));
            }
            
            // 设置缩放类型
            int scaleType = config.getScaleType();
            if (scaleType >= 0) {
                setScreenScaleType(scaleType);
            }
            
            // 设置播放速度
            float speed = config.getSpeed();
            if (speed > 0) {
                setSpeed(speed);
            }
        } catch (Exception e) {
            LOG.e("设置播放器配置失败", e);
        }
    }
}
