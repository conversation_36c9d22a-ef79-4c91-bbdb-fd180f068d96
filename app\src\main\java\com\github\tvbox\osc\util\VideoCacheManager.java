package com.github.tvbox.osc.util;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.github.tvbox.osc.base.App;
import com.github.tvbox.osc.cache.CacheManager;
import com.github.tvbox.osc.util.HawkConfig;
import com.orhanobut.hawk.Hawk;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 专门用于视频文件的缓存管理
 * 支持分片缓存、索引管理和高效读写
 */
public class VideoCacheManager {
    private static final String TAG = "VideoCacheManager";
    
    // 单例实例
    private static volatile VideoCacheManager instance;
    
    // 缓存配置参数
    private static final long DEFAULT_MAX_SIZE = 2L * 1024 * 1024 * 1024; // 2GB
    private static final long DEFAULT_MAX_FILE_SIZE = 512L * 1024 * 1024; // 512MB
    private static final int DEFAULT_READ_BUFFER_SIZE = 64 * 1024; // 64KB
    private static final int DEFAULT_WRITE_BUFFER_SIZE = 64 * 1024; // 64KB
    
    // 缓存目录和索引文件
    private static final String CACHE_DIR_NAME = "videocache";
    private static final String INDEX_FILE_NAME = "vidcache.idx";
    
    // 缓存操作执行器
    private final ExecutorService executor;
    private final Handler mainHandler;
    
    // 缓存文件和索引管理
    private final File cacheDir;
    private final ConcurrentHashMap<String, CacheInfo> cacheIndex;
    private long currentCacheSize = 0; // 当前已用缓存大小
    
    // 当前正在访问的缓存项
    private final Map<String, Long> activeAccessTimes = new ConcurrentHashMap<>();
    
    // 锁对象，用于同步
    private final Object lock = new Object();
    
    // 是否已初始化
    private boolean initialized = false;
    
    /**
     * 私有构造函数
     */
    private VideoCacheManager() {
        // 创建线程池和处理器
        executor = Executors.newFixedThreadPool(3);
        mainHandler = new Handler(Looper.getMainLooper());
        
        // 初始化缓存目录
        cacheDir = new File(App.getInstance().getExternalCacheDir(), CACHE_DIR_NAME);
        if (!cacheDir.exists()) {
            cacheDir.mkdirs();
        }
        
        // 初始化缓存索引
        cacheIndex = new ConcurrentHashMap<>();
        
        // 加载索引文件
        loadIndex();
        
        // 计算当前缓存大小
        calculateCurrentSize();
        
        // 打印缓存状态
        printStatus();
        
        initialized = true;
    }
    
    /**
     * 获取单例实例
     */
    public static VideoCacheManager getInstance() {
        if (instance == null) {
            synchronized (VideoCacheManager.class) {
                if (instance == null) {
                    instance = new VideoCacheManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 打印缓存状态
     */
    private void printStatus() {
        long maxSize = getMaxCacheSize();
        int fileCount = cacheIndex.size();
        long usedPercentage = currentCacheSize * 100 / Math.max(1, maxSize);
        
        Log.d(TAG, "┌─────────────────────────────────────┐");
        Log.d(TAG, "│          视频缓存管理器状态           │");
        Log.d(TAG, "├─────────────────────────────────────┤");
        Log.d(TAG, String.format("│ 已用空间: %5.2f MB / %5.2f MB (%d%%)   │", 
                                 currentCacheSize / 1024.0 / 1024.0, 
                                 maxSize / 1024.0 / 1024.0, 
                                 usedPercentage));
        Log.d(TAG, String.format("│ 缓存文件数: %d                      │", fileCount));
        Log.d(TAG, "└─────────────────────────────────────┘");
    }
    
    /**
     * 获取最大缓存大小
     */
    public long getMaxCacheSize() {
        try {
            // 从设置中获取，默认2GB
            long configSize = Hawk.get(HawkConfig.VIDEO_CACHE_MAX_SIZE, DEFAULT_MAX_SIZE);
            
            // 如果值小于100MB，认为单位是GB，需要转换
            if (configSize > 0 && configSize < 100 * 1024 * 1024) {
                configSize = configSize * 1024 * 1024 * 1024;
            }
            
            return configSize;
        } catch (Exception e) {
            Log.e(TAG, "获取最大缓存大小出错，使用默认值", e);
            return DEFAULT_MAX_SIZE;
        }
    }
    
    /**
     * 获取单个文件最大大小
     */
    public long getMaxFileCacheSize() {
        try {
            // 从设置中获取，默认512MB
            return Hawk.get(HawkConfig.VIDEO_FILE_CACHE_MAX_SIZE, DEFAULT_MAX_FILE_SIZE);
        } catch (Exception e) {
            return DEFAULT_MAX_FILE_SIZE;
        }
    }
    
    /**
     * 计算当前已用缓存大小
     */
    private void calculateCurrentSize() {
        long size = 0;
        for (CacheInfo info : cacheIndex.values()) {
            size += info.size;
        }
        currentCacheSize = size;
    }
    
    /**
     * 加载索引文件
     */
    @SuppressWarnings("unchecked")
    private void loadIndex() {
        File indexFile = new File(cacheDir, INDEX_FILE_NAME);
        if (!indexFile.exists()) {
            return;
        }
        
        try {
            // 从CacheManager读取索引文件
            Map<String, CacheInfo> index = (Map<String, CacheInfo>) 
                CacheManager.getInstance().getCache(CACHE_DIR_NAME + "_index");
            
            if (index != null) {
                cacheIndex.putAll(index);
                Log.d(TAG, "成功加载缓存索引，包含 " + index.size() + " 个条目");
            }
        } catch (Exception e) {
            Log.e(TAG, "加载缓存索引出错", e);
        }
    }
    
    /**
     * 保存索引文件
     */
    private void saveIndex() {
        try {
            // 使用CacheManager存储索引
            CacheManager.getInstance().save(CACHE_DIR_NAME + "_index", cacheIndex, 
                                          CacheManager.CACHE_TYPE_VIDEO);
            Log.d(TAG, "已保存缓存索引，包含 " + cacheIndex.size() + " 个条目");
        } catch (Exception e) {
            Log.e(TAG, "保存缓存索引出错", e);
        }
    }
    
    /**
     * 缓存整个视频文件
     * @param url 视频URL
     * @param data 视频数据
     * @param callback 缓存回调
     */
    public void cacheVideo(String url, byte[] data, CacheCallback callback) {
        if (data == null || data.length == 0 || TextUtils.isEmpty(url)) {
            if (callback != null) {
                callback.onError(url, "无效的视频数据");
            }
            return;
        }
        
        // 生成缓存键
        final String cacheKey = generateCacheKey(url);
        
        // 检查最大文件大小
        if (data.length > getMaxFileCacheSize()) {
            if (callback != null) {
                callback.onError(url, "视频文件过大，超过最大限制");
            }
            return;
        }
        
        // 在后台线程中执行缓存操作
        executor.execute(() -> {
            try {
                // 检查缓存空间
                ensureSpaceAvailable(data.length);
                
                // 存储文件
                File cacheFile = new File(cacheDir, cacheKey);
                try (FileOutputStream fos = new FileOutputStream(cacheFile)) {
                    fos.write(data);
                    fos.flush();
                }
                
                // 添加到索引
                CacheInfo entry = new CacheInfo(url, cacheKey, data.length);
                cacheIndex.put(url, entry);
                
                // 更新缓存大小
                currentCacheSize += data.length;
                
                // 保存索引
                saveIndex();
                
                // 回调成功
                if (callback != null) {
                    mainHandler.post(() -> callback.onSuccess(url, cacheFile.getAbsolutePath()));
                }
                
                Log.d(TAG, "成功缓存视频文件: " + url + ", 大小: " + (data.length / 1024) + "KB");
            } catch (Exception e) {
                Log.e(TAG, "缓存视频文件出错: " + url, e);
                if (callback != null) {
                    mainHandler.post(() -> callback.onError(url, e.getMessage()));
                }
            }
        });
    }
    
    /**
     * 缓存视频片段
     * @param url 视频URL
     * @param segment 片段数据
     * @param startPos 片段在文件中的起始位置
     * @param callback 缓存回调
     */
    public void cacheVideoSegment(String url, byte[] segment, long startPos, CacheCallback callback) {
        if (segment == null || segment.length == 0 || TextUtils.isEmpty(url)) {
            if (callback != null) {
                callback.onError(url, "无效的视频片段数据");
            }
            return;
        }
        
        // 生成缓存键
        final String cacheKey = generateCacheKey(url);
        
        // 在后台线程中执行缓存操作
        executor.execute(() -> {
            try {
                // 检查缓存索引
                CacheInfo entry = cacheIndex.get(url);
                if (entry == null) {
                    // 新建缓存条目
                    entry = new CacheInfo(url, cacheKey, 0);
                    cacheIndex.put(url, entry);
                }
                
                // 确保有足够空间
                ensureSpaceAvailable(segment.length);
                
                // 缓存文件
                File cacheFile = new File(cacheDir, cacheKey);
                
                // 添加片段
                try (RandomAccessFile raf = new RandomAccessFile(cacheFile, "rw")) {
                    raf.seek(startPos);
                    raf.write(segment);
                    
                    // 更新片段索引
                    entry.addSegment(startPos, segment.length);
                    
                    // 更新文件大小
                    long oldSize = entry.getSize();
                    entry.updateSize(Math.max(raf.length(), entry.getSize()));
                    
                    // 更新缓存总大小
                    currentCacheSize += (entry.getSize() - oldSize);
                }
                
                // 保存索引
                saveIndex();
                
                // 回调成功
                if (callback != null) {
                    mainHandler.post(() -> callback.onSuccess(url, cacheFile.getAbsolutePath()));
                }
                
                Log.d(TAG, "成功缓存视频片段: " + url + 
                      ", 位置: " + startPos + 
                      ", 大小: " + (segment.length / 1024) + "KB");
            } catch (Exception e) {
                Log.e(TAG, "缓存视频片段出错: " + url, e);
                if (callback != null) {
                    mainHandler.post(() -> callback.onError(url, e.getMessage()));
                }
            }
        });
    }
    
    /**
     * 读取缓存的视频文件
     * @param url 视频URL
     * @param callback 读取回调
     */
    public void getVideo(String url, GetCallback callback) {
        if (TextUtils.isEmpty(url) || callback == null) {
            return;
        }
        
        // 在后台线程中执行读取操作
        executor.execute(() -> {
            try {
                // 查找缓存索引
                CacheInfo entry = cacheIndex.get(url);
                if (entry == null) {
                    mainHandler.post(() -> callback.onNotFound(url));
                    return;
                }
                
                // 更新访问时间
                updateAccessTime(url);
                
                // 读取文件
                File cacheFile = new File(cacheDir, entry.getCacheKey());
                if (!cacheFile.exists()) {
                    // 文件不存在，从索引中移除
                    cacheIndex.remove(url);
                    saveIndex();
                    mainHandler.post(() -> callback.onNotFound(url));
                    return;
                }
                
                // 检查是否是完整文件
                if (entry.isComplete()) {
                    // 读取整个文件
                    byte[] data = new byte[(int)cacheFile.length()];
                    try (FileInputStream fis = new FileInputStream(cacheFile)) {
                        int read = fis.read(data);
                        if (read != data.length) {
                            throw new IOException("读取缓存文件不完整");
                        }
                    }
                    
                    // 回调成功
                    mainHandler.post(() -> callback.onSuccess(url, data));
                } else {
                    // 部分缓存，返回文件路径和段信息
                    Map<Long, Integer> segments = entry.getSegments();
                    mainHandler.post(() -> callback.onPartialData(url, cacheFile.getAbsolutePath(), segments));
                }
            } catch (Exception e) {
                Log.e(TAG, "读取缓存视频文件出错: " + url, e);
                mainHandler.post(() -> callback.onError(url, e.getMessage()));
            }
        });
    }
    
    /**
     * 读取缓存的视频文件特定范围
     * @param url 视频URL
     * @param start 起始位置
     * @param length 长度
     * @param callback 读取回调
     */
    public void getVideoRange(String url, long start, int length, GetCallback callback) {
        if (TextUtils.isEmpty(url) || callback == null) {
            return;
        }
        
        // 在后台线程中执行读取操作
        executor.execute(() -> {
            try {
                // 查找缓存索引
                CacheInfo entry = cacheIndex.get(url);
                if (entry == null) {
                    mainHandler.post(() -> callback.onNotFound(url));
                    return;
                }
                
                // 更新访问时间
                updateAccessTime(url);
                
                // 读取文件
                File cacheFile = new File(cacheDir, entry.getCacheKey());
                if (!cacheFile.exists()) {
                    // 文件不存在，从索引中移除
                    cacheIndex.remove(url);
                    saveIndex();
                    mainHandler.post(() -> callback.onNotFound(url));
                    return;
                }
                
                // 检查范围是否在缓存的段内
                if (entry.hasSegmentAt(start, length)) {
                    // 读取指定范围
                    byte[] data = new byte[length];
                    try (RandomAccessFile raf = new RandomAccessFile(cacheFile, "r")) {
                        raf.seek(start);
                        int read = raf.read(data, 0, length);
                        if (read != length) {
                            throw new IOException("读取缓存文件不完整");
                        }
                    }
                    
                    // 回调成功
                    mainHandler.post(() -> callback.onRangeSuccess(url, data, start));
                } else {
                    // 请求的范围不在缓存中
                    mainHandler.post(() -> callback.onRangeNotFound(url, start, length));
                }
            } catch (Exception e) {
                Log.e(TAG, "读取缓存视频范围出错: " + url, e);
                mainHandler.post(() -> callback.onError(url, e.getMessage()));
            }
        });
    }
    
    /**
     * 删除缓存的视频文件
     * @param url 视频URL
     */
    public void removeVideo(String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        
        // 在后台线程中执行删除操作
        executor.execute(() -> {
            // 查找缓存索引
            CacheInfo entry = cacheIndex.get(url);
            if (entry == null) {
                return;
            }
            
            // 删除文件
            File cacheFile = new File(cacheDir, entry.getCacheKey());
            if (cacheFile.exists()) {
                if (cacheFile.delete()) {
                    Log.d(TAG, "成功删除缓存视频文件: " + url);
                } else {
                    Log.e(TAG, "删除缓存视频文件失败: " + url);
                }
            }
            
            // 更新缓存大小
            currentCacheSize -= entry.getSize();
            
            // 从索引中移除
            cacheIndex.remove(url);
            activeAccessTimes.remove(url);
            
            // 保存索引
            saveIndex();
        });
    }
    
    /**
     * 清理所有缓存
     */
    public void clearAll() {
        executor.execute(() -> {
            // 遍历删除所有文件
            for (File file : cacheDir.listFiles()) {
                if (!file.getName().equals(INDEX_FILE_NAME)) {
                    file.delete();
                }
            }
            
            // 清空索引
            cacheIndex.clear();
            activeAccessTimes.clear();
            currentCacheSize = 0;
            
            // 保存索引
            saveIndex();
            
            Log.d(TAG, "成功清理所有视频缓存");
        });
    }
    
    /**
     * 按大小清理缓存
     * @param spaceNeeded 需要释放的空间大小
     */
    public void trimCache(long spaceNeeded) {
        executor.execute(() -> {
            // 检查是否需要清理
            long maxSize = getMaxCacheSize();
            if (currentCacheSize + spaceNeeded <= maxSize) {
                return; // 空间足够，不需要清理
            }
            
            // 需要释放的空间
            long needToFree = currentCacheSize + spaceNeeded - maxSize;
            
            // 按最后访问时间排序
            List<Map.Entry<String, CacheInfo>> entries = new ArrayList<>(cacheIndex.entrySet());
            entries.sort((a, b) -> {
                long timeA = a.getValue().getLastAccessTime();
                long timeB = b.getValue().getLastAccessTime();
                return Long.compare(timeA, timeB); // 最早访问的排在前面
            });
            
            // 释放空间直到满足需求
            long freed = 0;
            for (Map.Entry<String, CacheInfo> entry : entries) {
                if (freed >= needToFree) {
                    break;
                }
                
                // 跳过当前正在访问的缓存
                if (activeAccessTimes.containsKey(entry.getKey())) {
                    continue;
                }
                
                // 删除这个缓存
                removeVideo(entry.getKey());
                freed += entry.getValue().getSize();
            }
            
            Log.d(TAG, String.format("缓存清理完成，释放了 %d KB 空间", freed / 1024));
        });
    }
    
    /**
     * 确保有足够的缓存空间
     * @param spaceNeeded 需要的空间大小
     */
    private void ensureSpaceAvailable(long spaceNeeded) {
        long maxSize = getMaxCacheSize();
        if (currentCacheSize + spaceNeeded > maxSize) {
            // 需要释放空间
            trimCache(spaceNeeded);
        }
    }
    
    /**
     * 更新文件访问时间
     * @param url 视频URL
     */
    private void updateAccessTime(String url) {
        long now = System.currentTimeMillis();
        activeAccessTimes.put(url, now);
        
        CacheInfo entry = cacheIndex.get(url);
        if (entry != null) {
            entry.setLastAccessTime(now);
        }
    }
    
    /**
     * 生成缓存文件名
     * @param url 视频URL
     * @return 缓存键
     */
    private String generateCacheKey(String url) {
        return MD5.encode(url);
    }
    
    /**
     * 视频缓存条目
     * 记录视频缓存的索引信息
     */
    public static class CacheInfo {
        private final String url;         // 原始URL
        private final String cacheKey;    // 缓存键
        private long size;                // 文件大小
        private boolean isComplete;       // 是否是完整文件
        private Map<Long, Integer> segments; // 已缓存片段位置和大小
        private long createTime;          // 创建时间
        private long lastAccessTime;      // 最后访问时间
        
        public CacheInfo(String url, String cacheKey, long size) {
            this.url = url;
            this.cacheKey = cacheKey;
            this.size = size;
            this.isComplete = false;
            this.segments = new HashMap<>();
            this.createTime = System.currentTimeMillis();
            this.lastAccessTime = this.createTime;
        }
        
        public String getUrl() {
            return url;
        }
        
        public String getCacheKey() {
            return cacheKey;
        }
        
        public long getSize() {
            return size;
        }
        
        public boolean isComplete() {
            return isComplete;
        }
        
        public void setComplete(boolean complete) {
            isComplete = complete;
        }
        
        public long getCreateTime() {
            return createTime;
        }
        
        public long getLastAccessTime() {
            return lastAccessTime;
        }
        
        public void setLastAccessTime(long time) {
            this.lastAccessTime = time;
        }
        
        public void updateSize(long size) {
            this.size = size;
        }
        
        public Map<Long, Integer> getSegments() {
            return segments;
        }
        
        public void addSegment(long position, int length) {
            segments.put(position, length);
        }
        
        /**
         * 检查指定范围是否在缓存的段内
         * @param position 起始位置
         * @param length 长度
         * @return 是否有缓存
         */
        public boolean hasSegmentAt(long position, int length) {
            // 如果是完整文件，总是返回true
            if (isComplete) {
                return true;
            }
            
            // 检查是否有完全覆盖请求范围的片段
            for (Map.Entry<Long, Integer> segment : segments.entrySet()) {
                long segStart = segment.getKey();
                int segLength = segment.getValue();
                
                if (position >= segStart && position + length <= segStart + segLength) {
                    return true;
                }
            }
            
            return false;
        }
    }
    
    /**
     * 缓存回调接口
     */
    public interface CacheCallback {
        /**
         * 缓存成功回调
         * @param url 视频URL
         * @param filePath 缓存文件路径
         */
        void onSuccess(String url, String filePath);
        
        /**
         * 缓存错误回调
         * @param url 视频URL
         * @param error 错误信息
         */
        void onError(String url, String error);
    }
    
    /**
     * 获取缓存回调接口
     */
    public interface GetCallback {
        /**
         * 成功获取完整文件
         * @param url 视频URL
         * @param data 文件数据
         */
        void onSuccess(String url, byte[] data);
        
        /**
         * 获取部分缓存数据
         * @param url 视频URL
         * @param filePath 缓存文件路径
         * @param segments 已缓存片段
         */
        void onPartialData(String url, String filePath, Map<Long, Integer> segments);
        
        /**
         * 成功获取指定范围
         * @param url 视频URL
         * @param data 范围数据
         * @param startPos 起始位置
         */
        void onRangeSuccess(String url, byte[] data, long startPos);
        
        /**
         * 指定范围不在缓存中
         * @param url 视频URL
         * @param startPos 起始位置
         * @param length 长度
         */
        void onRangeNotFound(String url, long startPos, int length);
        
        /**
         * 未找到缓存
         * @param url 视频URL
         */
        void onNotFound(String url);
        
        /**
         * 发生错误
         * @param url 视频URL
         * @param error 错误信息
         */
        void onError(String url, String error);
    }
    
    /**
     * 缓存整个视频文件
     * @param url 视频URL
     * @param data 视频数据
     */
    public void saveVideo(String url, byte[] data) {
        cacheVideo(url, data, null);
    }
} 