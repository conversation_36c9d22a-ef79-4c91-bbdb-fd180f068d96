package com.github.tvbox.osc.util;

import android.util.Log;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 视频流拦截器
 * 专门用于优化视频相关请求，提供断点续传支持和更长的超时设置
 * 根据视频格式和网络状态自动调整参数
 */
public class VideoStreamInterceptor implements Interceptor {
    private static final String TAG = "VideoStreamInterceptor";
    
    // 视频请求超时时间 (默认3分钟，实际会根据网络状况调整)
    private static final int DEFAULT_VIDEO_READ_TIMEOUT = 180; // 秒
    
    // HLS流超时时间 (HLS格式更短以便快速失败和切换)
    private static final int HLS_READ_TIMEOUT = 60; // 秒
    
    // 直播流超时设置 (为流媒体优化)
    private static final int LIVE_READ_TIMEOUT = 30; // 秒
    
    // 分片下载大小控制
    private static final int MIN_CHUNK_SIZE = 256 * 1024; // 256KB
    private static final int MAX_CHUNK_SIZE = 4 * 1024 * 1024; // 4MB
    
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        String url = request.url().toString().toLowerCase();
        
        // 判断是否为视频流请求
        if (isVideoRequest(url)) {
            LOG.i("处理视频流请求: " + url);
            
            // 确定视频类型
            VideoType videoType = determineVideoType(url);
            LOG.i("视频类型: " + videoType);
            
            // 获取当前网络信息
            NetworkUtils.NetworkType networkType = NetworkUtils.getInstance().getNetworkType();
            
            // 构建增强的请求
            Request.Builder requestBuilder = enhanceVideoRequest(request, videoType, networkType);
            
            // 设置适当的超时时间
            int readTimeout = determineReadTimeout(videoType, networkType);
            int connectTimeout = determineConnectTimeout(networkType);
            
            LOG.i("使用超时设置: 读取=" + readTimeout + "秒, 连接=" + connectTimeout + "秒");
            
            // 执行请求
            return chain
                .withReadTimeout(readTimeout, TimeUnit.SECONDS)
                .withConnectTimeout(connectTimeout, TimeUnit.SECONDS)
                .proceed(requestBuilder.build());
        }
        
        // 非视频请求正常处理
        return chain.proceed(request);
    }
    
    /**
     * 增强视频请求
     * 添加合适的请求头和参数
     */
    private Request.Builder enhanceVideoRequest(Request originalRequest, VideoType videoType, 
                                              NetworkUtils.NetworkType networkType) {
        Request.Builder builder = originalRequest.newBuilder();
        
        // 通用视频请求头
        builder.addHeader("Connection", "keep-alive");
        builder.addHeader("Accept-Encoding", "identity");  // 防止压缩视频流
        
        // 根据视频类型添加特定请求头
        switch (videoType) {
            case DASH:
                // DASH特定优化
                builder.addHeader("Accept", "application/dash+xml,video/*,audio/*");
                break;
                
            case HLS:
                // HLS特定优化
                builder.addHeader("Accept", "application/vnd.apple.mpegurl,application/x-mpegurl,audio/*,video/*");
                break;
                
            case MP4:
                // MP4特定优化，添加Range支持
                if (originalRequest.header("Range") == null) {
                    // 确定适当的预加载范围
                    int preloadSize = determinePreloadSize(networkType);
                    builder.addHeader("Range", "bytes=0-" + preloadSize);
                    LOG.i("添加MP4范围请求: 0-" + preloadSize);
                }
                break;
                
            case TS:
                // TS分片特定优化
                break;
                
            case LIVE:
                // 直播流特定优化
                builder.cacheControl(okhttp3.CacheControl.FORCE_NETWORK); // 不缓存直播流
                break;
                
            case OTHER:
            default:
                // 其他视频类型，添加通用Range支持
                if (originalRequest.header("Range") == null) {
                    builder.addHeader("Range", "bytes=0-");
                }
                break;
        }
        
        // 如果是低速网络，添加低质量偏好
        if (networkType == NetworkUtils.NetworkType.MOBILE) {
            String subType = NetworkUtils.getNetworkTypeName();
            if (subType.contains("2G") || subType.contains("3G")) {
                builder.addHeader("X-Prefer-Quality", "low"); // 自定义头，某些CDN可能支持
            }
        }
        
        return builder;
    }
    
    /**
     * 判断视频请求类型
     */
    private VideoType determineVideoType(String url) {
        url = url.toLowerCase();
        
        if (url.contains(".m3u8") || url.contains("/hls/") || url.contains("=m3u8")) {
            return VideoType.HLS;
        } else if (url.contains(".mpd") || url.contains("/dash/")) {
            return VideoType.DASH;
        } else if (url.contains(".mp4")) {
            return VideoType.MP4;
        } else if (url.contains(".ts")) {
            return VideoType.TS;
        } else if (url.contains("live") || url.contains("rtmp://") || 
                  url.contains("rtsp://") || url.contains("/live/")) {
            return VideoType.LIVE;
        } else {
            return VideoType.OTHER;
        }
    }
    
    /**
     * 确定读取超时时间
     */
    private int determineReadTimeout(VideoType videoType, NetworkUtils.NetworkType networkType) {
        // 基于视频类型的基础超时
        int baseTimeout;
        switch (videoType) {
            case HLS:
                baseTimeout = HLS_READ_TIMEOUT;
                break;
            case LIVE:
                baseTimeout = LIVE_READ_TIMEOUT;
                break;
            default:
                baseTimeout = DEFAULT_VIDEO_READ_TIMEOUT;
                break;
        }
        
        // 根据网络类型调整
        switch (networkType) {
            case WIFI:
            case ETHERNET:
                // WiFi或有线网络使用原有超时
                return baseTimeout;
            case MOBILE:
                // 移动网络稍微降低超时
                return (int)(baseTimeout * 0.8);
            case NONE:
            case UNKNOWN:
            default:
                // 未知网络降低超时
                return (int)(baseTimeout * 0.5);
        }
    }
    
    /**
     * 确定连接超时时间
     */
    private int determineConnectTimeout(NetworkUtils.NetworkType networkType) {
        // 根据网络类型调整连接超时
        switch (networkType) {
            case WIFI:
            case ETHERNET:
                return 15; // WiFi和有线网络用15秒
            case MOBILE:
                return 20; // 移动网络用20秒
            case NONE:
            case UNKNOWN:
            default:
                return 10; // 未知网络用10秒
        }
    }
    
    /**
     * 确定预加载大小
     */
    private int determinePreloadSize(NetworkUtils.NetworkType networkType) {
        // 根据网络类型确定初始预加载大小
        switch (networkType) {
            case WIFI:
                return 2 * 1024 * 1024; // WiFi 2MB
            case ETHERNET:
                return 4 * 1024 * 1024; // 有线 4MB
            case MOBILE:
                // 进一步根据移动网络类型细分
                String subType = NetworkUtils.getNetworkTypeName();
                if (subType.contains("5G")) {
                    return 2 * 1024 * 1024; // 5G 2MB
                } else if (subType.contains("4G") || subType.contains("LTE")) {
                    return 1 * 1024 * 1024; // 4G 1MB
                } else if (subType.contains("3G")) {
                    return 512 * 1024; // 3G 512KB
                } else {
                    return 256 * 1024; // 2G或其他 256KB
                }
            case NONE:
            case UNKNOWN:
            default:
                return 256 * 1024; // 未知网络 256KB
        }
    }
    
    /**
     * 判断是否为视频请求
     * @param url 请求URL
     * @return 是否为视频请求
     */
    private boolean isVideoRequest(String url) {
        return url.contains(".m3u8") || 
               url.contains(".ts") || 
               url.contains(".mp4") || 
               url.contains(".flv") ||
               url.contains(".avi") ||
               url.contains(".mkv") ||
               url.contains(".mov") ||
               url.contains(".mpd") ||
               url.contains(".m4s") ||
               url.contains("/hls/") ||
               url.contains("/dash/") ||
               url.contains("videoplayback") ||
               url.contains("video/") ||
               url.contains("play?") ||
               url.contains("=m3u8") ||
               url.contains("rtmp://") ||
               url.contains("rtsp://");
    }
    
    /**
     * 视频类型枚举
     */
    private enum VideoType {
        HLS,     // HLS流媒体
        DASH,    // DASH流媒体
        MP4,     // MP4文件
        TS,      // TS分片
        LIVE,    // 直播流
        OTHER    // 其他视频类型
    }
} 