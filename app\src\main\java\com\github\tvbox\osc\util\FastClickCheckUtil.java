package com.github.tvbox.osc.util;

import android.os.Handler;
import android.os.Looper;
import android.view.View;

import com.github.tvbox.osc.util.LOG;

/**
 * <AUTHOR>
 * @date :2020/12/22
 * @description:
 */
public class FastClickCheckUtil {
    // 记录上次点击时间
    private static long lastClickTime = 0;
    private static int lastViewId = -1;
    
    /**
     * 相同视图点击必须间隔0.5s才能有效
     *
     * @param view 目标视图
     * @return 是否允许点击
     */
    public static boolean check(View view) {
        return check(view, 100); // 降低为100ms以便更容易点击
    }

    /**
     * 设置间隔点击规则，配置间隔点击时间
     *
     * @param view  目标视图
     * @param mills 点击间隔时间（毫秒）
     * @return 是否允许点击
     */
    public static boolean check(final View view, int mills) {
        if (view == null) {
            return false;
        }
        
        int viewId = view.getId();
        long currentTime = System.currentTimeMillis();
        
        // 如果两次点击视图不同，始终允许通过
        if (viewId != lastViewId) {
            lastViewId = viewId;
            lastClickTime = currentTime;
            LOG.i("FastClickCheckUtil - 点击通过(不同视图)");
            return true;
        }
        
        // 如果两次点击间隔小于设定值，也允许处理，但记录日志
        // TV环境下需要更宽松的点击处理
        if (currentTime - lastClickTime < mills) {
            LOG.i("FastClickCheckUtil - 点击间隔短但仍然处理: " + (currentTime - lastClickTime) + "ms");
            lastClickTime = currentTime;
            return true; // 修改为直接返回true，允许所有点击
        }
        
        lastClickTime = currentTime;
        LOG.i("FastClickCheckUtil - 点击通过(时间间隔足够)");
        return true;
    }
}