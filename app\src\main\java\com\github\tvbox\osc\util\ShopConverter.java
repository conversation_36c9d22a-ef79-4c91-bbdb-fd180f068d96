package com.github.tvbox.osc.util;

import com.github.tvbox.osc.bean.Shop;
import com.github.tvbox.osc.beanry.ExchangeBean;

import java.util.ArrayList;
import java.util.List;

/**
 * 将API返回的ExchangeBean.MsgDTO转换为Shop对象的工具类
 */
public class ShopConverter {
    
    /**
     * 将ExchangeBean.MsgDTO列表转换为Shop列表
     * @param msgList API返回的商品列表
     * @return 转换后的Shop列表
     */
    public static List<Shop> convertToShopList(List<ExchangeBean.MsgDTO> msgList) {
        List<Shop> shopList = new ArrayList<>();
        
        if (msgList != null && !msgList.isEmpty()) {
            for (ExchangeBean.MsgDTO msgItem : msgList) {
                Shop shop = new Shop();
                shop.setId(msgItem.id);
                shop.setTitle(msgItem.name);
                shop.setPrice(msgItem.fen_num);
                shop.setRemarks(msgItem.vip_num != null ? msgItem.vip_num : "");
                // VIP状态在Shop类的setTitle方法中会自动设置
                shopList.add(shop);
            }
        }
        
        return shopList;
    }
} 