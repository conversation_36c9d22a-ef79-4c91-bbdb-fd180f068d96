package com.github.tvbox.osc.util;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Build;
import android.os.Debug;
import android.os.Handler;
import android.os.Looper;
import android.os.Process;
import android.util.Log;

import com.github.tvbox.osc.base.App;
import com.github.tvbox.osc.player.IjkMediaPlayer;
import com.github.tvbox.osc.player.controller.VodController;
import com.orhanobut.hawk.Hawk;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能监控系统
 * 提供应用性能、网络请求、内存使用和播放性能的监控功能
 */
public class PerformanceMonitor {
    private static final String TAG = "PerformanceMonitor";
    private static volatile PerformanceMonitor instance;
    
    // 监控线程
    private final ScheduledExecutorService scheduler;
    private final Handler mainHandler;
    
    // 应用性能监控
    private long frameStartTime = 0;
    private final List<Long> frameRenderTimes = new CopyOnWriteArrayList<>();
    private int frameCount = 0;
    private final AtomicLong totalRenderTime = new AtomicLong(0);
    private float averageFrameRate = 0;
    private final Map<String, Long> activityRenderTimes = new ConcurrentHashMap<>();
    
    // 内存监控
    private long lastMemoryCheckTime = 0;
    private final List<MemoryInfo> memoryHistory = new CopyOnWriteArrayList<>();
    private final int MAX_MEMORY_HISTORY = 120; // 保存2小时的内存记录（每分钟一次）
    
    // 网络监控（使用NetworkRequestManager提供的统计信息）
    
    // 播放性能监控
    private boolean isPlaying = false;
    private final Map<String, PlayerPerformanceInfo> playerPerformanceMap = new ConcurrentHashMap<>();
    private int bufferingCount = 0;
    private long totalBufferingTime = 0;
    private long lastBufferingStartTime = 0;
    
    // 监控配置
    private boolean isMonitoringEnabled = true;
    private int monitoringInterval = 5000; // 默认5秒检查一次
    private boolean isMemoryMonitoringEnabled = true;
    private boolean isNetworkMonitoringEnabled = true;
    private boolean isFrameMonitoringEnabled = true;
    private boolean isPlayerMonitoringEnabled = true;
    
    private PerformanceMonitor() {
        scheduler = Executors.newScheduledThreadPool(1);
        mainHandler = new Handler(Looper.getMainLooper());
        
        // 读取配置
        loadConfig();
        
        // 开始监控线程
        if (isMonitoringEnabled) {
            startMonitoring();
        }
    }
    
    private void loadConfig() {
        isMonitoringEnabled = Hawk.get("perf_monitor_enabled", true);
        monitoringInterval = Hawk.get("perf_monitor_interval", 5000);
        isMemoryMonitoringEnabled = Hawk.get("perf_monitor_memory_enabled", true);
        isNetworkMonitoringEnabled = Hawk.get("perf_monitor_network_enabled", true);
        isFrameMonitoringEnabled = Hawk.get("perf_monitor_frame_enabled", true);
        isPlayerMonitoringEnabled = Hawk.get("perf_monitor_player_enabled", true);
    }
    
    public static PerformanceMonitor getInstance() {
        if (instance == null) {
            synchronized (PerformanceMonitor.class) {
                if (instance == null) {
                    instance = new PerformanceMonitor();
                }
            }
        }
        return instance;
    }
    
    /**
     * 开始性能监控
     */
    public void startMonitoring() {
        if (!isMonitoringEnabled) return;
        
        try {
            // 加载配置
            loadConfig();
            
            // 创建定时任务
            scheduler.scheduleAtFixedRate(() -> {
                if (isMemoryMonitoringEnabled) {
                    checkMemoryUsage();
                }
                
                if (isPlayerMonitoringEnabled) {
                    checkPlayerPerformance();
                }
            }, 0, monitoringInterval, TimeUnit.MILLISECONDS);
            
            LOG.i("开始性能监控");
        } catch (Exception e) {
            // 忽略异常
        }
    }
    
    /**
     * 停止性能监控
     */
    public void stopMonitoring() {
        try {
            // 关闭调度器
            if (scheduler != null && !scheduler.isShutdown()) {
                scheduler.shutdown();
            }
            
            LOG.i("停止性能监控");
        } catch (Exception e) {
            // 忽略异常
        }
    }
    
    /**
     * 检查内存使用情况
     */
    private void checkMemoryUsage() {
        try {
            // 获取当前时间
            long currentTime = System.currentTimeMillis();
            
            // 不要频繁检查
            if (currentTime - lastMemoryCheckTime < 60000) { // 每分钟最多检查一次
                return;
            }
            
            lastMemoryCheckTime = currentTime;
            
            // 创建内存信息对象
            MemoryInfo memInfo = new MemoryInfo();
            memInfo.timestamp = currentTime;
            
            // 获取系统内存信息
            ActivityManager am = (ActivityManager) App.getInstance().getSystemService(Context.ACTIVITY_SERVICE);
            ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
            am.getMemoryInfo(memoryInfo);
            
            memInfo.totalMem = memoryInfo.totalMem;
            memInfo.availMem = memoryInfo.availMem;
            memInfo.threshold = memoryInfo.threshold;
            memInfo.lowMemory = memoryInfo.lowMemory;
            
            // 获取应用内存信息
            Runtime runtime = Runtime.getRuntime();
            memInfo.heapMaxMemory = runtime.maxMemory();
            memInfo.heapTotalMemory = runtime.totalMemory();
            memInfo.heapFreeMemory = runtime.freeMemory();
            memInfo.usedHeapMemory = memInfo.heapTotalMemory - memInfo.heapFreeMemory;
            
            // 获取进程内存
            int[] processMemory = getProcessMemoryInfo();
            memInfo.vss = processMemory[0]; // 虚拟内存大小（KB）
            memInfo.rss = processMemory[1]; // 物理内存占用（KB）
            
            // 尝试获取更详细的内存信息
            Debug.MemoryInfo[] debugMemoryInfos = am.getProcessMemoryInfo(new int[]{android.os.Process.myPid()});
            if (debugMemoryInfos != null && debugMemoryInfos.length > 0) {
                Debug.MemoryInfo debugMemoryInfo = debugMemoryInfos[0];
                memInfo.totalPss = debugMemoryInfo.getTotalPss(); // 总PSS（KB）
                try {
                    // 这些方法在旧版本可能不存在，使用反射来安全调用
                    Method getNativeMethod = Debug.MemoryInfo.class.getMethod("getNativePrivateDirty");
                    Method getJavaMethod = Debug.MemoryInfo.class.getMethod("getJavaHeap");
                    
                    memInfo.nativePss = (Integer) getNativeMethod.invoke(debugMemoryInfo); // 原生PSS（KB）
                    memInfo.dalvikPss = (Integer) getJavaMethod.invoke(debugMemoryInfo); // Java堆PSS（KB）
                } catch (Exception e) {
                    // 如果反射失败，使用其他方法估算
                    memInfo.nativePss = memInfo.totalPss / 3;
                    memInfo.dalvikPss = memInfo.totalPss / 3 * 2;
                }
            }
            
            // 添加到历史记录
            memoryHistory.add(memInfo);
            
            // 保持历史记录不超过最大限制
            if (memoryHistory.size() > MAX_MEMORY_HISTORY) {
                memoryHistory.remove(0);
            }
            
            // 内存不足警告
            long availMem = memInfo.availMem;
            long totalMem = memInfo.totalMem;
            if (availMem < totalMem * 0.1) { // 可用内存不足10%
                LOG.i("内存不足警告: 可用内存 " + (availMem / 1024 / 1024) + "MB, 总内存的 " 
                      + (availMem * 100 / totalMem) + "%");
            }
            
        } catch (Exception e) {
            // 忽略异常
        }
    }
    
    private int[] getProcessMemoryInfo() {
        int[] result = new int[2]; // [0]: VSS, [1]: RSS
        
        try {
            // 读取/proc/self/statm文件获取内存信息
            BufferedReader reader = new BufferedReader(new FileReader("/proc/self/statm"));
            String line = reader.readLine();
            reader.close();
            
            if (line != null && !line.isEmpty()) {
                String[] parts = line.split("\\s+");
                if (parts.length >= 2) {
                    // 第一个数是VSS，第二个是RSS，都是以页为单位
                    long vss = Long.parseLong(parts[0]);
                    long rss = Long.parseLong(parts[1]);
                    
                    // 转换为KB，页大小通常是4KB
                    int pageSize = getPageSize();
                    result[0] = (int) (vss * pageSize / 1024);
                    result[1] = (int) (rss * pageSize / 1024);
                }
            }
        } catch (Exception e) {
            LOG.e("读取进程内存信息失败: " + e.getMessage());
        }
        
        return result;
    }
    
    private int getPageSize() {
        int pageSize = 4096; // 默认值
        try {
            java.lang.Process process = Runtime.getRuntime().exec("getconf PAGESIZE");
            try (BufferedReader reader = new BufferedReader(new java.io.InputStreamReader(process.getInputStream()))) {
                String line = reader.readLine();
                if (line != null && !line.isEmpty()) {
                    pageSize = Integer.parseInt(line.trim());
                }
            }
            process.waitFor();
        } catch (Exception e) {
            // 使用默认值
        }
        return pageSize;
    }
    
    /**
     * 检查播放性能
     */
    private void checkPlayerPerformance() {
        try {
            if (!isPlaying || playerPerformanceMap.isEmpty()) {
                return;
            }
            
            long currentTime = System.currentTimeMillis();
            
            // 检查所有播放记录，如果播放已经结束超过5分钟则清理
            List<String> toRemove = new ArrayList<>();
            for (Map.Entry<String, PlayerPerformanceInfo> entry : playerPerformanceMap.entrySet()) {
                PlayerPerformanceInfo info = entry.getValue();
                if (info.endTime > 0 && currentTime - info.endTime > 5 * 60 * 1000) {
                    toRemove.add(entry.getKey());
                }
            }
            
            for (String url : toRemove) {
                playerPerformanceMap.remove(url);
            }
            
        } catch (Exception e) {
            LOG.e("检查播放性能失败: " + e.getMessage());
        }
    }
    
    /**
     * 标记播放开始
     */
    public void onPlayStart(String url) {
        if (!isPlayerMonitoringEnabled || url == null) return;
        
        try {
            LOG.i("开始播放性能监控: " + url);
            
            PlayerPerformanceInfo perfInfo = new PlayerPerformanceInfo(url);
            perfInfo.startTime = System.currentTimeMillis();
            
            playerPerformanceMap.put(url, perfInfo);
            isPlaying = true;
            
        } catch (Exception e) {
            // 忽略异常
        }
    }
    
    /**
     * 标记播放结束
     */
    public void onPlayEnd(String url) {
        if (!isPlayerMonitoringEnabled || url == null) return;
        
        try {
            PlayerPerformanceInfo perfInfo = playerPerformanceMap.get(url);
            if (perfInfo != null) {
                perfInfo.endTime = System.currentTimeMillis();
                perfInfo.totalPlayTime = perfInfo.endTime - perfInfo.startTime;
                
                LOG.i("结束播放性能监控: " + url + " 总时长: " + (perfInfo.totalPlayTime / 1000) + " 秒, "
                     + "缓冲次数: " + perfInfo.bufferingCount + ", 缓冲占比: " 
                     + (perfInfo.totalBufferingTime * 100 / Math.max(1, perfInfo.totalPlayTime)) + "%");
            }
            
            // 如果没有其他播放任务，设置为非播放状态
            boolean hasActivePlaying = false;
            for (PlayerPerformanceInfo info : playerPerformanceMap.values()) {
                if (info.endTime == 0) {
                    hasActivePlaying = true;
                    break;
                }
            }
            
            if (!hasActivePlaying) {
                isPlaying = false;
            }
            
        } catch (Exception e) {
            // 忽略异常
        }
    }
    
    /**
     * 标记缓冲开始
     */
    public void onBufferingStart() {
        if (!isPlayerMonitoringEnabled) return;
        
        try {
            LOG.i("缓冲开始");
            
            lastBufferingStartTime = System.currentTimeMillis();
            bufferingCount++;
            
        } catch (Exception e) {
            // 忽略异常
        }
    }
    
    /**
     * 标记缓冲结束
     */
    public void onBufferingEnd(String url) {
        if (!isPlayerMonitoringEnabled || url == null) return;
        
        try {
            long bufferingEndTime = System.currentTimeMillis();
            long bufferingTime = 0;
            
            if (lastBufferingStartTime > 0) {
                bufferingTime = bufferingEndTime - lastBufferingStartTime;
            }
            
            PlayerPerformanceInfo perfInfo = playerPerformanceMap.get(url);
            if (perfInfo != null) {
                perfInfo.bufferingCount++;
                perfInfo.totalBufferingTime += bufferingTime;
                
                if (bufferingTime > perfInfo.maxBufferingTime) {
                    perfInfo.maxBufferingTime = bufferingTime;
                }
            }
            
            LOG.i("缓冲结束, 耗时: " + (bufferingTime / 1000.0) + " 秒");
            
            // 重置缓冲开始时间
            lastBufferingStartTime = 0;
            
        } catch (Exception e) {
            // 忽略异常
        }
    }
    
    /**
     * 记录帧渲染开始
     */
    public void onFrameStart() {
        if (!isFrameMonitoringEnabled) {
            return;
        }
        
        frameStartTime = System.currentTimeMillis();
    }
    
    /**
     * 记录帧渲染结束
     */
    public void onFrameEnd() {
        if (!isFrameMonitoringEnabled || frameStartTime == 0) {
            return;
        }
        
        long frameTime = System.currentTimeMillis() - frameStartTime;
        frameRenderTimes.add(frameTime);
        totalRenderTime.addAndGet(frameTime);
        frameCount++;
        
        // 限制列表大小
        while (frameRenderTimes.size() > 120) { // 保存最近120帧
            Long oldTime = frameRenderTimes.remove(0);
            totalRenderTime.addAndGet(-oldTime);
            frameCount--;
        }
        
        // 计算平均帧率
        if (frameCount > 0) {
            long avgFrameTime = totalRenderTime.get() / frameCount;
            if (avgFrameTime > 0) {
                averageFrameRate = 1000.0f / avgFrameTime;
            }
        }
    }
    
    /**
     * 记录Activity渲染时间
     */
    public void recordActivityRender(String activityName, long renderTime) {
        activityRenderTimes.put(activityName, renderTime);
    }
    
    /**
     * 获取性能报告
     */
    public Map<String, Object> getPerformanceReport() {
        Map<String, Object> report = new HashMap<>();
        
        // 应用性能
        Map<String, Object> appPerf = new HashMap<>();
        appPerf.put("averageFrameRate", averageFrameRate);
        appPerf.put("activityRenderTimes", new HashMap<>(activityRenderTimes));
        report.put("appPerformance", appPerf);
        
        // 内存性能
        Map<String, Object> memoryPerf = new HashMap<>();
        if (!memoryHistory.isEmpty()) {
            MemoryInfo latestMemInfo = memoryHistory.get(memoryHistory.size() - 1);
            memoryPerf.put("totalMemory", latestMemInfo.totalMem / 1024 / 1024 + " MB");
            memoryPerf.put("availableMemory", latestMemInfo.availMem / 1024 / 1024 + " MB");
            memoryPerf.put("usedHeapMemory", latestMemInfo.usedHeapMemory / 1024 / 1024 + " MB");
            memoryPerf.put("maxHeapMemory", latestMemInfo.heapMaxMemory / 1024 / 1024 + " MB");
            memoryPerf.put("isLowMemory", latestMemInfo.lowMemory);
            
            // 内存趋势（最近10个点）
            List<Map<String, Object>> memoryTrend = new ArrayList<>();
            int startIndex = Math.max(0, memoryHistory.size() - 10);
            for (int i = startIndex; i < memoryHistory.size(); i++) {
                MemoryInfo info = memoryHistory.get(i);
                Map<String, Object> point = new HashMap<>();
                point.put("time", info.timestamp);
                point.put("usedMem", info.usedHeapMemory / 1024 / 1024);
                point.put("availMem", info.availMem / 1024 / 1024);
                memoryTrend.add(point);
            }
            memoryPerf.put("trend", memoryTrend);
        }
        report.put("memoryPerformance", memoryPerf);
        
        // 网络性能
        report.put("networkPerformance", NetworkRequestManager.getInstance().getPerformanceStats());
        
        // 播放性能
        Map<String, Object> playerPerf = new HashMap<>();
        playerPerf.put("currentlyPlaying", isPlaying);
        playerPerf.put("totalBufferingCount", bufferingCount);
        playerPerf.put("totalBufferingTime", totalBufferingTime / 1000.0 + " 秒");
        
        // 最近播放的视频性能
        List<Map<String, Object>> recentPlays = new ArrayList<>();
        for (PlayerPerformanceInfo info : playerPerformanceMap.values()) {
            if (info.totalPlayTime > 0) {
                Map<String, Object> playInfo = new HashMap<>();
                playInfo.put("url", info.url);
                playInfo.put("duration", info.totalPlayTime / 1000.0 + " 秒");
                playInfo.put("bufferingCount", info.bufferingCount);
                playInfo.put("bufferingTime", info.totalBufferingTime / 1000.0 + " 秒");
                playInfo.put("bufferingRatio", info.totalPlayTime > 0 ? 
                        (info.totalBufferingTime * 100.0 / info.totalPlayTime) + "%" : "0%");
                recentPlays.add(playInfo);
            }
        }
        playerPerf.put("recentPlays", recentPlays);
        report.put("playerPerformance", playerPerf);
        
        return report;
    }
    
    /**
     * 内存信息类
     */
    public static class MemoryInfo {
        public long timestamp;
        public long totalMem;
        public long availMem;
        public long threshold;
        public boolean lowMemory;
        public int vss;
        public int rss;
        public long heapMaxMemory;
        public long heapTotalMemory;
        public long heapFreeMemory;
        public long usedHeapMemory;
        public int totalPss;
        public int nativePss;
        public int dalvikPss;
    }
    
    /**
     * 播放器性能信息类
     */
    public static class PlayerPerformanceInfo {
        public String url;
        public long startTime;
        public long endTime;
        public long totalPlayTime;
        public int bufferingCount;
        public long totalBufferingTime;
        public long maxBufferingTime;
        
        public PlayerPerformanceInfo(String url) {
            this.url = url;
            this.startTime = System.currentTimeMillis();
        }
    }
} 