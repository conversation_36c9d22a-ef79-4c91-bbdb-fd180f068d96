package com.github.tvbox.osc.util;

import android.os.Environment;
import android.text.TextUtils;

import com.github.tvbox.osc.base.App;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;

public class FileUtils {

    public static boolean writeSimple(byte[] data, File dst) {
        try {
            if (dst.exists())
                dst.delete();
            BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(dst));
            bos.write(data);
            bos.close();
            return true;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    public static byte[] readSimple(File src) {
        try {
            BufferedInputStream bis = new BufferedInputStream(new FileInputStream(src));
            int len = bis.available();
            byte[] data = new byte[len];
            bis.read(data);
            bis.close();
            return data;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void copyFile(File source, File dest) throws IOException {
        InputStream is = null;
        OutputStream os = null;
        try {
            is = new FileInputStream(source);
            os = new FileOutputStream(dest);
            byte[] buffer = new byte[1024];
            int length;
            while ((length = is.read(buffer)) > 0) {
                os.write(buffer, 0, length);
            }
        } finally {
            is.close();
            os.close();
        }
    }

    public static void recursiveDelete(File file) {
        if (!file.exists())
            return;
        if (file.isDirectory()) {
            for (File f : file.listFiles()) {
                recursiveDelete(f);
            }
        }
        file.delete();
    }

    public static String readFileToString(String path, String charsetName) {
        // 定义返回结果
        String jsonString = "";

        BufferedReader in = null;
        try {
            in = new BufferedReader(new InputStreamReader(new FileInputStream(new File(path)), charsetName));// 读取文件
            String thisLine = null;
            while ((thisLine = in.readLine()) != null) {
                jsonString += thisLine;
            }
            in.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException el) {
                }
            }
        }
        // 返回拼接好的JSON String
        return jsonString;
    }

    public static String getAssetFile(String assetName) throws IOException {
        InputStream is = App.getInstance().getAssets().open(assetName);
        byte[] data = new byte[is.available()];
        is.read(data);
        return new String(data, "UTF-8");
    }

    public static boolean isAssetFile(String name, String path) {
        try {
            for(String one : App.getInstance().getAssets().list(path)) {
                if (one.equals(name)) return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static String getRootPath() {
        return Environment.getExternalStorageDirectory().getAbsolutePath();
    }

    public static File getLocal(String path) {
        return new File(path.replace("file:/", getRootPath()));
    }

    public static File getCacheDir() {
        return App.getInstance().getCacheDir();
    }

    public static String getCachePath() {
        return getCacheDir().getAbsolutePath();
    }

    public static void cleanDirectory(File dir) {
        if (!dir.exists()) return;
        File[] files = dir.listFiles();
        if (files == null || files.length == 0) return;
        for(File one : files) {
            try {
                deleteFile(one);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void deleteFile(File file) {
        if (!file.exists()) return;
        if (file.isFile()) {
            if (file.canWrite()) file.delete();
            return;
        }
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            if (files == null || files.length == 0) {
                if (file.canWrite()) file.delete();
                return;
            }
            for(File one : files) {
                deleteFile(one);
            }
        }
        return;
    }

    public static void cleanPlayerCache() {
        String ijkCachePath = getCachePath() + "/ijkcaches/";
        String thunderCachePath = getCachePath() + "/thunder/";
        String exoCachePath = getCachePath() + "/exo-video-cache/";
        
        File ijkCacheDir = new File(ijkCachePath);
        File thunderCacheDir = new File(thunderCachePath);
        File exoCacheDir = new File(exoCachePath);
        
        try {
            int days = com.orhanobut.hawk.Hawk.get(com.github.tvbox.osc.util.HawkConfig.CACHE_AUTO_CLEAN_DAYS, 7);
            long cleanTimeThreshold = System.currentTimeMillis() - (days * 24 * 60 * 60 * 1000L);
            
            // 智能清理：先清理老旧文件
            int cleanedFiles = 0;
            
            if (ijkCacheDir.exists()) {
                cleanedFiles += cleanOldFiles(ijkCacheDir, cleanTimeThreshold);
            }
            
            if (thunderCacheDir.exists()) {
                cleanedFiles += cleanOldFiles(thunderCacheDir, cleanTimeThreshold);
            }
            
            if (exoCacheDir.exists()) {
                cleanedFiles += cleanOldFiles(exoCacheDir, cleanTimeThreshold);
            }
            
            // 如果有文件被清理，显示提示信息
            if (cleanedFiles > 0) {
                android.widget.Toast.makeText(com.github.tvbox.osc.base.App.getInstance(), 
                    "智能清理了" + cleanedFiles + "个缓存文件", 
                    android.widget.Toast.LENGTH_SHORT).show();
                return;
            }
            
            // 如果没有老旧文件被清理，则执行完全清理
            if (ijkCacheDir.exists()) cleanDirectory(ijkCacheDir);
            if (thunderCacheDir.exists()) cleanDirectory(thunderCacheDir);
            if (exoCacheDir.exists()) cleanDirectory(exoCacheDir);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 清理老旧文件
     * @param dir 目录
     * @param timeThreshold 时间阈值
     * @return 清理的文件数量
     */
    private static int cleanOldFiles(File dir, long timeThreshold) {
        if (!dir.exists() || !dir.isDirectory()) {
            return 0;
        }
        
        File[] files = dir.listFiles();
        if (files == null || files.length == 0) {
            return 0;
        }
        
        int count = 0;
        for (File file : files) {
            if (file.isFile() && file.lastModified() < timeThreshold) {
                if (file.delete()) {
                    count++;
                }
            } else if (file.isDirectory()) {
                count += cleanOldFiles(file, timeThreshold);
            }
        }
        
        return count;
    }

    public static String read(String path) {
        try {
            BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(getLocal(path))));
            StringBuilder sb = new StringBuilder();
            String text;
            while ((text = br.readLine()) != null) sb.append(text).append("\n");
            br.close();
            return sb.toString();
        } catch (Exception e) {
            return "";
        }
    }

    public static String getFileName(String filePath){
        if(TextUtils.isEmpty(filePath)) return "";
        String fileName = filePath;
        int p = fileName.lastIndexOf(File.separatorChar);
        if(p != -1){
            fileName = fileName.substring(p + 1);
        }
        return fileName;
    }

    public static String getFileNameWithoutExt(String filePath){
        if(TextUtils.isEmpty(filePath)) return "";
        String fileName = filePath;
        int p = fileName.lastIndexOf(File.separatorChar);
        if(p != -1){
            fileName = fileName.substring(p + 1);
        }
        p = fileName.indexOf('.');
        if(p != -1){
            fileName = fileName.substring(0, p);
        }
        return fileName;
    }

    public static String getFileExt(String fileName){
        if(TextUtils.isEmpty(fileName)) return "";
        int p = fileName.lastIndexOf('.');
        if(p != -1) {
            return fileName.substring(p).toLowerCase();
        }
        return "";
    }

    public static boolean hasExtension(String path) {
        int lastDotIndex = path.lastIndexOf(".");
        int lastSlashIndex = Math.max(path.lastIndexOf("/"), path.lastIndexOf("\\"));
        // 如果路径中有点号，并且点号在最后一个斜杠之后，认为有后缀
        return lastDotIndex > lastSlashIndex && lastDotIndex < path.length() - 1;
    }

    /**
     * 获取文件夹大小
     */
    public static long getFolderSize(File file) {
        long size = 0;
        try {
            if (file == null || !file.exists()) {
                return 0;
            }
            
            File[] fileList = file.listFiles();
            if (fileList == null || fileList.length == 0) {
                return 0;
            }
            
            for (File value : fileList) {
                try {
                    if (value.isDirectory()) {
                        long folderSize = getFolderSize(value);
                        // 防止整数溢出
                        if (folderSize > 0 && Long.MAX_VALUE - size >= folderSize) {
                            size += folderSize;
                        } else if (folderSize > 0) {
                            // 溢出保护
                            LOG.w("文件夹大小计算溢出保护: " + value.getPath());
                            return Long.MAX_VALUE;
                        }
                    } else {
                        long fileSize = value.length();
                        // 防止整数溢出
                        if (fileSize > 0 && Long.MAX_VALUE - size >= fileSize) {
                            size += fileSize;
                        } else if (fileSize > 0) {
                            // 溢出保护
                            LOG.w("文件大小计算溢出保护: " + value.getPath());
                            return Long.MAX_VALUE;
                        }
                    }
                } catch (Exception e) {
                    LOG.e("计算单个文件大小出错: " + value.getPath() + ", 错误: " + e.getMessage());
                    // 单个文件出错不应该影响整体计算，继续处理下一个文件
                }
            }
        } catch (Exception e) {
            LOG.e("计算文件夹大小出错: " + (file != null ? file.getPath() : "null") + ", 错误: " + e.getMessage());
            e.printStackTrace();
            // 出错返回0而不是负值
            return 0;
        }
        
        // 确保不返回负值
        return Math.max(0, size);
    }
    
    /**
     * 删除目录
     */
    public static boolean deleteDir(File dir) {
        if (dir != null && dir.isDirectory()) {
            String[] children = dir.list();
            if (children != null) {
                for (String child : children) {
                    boolean success = deleteDir(new File(dir, child));
                    if (!success) {
                        return false;
                    }
                }
            }
        }
        return dir != null && dir.delete();
    }
    
    /**
     * 读取文件内容
     */
    public static byte[] readFile(File file) throws IOException {
        if (!file.exists() || !file.isFile()) {
            return null;
        }
        
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            byte[] data = new byte[(int) file.length()];
            int offset = 0;
            int numRead;
            while (offset < data.length && (numRead = fis.read(data, offset, data.length - offset)) >= 0) {
                offset += numRead;
            }
            if (offset != data.length) {
                throw new IOException("Could not completely read file " + file.getName());
            }
            return data;
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
    
    /**
     * 写入文件内容
     */
    public static void writeFile(File file, byte[] data) throws IOException {
        File parentFile = file.getParentFile();
        if (!parentFile.exists()) {
            parentFile.mkdirs();
        }
        
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(file);
            fos.write(data);
            fos.flush();
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
