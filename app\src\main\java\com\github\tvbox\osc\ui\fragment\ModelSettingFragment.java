package com.github.tvbox.osc.ui.fragment;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DiffUtil;

import com.blankj.utilcode.util.ColorUtils;
import com.blankj.utilcode.util.LogUtils;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.github.tvbox.osc.R;
import com.github.tvbox.osc.api.ApiConfig;
import com.github.tvbox.osc.base.App;
import com.github.tvbox.osc.base.BaseLazyFragment;
import com.github.tvbox.osc.base.BaseActivity;
import com.github.tvbox.osc.bean.IJKCode;
import com.github.tvbox.osc.bean.SourceBean;
import com.github.tvbox.osc.cache.Cache;
import com.github.tvbox.osc.cache.CacheHelper;
import com.github.tvbox.osc.cache.CacheManager;
import com.github.tvbox.osc.cache.TypeSizeInfo;
import com.github.tvbox.osc.data.AppDataManager;
import com.github.tvbox.osc.ui.activity.AboutActivity;
import com.github.tvbox.osc.ui.activity.SettingActivity;
import com.github.tvbox.osc.ui.activity.VipActivity;
import com.github.tvbox.osc.ui.activity.PerformanceActivity;
import com.github.tvbox.osc.ui.adapter.SelectDialogAdapter;
import com.github.tvbox.osc.ui.dialog.AboutDialog;
import com.github.tvbox.osc.ui.dialog.ApiDialog;
import com.github.tvbox.osc.ui.dialog.BackupDialog;
import com.github.tvbox.osc.ui.dialog.SelectDialog;
import com.github.tvbox.osc.ui.dialog.XWalkInitDialog;
import com.github.tvbox.osc.ui.dialog.LoadingDialog;
import com.github.tvbox.osc.util.DebugHelper;
import com.github.tvbox.osc.util.FastClickCheckUtil;
import com.github.tvbox.osc.util.FileUtils;
import com.github.tvbox.osc.util.HawkConfig;
import com.github.tvbox.osc.util.LOG;
import com.github.tvbox.osc.util.OkGoHelper;
import com.github.tvbox.osc.util.PlayerHelper;
import com.github.tvbox.osc.util.PreloadHelper;
import com.github.tvbox.osc.util.SharePreferencesUtils;
import com.lzy.okgo.OkGo;
import com.lzy.okgo.callback.FileCallback;
import com.lzy.okgo.callback.StringCallback;
import com.lzy.okgo.model.Progress;
import com.lzy.okgo.model.Response;
import com.orhanobut.hawk.Hawk;
import com.owen.tvrecyclerview.widget.TvRecyclerView;
import com.owen.tvrecyclerview.widget.V7GridLayoutManager;

import org.greenrobot.eventbus.EventBus;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import me.jessyan.autosize.utils.AutoSizeUtils;
import okhttp3.HttpUrl;
import tv.danmaku.ijk.media.player.IjkMediaPlayer;

/**
 * <AUTHOR>
 * @date :2020/12/23
 * @description:
 */
public class ModelSettingFragment extends BaseLazyFragment {
    private TextView tvDebugOpen;
    private TextView tvMediaCodec;
    private TextView tvParseWebView;
    private TextView tvRender;
    private TextView tvHomeApi;
    private TextView tvHomeShow;
    private TextView tvShowPreviewText;
    private TextView tvFastSearchText;
    private TextView tvDns;
    private TextView tvSearchView;
    private TextView tvTheme;
    private TextView tvAutoUpdate;
    private TextView tvUpdateInterval;
    private TextView tvCacheSize;
    private TextView tvCacheMaxSize;
    private TextView tvCachePriorityEnable;
    private TextView tvCacheAutoClean;
    private TextView tvCacheAutoCleanDays;
    private TextView tvPreloadEnable;
    private TextView tvContentPreloadEnable;
    private LinearLayout llCacheAnalysis;
    private LoadingDialog mLoadingDialog;
    private Activity mActivity;

    public static ModelSettingFragment newInstance() {
        return new ModelSettingFragment().setArguments();
    }

    public ModelSettingFragment setArguments() {
        return this;
    }

    @Override
    protected int getLayoutResID() {
        return R.layout.fragment_model_setting;
    }

    @Override
    protected void init() {
        try {
            LOG.i("ModelSettingFragment init start");

            // 检查Activity是否可用
            if (getActivity() == null || getActivity().isFinishing()) {
                LOG.e("ModelSettingFragment: Activity不可用，跳过初始化");
                return;
            }

            mActivity = getActivity();

            // 检查View是否可用
            if (getView() == null) {
                LOG.e("ModelSettingFragment: View不可用，跳过初始化");
                return;
            }

            // 分步初始化，每步都有异常处理
            try {
                initCategories();
                LOG.d("ModelSettingFragment: 分类标题初始化完成");
            } catch (Exception e) {
                LOG.e("ModelSettingFragment: 分类标题初始化失败: " + e.getMessage(), e);
            }

            try {
                initViews();
                LOG.d("ModelSettingFragment: 视图初始化完成");
            } catch (Exception e) {
                LOG.e("ModelSettingFragment: 视图初始化失败: " + e.getMessage(), e);
            }

            try {
                initCacheSettings();
                LOG.d("ModelSettingFragment: 缓存设置初始化完成");
            } catch (Exception e) {
                LOG.e("ModelSettingFragment: 缓存设置初始化失败: " + e.getMessage(), e);
            }

            try {
                setClickListeners();
                LOG.d("ModelSettingFragment: 点击监听器设置完成");
            } catch (Exception e) {
                LOG.e("ModelSettingFragment: 点击监听器设置失败: " + e.getMessage(), e);
            }

            try {
                ensureViewsVisibility();
                LOG.d("ModelSettingFragment: 视图可见性设置完成");
            } catch (Exception e) {
                LOG.e("ModelSettingFragment: 视图可见性设置失败: " + e.getMessage(), e);
            }

            try {
                enhanceTvFocusability();
                LOG.d("ModelSettingFragment: 焦点处理设置完成");
            } catch (Exception e) {
                LOG.e("ModelSettingFragment: 焦点处理设置失败: " + e.getMessage(), e);
            }

            LOG.i("ModelSettingFragment init completed");
        } catch (Exception e) {
            LOG.e("初始化设置模块出错: " + e.getMessage());
            e.printStackTrace();

            // 通知Activity初始化失败
            if (getActivity() instanceof SettingActivity) {
                ((SettingActivity) getActivity()).finish();
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        DebugHelper.logDebug("ModelSettingFragment onResume");
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        DebugHelper.logDebug("ModelSettingFragment onActivityCreated");
        
        // 设置回调，在onActivityCreated设置，确保在Activity创建完成后设置
        try {
            // 此时安全地设置回调
            SettingActivity.callback = new SettingActivity.DevModeCallback() {
                @Override
                public void onChange() {
                    try {
                        if (isAdded() && !isDetached() && getActivity() != null) {
                            View llDebug = getView().findViewById(R.id.llDebug);
                            if (llDebug != null) {
                                llDebug.setVisibility(View.VISIBLE);
                            }
                        }
                    } catch (Exception e) {
                        LOG.e("设置开发者模式出错: " + e.getMessage());
                    }
                }
            };
        } catch (Exception e) {
            LOG.e("设置回调异常: " + e.getMessage());
            DebugHelper.logError("设置回调异常", e);
        }
    }

    private void initCategories() {
        try {
            // 播放器设置类别
            View categoryPlayerView = getView().findViewById(R.id.categoryPlayer);
            if (categoryPlayerView != null) {
                TextView playerCategory = categoryPlayerView.findViewById(R.id.categoryTitle);
                ImageView playerIcon = categoryPlayerView.findViewById(R.id.categoryIcon);
                if (playerCategory != null) {
                    playerCategory.setText("播放器设置");
                }
                if (playerIcon != null) {
                    playerIcon.setImageResource(R.drawable.ic_setting_player);
                }
            }
            
            // 界面设置类别
            View categoryInterfaceView = getView().findViewById(R.id.categoryInterface);
            if (categoryInterfaceView != null) {
                TextView interfaceCategory = categoryInterfaceView.findViewById(R.id.categoryTitle);
                ImageView interfaceIcon = categoryInterfaceView.findViewById(R.id.categoryIcon);
                if (interfaceCategory != null) {
                    interfaceCategory.setText("界面设置");
                }
                if (interfaceIcon != null) {
                    interfaceIcon.setImageResource(R.drawable.ic_setting_ui);
                }
            }
            
            // 网络设置类别
            View categoryNetworkView = getView().findViewById(R.id.categoryNetwork);
            if (categoryNetworkView != null) {
                TextView networkCategory = categoryNetworkView.findViewById(R.id.categoryTitle);
                ImageView networkIcon = categoryNetworkView.findViewById(R.id.categoryIcon);
                if (networkCategory != null) {
                    networkCategory.setText("网络设置");
                }
                if (networkIcon != null) {
                    networkIcon.setImageResource(R.drawable.ic_setting_network);
                }
            }
            
            // 缓存设置类别
            View categoryCacheView = getView().findViewById(R.id.categoryCache);
            if (categoryCacheView != null) {
                TextView cacheCategory = categoryCacheView.findViewById(R.id.categoryTitle);
                ImageView cacheIcon = categoryCacheView.findViewById(R.id.categoryIcon);
                if (cacheCategory != null) {
                    cacheCategory.setText("缓存设置");
                }
                if (cacheIcon != null) {
                    cacheIcon.setImageResource(R.drawable.ic_setting_cache);
                }
            }
            
            // 其他设置类别
            View categoryOthersView = getView().findViewById(R.id.categoryOthers);
            if (categoryOthersView != null) {
                TextView othersCategory = categoryOthersView.findViewById(R.id.categoryTitle);
                ImageView othersIcon = categoryOthersView.findViewById(R.id.categoryIcon);
                if (othersCategory != null) {
                    othersCategory.setText("其他设置");
                }
                if (othersIcon != null) {
                    othersIcon.setImageResource(R.drawable.ic_setting_other);
                }
            }
            
            LOG.i("ModelSettingFragment 初始化分类标题完成");
        } catch (Exception e) {
            LOG.e("ModelSettingFragment 初始化分类标题错误: " + e.getMessage());
        }
    }

    private void initViews() {
        LOG.i("ModelSettingFragment initViews start");
        try {
            // 获取所有TextView
            initSettingItem(R.id.llMediaCodec, "媒体解码", R.drawable.ic_setting_media_codec);
            tvMediaCodec = getView().findViewById(R.id.llMediaCodec).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.llParseWebVew, "解析器", R.drawable.ic_setting_parser);
            tvParseWebView = findViewById(R.id.llParseWebVew).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.llRender, "渲染方式", R.drawable.ic_setting_render);
            tvRender = findViewById(R.id.llRender).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.llHomeApi, "首页源", R.drawable.ic_setting_api);
            tvHomeApi = findViewById(R.id.llHomeApi).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.llHomeShow, "显示源", R.drawable.ic_setting_display);
            tvHomeShow = findViewById(R.id.llHomeShow).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.showPreview, "视频预览", R.drawable.ic_setting_preview);
            tvShowPreviewText = findViewById(R.id.showPreview).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.llSearchView, "搜索视图", R.drawable.ic_setting_search);
            tvSearchView = findViewById(R.id.llSearchView).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.llTheme, "主题", R.drawable.ic_setting_theme);
            tvTheme = findViewById(R.id.llTheme).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.llDns, "安全DNS", R.drawable.ic_setting_dns);
            tvDns = findViewById(R.id.llDns).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.llAutoUpdate, "自动更新", R.drawable.ic_setting_update);
            tvAutoUpdate = findViewById(R.id.llAutoUpdate).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.llUpdateInterval, "检查间隔", R.drawable.ic_setting_interval);
            tvUpdateInterval = findViewById(R.id.llUpdateInterval).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.llClearCache, "清理缓存", R.drawable.ic_setting_cache);
            tvCacheSize = findViewById(R.id.llClearCache).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.llCacheMaxSize, "缓存大小", R.drawable.ic_setting_cache);
            tvCacheMaxSize = findViewById(R.id.llCacheMaxSize).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.llCachePriorityEnable, "缓存优先级", R.drawable.ic_setting_cache);
            tvCachePriorityEnable = findViewById(R.id.llCachePriorityEnable).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.llCacheAutoClean, "自动清理", R.drawable.ic_setting_cache);
            tvCacheAutoClean = findViewById(R.id.llCacheAutoClean).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.llCacheAutoCleanDays, "清理天数", R.drawable.ic_setting_cache);
            tvCacheAutoCleanDays = findViewById(R.id.llCacheAutoCleanDays).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.llPreloadEnable, "视频预加载", R.drawable.ic_setting_cache);
            tvPreloadEnable = findViewById(R.id.llPreloadEnable).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.llCacheAnalysis, "缓存分析", R.drawable.ic_setting_cache);
            llCacheAnalysis = findViewById(R.id.llCacheAnalysis);
            
            initSettingItem(R.id.llDebug, "调试模式", R.drawable.ic_setting_other);
            tvDebugOpen = findViewById(R.id.llDebug).findViewById(R.id.settingItemValue);
            
            initSettingItem(R.id.llBackup, "备份/恢复", R.drawable.ic_setting_other);
            initSettingItem(R.id.llAbout, "关于", R.drawable.ic_setting_other);
            initSettingItem(R.id.llSetOthers, "设置其他", R.drawable.ic_setting_other);
            
            // 性能监控设置项
            initSettingItem(R.id.llPerformanceMonitor, "性能监控", R.drawable.ic_setting_performance);
            
            // 设置调试模式布局的可见性
            View llDebug = findViewById(R.id.llDebug);
            if (llDebug != null) {
                llDebug.setVisibility(Hawk.get(HawkConfig.DEBUG_OPEN, false) ? View.VISIBLE : View.GONE);
                LOG.i("ModelSettingFragment llDebug visibility set: " + (Hawk.get(HawkConfig.DEBUG_OPEN, false) ? "VISIBLE" : "GONE"));
            } else {
                LOG.e("ModelSettingFragment llDebug is null");
            }
            
            // 初始化文本值
            initTextValues();
            
            // 初始化视图变量引用
            tvDebugOpen = getView().findViewById(R.id.llDebug).findViewById(R.id.settingItemValue);
            tvMediaCodec = getView().findViewById(R.id.llMediaCodec).findViewById(R.id.settingItemValue);
            tvParseWebView = getView().findViewById(R.id.llParseWebVew).findViewById(R.id.settingItemValue);
            tvHomeApi = getView().findViewById(R.id.llHomeApi).findViewById(R.id.settingItemValue);
            tvDns = getView().findViewById(R.id.llDns).findViewById(R.id.settingItemValue);
            tvHomeShow = getView().findViewById(R.id.llHomeShow).findViewById(R.id.settingItemValue);
            tvTheme = getView().findViewById(R.id.llTheme).findViewById(R.id.settingItemValue);
            tvSearchView = getView().findViewById(R.id.llSearchView).findViewById(R.id.settingItemValue);
            tvRender = getView().findViewById(R.id.llRender).findViewById(R.id.settingItemValue);
            tvShowPreviewText = getView().findViewById(R.id.showPreview).findViewById(R.id.settingItemValue);
            
            try {
                // 资源ID不存在，设置为null
                tvFastSearchText = null;
                LOG.i("快速搜索功能未启用或资源不存在");
            } catch (Exception e) {
                LOG.e("Fast search text view not found: " + e.getMessage());
            }
            
            tvAutoUpdate = getView().findViewById(R.id.llAutoUpdate).findViewById(R.id.settingItemValue);
            tvUpdateInterval = getView().findViewById(R.id.llUpdateInterval).findViewById(R.id.settingItemValue);
            tvCacheSize = getView().findViewById(R.id.llClearCache).findViewById(R.id.settingItemValue);
            tvCacheMaxSize = getView().findViewById(R.id.llCacheMaxSize).findViewById(R.id.settingItemValue);
            tvCacheAutoClean = getView().findViewById(R.id.llCacheAutoClean).findViewById(R.id.settingItemValue);
            tvCacheAutoCleanDays = getView().findViewById(R.id.llCacheAutoCleanDays).findViewById(R.id.settingItemValue);
            tvCachePriorityEnable = getView().findViewById(R.id.llCachePriorityEnable).findViewById(R.id.settingItemValue);
            tvPreloadEnable = getView().findViewById(R.id.llPreloadEnable).findViewById(R.id.settingItemValue);
            tvContentPreloadEnable = getView().findViewById(R.id.llContentPreloadEnable).findViewById(R.id.settingItemValue);
            
            // 视频预加载开关
            View llPreloadEnable = getView().findViewById(R.id.llPreloadEnable);
            if (llPreloadEnable != null) {
                TextView tvTitle = llPreloadEnable.findViewById(R.id.settingItemTitle);
                tvPreloadEnable = llPreloadEnable.findViewById(R.id.settingItemValue);
                ImageView icon = llPreloadEnable.findViewById(R.id.settingItemIcon);
                if (tvTitle != null && tvPreloadEnable != null && icon != null) {
                    tvTitle.setText("视频预加载");
                    tvPreloadEnable.setText(Hawk.get(HawkConfig.CACHE_PLAY_URL_ENABLE, true) ? "开启" : "关闭");
                    icon.setImageResource(R.drawable.ic_setting_cache);
                }
            }
            
            // 初始化视频预缓冲开关
            View llContentPreloadEnable = getView().findViewById(R.id.llContentPreloadEnable);
            if (llContentPreloadEnable != null) {
                tvContentPreloadEnable = llContentPreloadEnable.findViewById(R.id.settingItemValue);
                TextView tvTitle = llContentPreloadEnable.findViewById(R.id.settingItemTitle);
                ImageView icon = llContentPreloadEnable.findViewById(R.id.settingItemIcon);
                
                if (tvTitle != null && tvContentPreloadEnable != null && icon != null) {
                    tvTitle.setText(R.string.setting_cache_content_preload);
                    tvContentPreloadEnable.setText(Hawk.get(HawkConfig.CACHE_VIDEO_CONTENT_PRELOAD_ENABLE, true) ? "开启" : "关闭");
                    icon.setImageResource(R.drawable.ic_setting_cache);
                    
                    // 添加提示信息
                    TextView subtitleView = llContentPreloadEnable.findViewById(R.id.settingItemSubtitle);
                    if (subtitleView != null) {
                        subtitleView.setVisibility(View.VISIBLE);
                        subtitleView.setText(R.string.setting_cache_content_preload_info);
                    }
                }
            }
            
            // 删除初始化运营商类型设置项的代码
            
            LOG.i("ModelSettingFragment initViews completed");
        } catch (Exception e) {
            LOG.e("ModelSettingFragment initViews error: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void initSettingItem(int resId, String title, int iconResId) {
        try {
            View itemView = getView().findViewById(resId);
            if (itemView != null) {
                TextView titleView = itemView.findViewById(R.id.settingItemTitle);
                ImageView iconView = itemView.findViewById(R.id.settingItemIcon);
                
                if (titleView != null) {
                    titleView.setText(title);
                }
                
                if (iconView != null && iconResId != 0) {
                    iconView.setImageResource(iconResId);
                }
            }
        } catch (Exception e) {
            LOG.e("初始化设置项失败: " + resId + ", " + e.getMessage());
        }
    }

    private void initTextValues() {
        try {
            // 初始化各种TextView的值
            if (tvShowPreviewText != null) {
                tvShowPreviewText.setText(Hawk.get(HawkConfig.SHOW_PREVIEW, true) ? "开启" : "关闭");
            }
            
            if (tvHomeShow != null) {
                tvHomeShow.setText(Hawk.get(HawkConfig.HOME_SHOW_SOURCE, false) ? "开启" : "关闭");
            }
            
            if (tvTheme != null) {
                tvTheme.setText(getThemeView(Hawk.get(HawkConfig.THEME_SELECT, 0)));
            }
            
            if (tvMediaCodec != null) {
                String codecName = Hawk.get(HawkConfig.IJK_CODEC, "");
                tvMediaCodec.setText(codecName.isEmpty() ? "软解码" : codecName);
            }
            
            if (tvDebugOpen != null) {
                tvDebugOpen.setText(Hawk.get(HawkConfig.DEBUG_OPEN, false) ? "【开】" : "【关】");
            }
            
            if (tvParseWebView != null) {
                tvParseWebView.setText(Hawk.get(HawkConfig.PARSE_WEBVIEW, true) ? "系统自带" : "XWalkView");
            }
            
            if (tvDns != null) {
                int dohIndex = Hawk.get(HawkConfig.DOH_URL, 0);
                if (dohIndex >= 0 && dohIndex < OkGoHelper.dnsHttpsList.size()) {
                    tvDns.setText(OkGoHelper.dnsHttpsList.get(dohIndex));
                } else {
                    tvDns.setText("关闭");
                }
            }
            
            if (tvSearchView != null) {
                tvSearchView.setText(getSearchView(Hawk.get(HawkConfig.SEARCH_VIEW, 0)));
            }
            
            if (tvHomeApi != null) {
                if (ApiConfig.get() != null && ApiConfig.get().getHomeSourceBean() != null) {
                    tvHomeApi.setText(ApiConfig.get().getHomeSourceBean().getName());
                } else {
                    tvHomeApi.setText("未设置");
                }
            }
            
            if (tvRender != null) {
                tvRender.setText(PlayerHelper.getRenderName(Hawk.get(HawkConfig.PLAY_RENDER, 0)));
            }
            
            if (tvAutoUpdate != null) {
                tvAutoUpdate.setText(Hawk.get(HawkConfig.AUTO_UPDATE_ENABLED, true) ? "开启" : "关闭");
            }
            
            if (tvUpdateInterval != null) {
                long currentInterval = Hawk.get(HawkConfig.UPDATE_INTERVAL, 24 * 60 * 60 * 1000L);
                if (currentInterval == 12 * 60 * 60 * 1000L) {
                    tvUpdateInterval.setText("12小时");
                } else if (currentInterval == 24 * 60 * 60 * 1000L) {
                    tvUpdateInterval.setText("24小时");
                } else if (currentInterval == 48 * 60 * 60 * 1000L) {
                    tvUpdateInterval.setText("48小时");
                } else if (currentInterval == 7 * 24 * 60 * 60 * 1000L) {
                    tvUpdateInterval.setText("7天");
                } else {
                    tvUpdateInterval.setText("24小时");
                }
            }
            
            // 缓存相关
            updateCacheSize();
            
            if (tvCacheMaxSize != null) {
                // 从Hawk获取缓存大小（可能是字节或MB）
                Object cacheMaxSizeObj = Hawk.get(HawkConfig.CACHE_MAX_SIZE, 2048);
                int cacheMaxSizeMB;
                
                // 处理不同类型的缓存大小值
                if (cacheMaxSizeObj instanceof Integer) {
                    int value = (Integer) cacheMaxSizeObj;
                    // 如果值大于10MB，认为是字节单位，需要转换为MB
                    if (value > 10 * 1024 * 1024) {
                        cacheMaxSizeMB = value / 1024 / 1024;
                    } else {
                        // 小于10MB，可能已经是MB单位
                        cacheMaxSizeMB = value;
                    }
                } else if (cacheMaxSizeObj instanceof Long) {
                    long value = (Long) cacheMaxSizeObj;
                    // 如果值大于10MB，认为是字节单位，需要转换为MB
                    if (value > 10 * 1024 * 1024) {
                        cacheMaxSizeMB = (int)(value / 1024 / 1024);
                    } else {
                        // 小于10MB，可能已经是MB单位
                        cacheMaxSizeMB = (int)value;
                    }
                } else {
                    // 默认值
                    cacheMaxSizeMB = 2048;
                }
                
                tvCacheMaxSize.setText(cacheMaxSizeMB + "MB");
            }
            
            if (tvPreloadEnable != null) {
                boolean preloadEnabled = Hawk.get(HawkConfig.CACHE_VIDEO_PRELOAD_ENABLE, true);
                tvPreloadEnable.setText(preloadEnabled ? "开启" : "关闭");
            }
            
            if (tvCachePriorityEnable != null) {
                boolean enabled = Hawk.get(HawkConfig.CACHE_PRIORITY_ENABLE, false);
                tvCachePriorityEnable.setText(enabled ? "开启" : "关闭");
            }
            
            if (tvCacheAutoClean != null) {
                boolean autoClean = Hawk.get(HawkConfig.CACHE_VIDEO_AUTO_CLEAN, false);
                tvCacheAutoClean.setText(autoClean ? "开启" : "关闭");
            }
            
            if (tvCacheAutoCleanDays != null) {
                int autoCleanDays = Hawk.get(HawkConfig.CACHE_AUTO_CLEAN_DAYS, 7);
                tvCacheAutoCleanDays.setText(autoCleanDays + "天");
            }
            
            // 视频预缓冲开关状态
            boolean contentPreloadEnabled = Hawk.get(HawkConfig.CACHE_VIDEO_CONTENT_PRELOAD_ENABLE, true);
            if (tvContentPreloadEnable != null) {
                tvContentPreloadEnable.setText(contentPreloadEnabled ? "开启" : "关闭");
            }
            
            LOG.i("ModelSettingFragment initTextValues completed");
        } catch (Exception e) {
            LOG.e("ModelSettingFragment initTextValues error: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void setClickListeners() {
        LOG.i("ModelSettingFragment setClickListeners start");
        try {
            // 调试模式
            LinearLayout llDebug = getView().findViewById(R.id.llDebug);
            if (llDebug != null) {
                llDebug.setOnClickListener(v -> {
                    try {
                        // 添加明显的视觉反馈
                        showMessage("调试模式按钮被点击");
                        DebugHelper.logDebug("调试模式按钮被点击");
                        
                        if (FastClickCheckUtil.check(v)) {
                            boolean isOpen = !Hawk.get(HawkConfig.DEBUG_OPEN, false);
                            Hawk.put(HawkConfig.DEBUG_OPEN, isOpen);
                            if (tvDebugOpen != null) {
                                tvDebugOpen.setText(isOpen ? "【开】" : "【关】");
                            }
                            LOG.i("ModelSettingFragment debug mode set to: " + (isOpen ? "开启" : "关闭"));
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment debug click error: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llDebug click listener");
            }

            // 缓存清理
            LinearLayout llClearCache = getView().findViewById(R.id.llClearCache);
            if (llClearCache != null) {
                llClearCache.setOnClickListener(v -> {
                    if (FastClickCheckUtil.check(v)) {
                        onClickClearCache(v);
                    }
                });
                LOG.i("ModelSettingFragment set llClearCache click listener");
            }
            
            // 解析器设置
            LinearLayout llParseWebVew = getView().findViewById(R.id.llParseWebVew);
            if (llParseWebVew != null) {
                llParseWebVew.setOnClickListener(v -> {
                    try {
                        if (FastClickCheckUtil.check(v)) {
                            boolean useSystem = !Hawk.get(HawkConfig.PARSE_WEBVIEW, true);
                            Hawk.put(HawkConfig.PARSE_WEBVIEW, useSystem);
                            if (tvParseWebView != null) {
                                tvParseWebView.setText(useSystem ? "系统自带" : "XWalkView");
                            }
                            LOG.i("ModelSettingFragment parse webview set to: " + (useSystem ? "系统自带" : "XWalkView"));
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment parseWebView click error: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llParseWebVew click listener");
            }

            // 媒体解码设置
            LinearLayout llMediaCodec = getView().findViewById(R.id.llMediaCodec);
            if (llMediaCodec != null && tvMediaCodec != null) {
                llMediaCodec.setOnClickListener(v -> {
                    try {
                        // 添加明显的视觉反馈
                        showMessage("正在切换媒体解码设置");
                        DebugHelper.logDebug("媒体解码按钮被点击，不依赖FastClickCheckUtil");
                        
                        // 直接处理点击事件，不检查重复点击
                        LOG.i("ModelSettingFragment media codec click");
                        List<IJKCode> ijkCodes = ApiConfig.get().getIjkCodes();
                        if (ijkCodes == null || ijkCodes.isEmpty()) {
                            showMessage("无可用编码");
                            return;
                        }
                        
                        // 直接设置，不使用对话框
                        String currentCodec = Hawk.get(HawkConfig.IJK_CODEC, "");
                        int defaultPos = 0;
                        
                        for (int j = 0; j < ijkCodes.size(); j++) {
                            if (ijkCodes.get(j).getName().equals(currentCodec)) {
                                defaultPos = j;
                                break;
                            }
                        }
                        
                        // 选择下一个编码
                        int nextPos = (defaultPos + 1) % ijkCodes.size();
                        IJKCode nextCode = ijkCodes.get(nextPos);
                        
                        // 直接应用设置
                        tvMediaCodec.setText(nextCode.getName());
                        Hawk.put(HawkConfig.IJK_CODEC, nextCode.getName());
                        LOG.i("ModelSettingFragment media codec directly changed to: " + nextCode.getName());
                        showMessage("已设置解码器为: " + nextCode.getName());
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment media codec click error: " + e.getMessage());
                        showMessage("设置解码器失败");
                    }
                });
                LOG.i("ModelSettingFragment set llMediaCodec click listener");
            }

            // 主页显示源
            LinearLayout llHomeShow = getView().findViewById(R.id.llHomeShow);
            if (llHomeShow != null) {
                llHomeShow.setOnClickListener(v -> {
                    try {
                        if (FastClickCheckUtil.check(v)) {
                            boolean isShow = !Hawk.get(HawkConfig.HOME_SHOW_SOURCE, false);
                            Hawk.put(HawkConfig.HOME_SHOW_SOURCE, isShow);
                            if (tvHomeShow != null) {
                                tvHomeShow.setText(isShow ? "开启" : "关闭");
                            }
                            LOG.i("ModelSettingFragment home show source set to: " + (isShow ? "开启" : "关闭"));
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment homeShow click error: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llHomeShow click listener");
            }
            
            // 缓存最大容量设置
            LinearLayout llCacheMaxSize = getView().findViewById(R.id.llCacheMaxSize);
            if (llCacheMaxSize != null && tvCacheMaxSize != null) {
                llCacheMaxSize.setOnClickListener(view -> {
                    try {
                        if (FastClickCheckUtil.check(view)) {
                            LOG.i("ModelSettingFragment cache max size click");
                            
                            // 获取当前缓存大小（可能是字节或MB）
                            Object currentSizeObj = Hawk.get(HawkConfig.CACHE_MAX_SIZE, 2048);
                            int currentSizeMB;
                            
                            // 处理不同类型的缓存大小值
                            if (currentSizeObj instanceof Integer) {
                                int value = (Integer) currentSizeObj;
                                // 如果值大于10MB，认为是字节单位，需要转换为MB
                                if (value > 10 * 1024 * 1024) {
                                    currentSizeMB = value / 1024 / 1024;
                                } else {
                                    // 小于10MB，可能已经是MB单位
                                    currentSizeMB = value;
                                }
                            } else if (currentSizeObj instanceof Long) {
                                long value = (Long) currentSizeObj;
                                // 如果值大于10MB，认为是字节单位，需要转换为MB
                                if (value > 10 * 1024 * 1024) {
                                    currentSizeMB = (int)(value / 1024 / 1024);
                                } else {
                                    // 小于10MB，可能已经是MB单位
                                    currentSizeMB = (int)value;
                                }
                            } else {
                                // 默认值
                                currentSizeMB = 2048;
                            }
                            
                            ArrayList<Integer> sizes = new ArrayList<>();
                            sizes.add(50);
                            sizes.add(100);
                            sizes.add(200);
                            sizes.add(512);
                            sizes.add(1024);
                            sizes.add(2048);
                            int defaultIndex = 0;
                            for (int i = 0; i < sizes.size(); i++) {
                                if (currentSizeMB == sizes.get(i)) {
                                    defaultIndex = i;
                                    break;
                                }
                            }
                            
                            // 直接设置，不使用对话框
                            int nextIndex = (defaultIndex + 1) % sizes.size();
                            int nextSize = sizes.get(nextIndex);
                            
                            // 直接应用设置
                            tvCacheMaxSize.setText(nextSize + "MB");
                            // 存储为字节单位，与CacheManager中的定义保持一致
                            Hawk.put(HawkConfig.CACHE_MAX_SIZE, nextSize * 1024 * 1024);
                            CacheManager.SmartCacheCleaner.smartCleanCache(CacheManager.SmartCacheCleaner.CLEAN_MODE_MINIMAL);
                            LOG.i("ModelSettingFragment cacheMaxSize directly changed to: " + nextSize + "MB");
                            showMessage("已设置缓存大小为: " + nextSize + "MB");
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment cacheMaxSize click error: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llCacheMaxSize click listener");
            }
            
            // 视频预加载设置
            LinearLayout llPreloadEnable = getView().findViewById(R.id.llPreloadEnable);
            if (llPreloadEnable != null && tvPreloadEnable != null) {
                llPreloadEnable.setOnClickListener(view -> {
                    try {
                        if (FastClickCheckUtil.check(view)) {
                            boolean isEnabled = !Hawk.get(HawkConfig.CACHE_PLAY_URL_ENABLE, true);
                            Hawk.put(HawkConfig.CACHE_PLAY_URL_ENABLE, isEnabled);
                            tvPreloadEnable.setText(isEnabled ? "开启" : "关闭");
                            
                            // 显示预加载状态
                            showPreloadStatus();
                            LOG.i("ModelSettingFragment preload enable changed to: " + (isEnabled ? "开启" : "关闭"));
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment preload click error: " + e.getMessage());
                    }
                });
                
                // 添加长按事件，用于测试预加载功能
                llPreloadEnable.setOnLongClickListener(v -> {
                    try {
                        LOG.i("长按预加载按钮，测试预加载功能");
                        PreloadHelper.getInstance().testPreloadFunction(mActivity);
                        return true;
                    } catch (Exception e) {
                        LOG.e("测试预加载功能失败: " + e.getMessage());
                        return false;
                    }
                });
                LOG.i("ModelSettingFragment set llPreloadEnable click and long-click listener");
            }
            
            // 缓存优先级设置
            LinearLayout llCachePriorityEnable = getView().findViewById(R.id.llCachePriorityEnable);
            if (llCachePriorityEnable != null && tvCachePriorityEnable != null) {
                llCachePriorityEnable.setOnClickListener(view -> {
                    try {
                        if (FastClickCheckUtil.check(view)) {
                            boolean currentEnabled = Hawk.get(HawkConfig.CACHE_PRIORITY_ENABLE, false);
                            boolean newEnabled = !currentEnabled;
                            Hawk.put(HawkConfig.CACHE_PRIORITY_ENABLE, newEnabled);
                            tvCachePriorityEnable.setText(newEnabled ? "开启" : "关闭");
                            LOG.i("ModelSettingFragment cachePriorityEnable changed to: " + (newEnabled ? "开启" : "关闭"));
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment cachePriorityEnable click error: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llCachePriorityEnable click listener");
            }
            
            // 自动清理设置
            LinearLayout llCacheAutoClean = getView().findViewById(R.id.llCacheAutoClean);
            if (llCacheAutoClean != null && tvCacheAutoClean != null) {
                llCacheAutoClean.setOnClickListener(view -> {
                    try {
                        if (FastClickCheckUtil.check(view)) {
                            boolean currentClean = Hawk.get(HawkConfig.CACHE_VIDEO_AUTO_CLEAN, false);
                            boolean newClean = !currentClean;
                            Hawk.put(HawkConfig.CACHE_VIDEO_AUTO_CLEAN, newClean);
                            tvCacheAutoClean.setText(newClean ? "开启" : "关闭");
                            LOG.i("ModelSettingFragment cacheAutoClean changed to: " + (newClean ? "开启" : "关闭"));
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment cacheAutoClean click error: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llCacheAutoClean click listener");
            }
            
            // 清理天数设置
            LinearLayout llCacheAutoCleanDays = getView().findViewById(R.id.llCacheAutoCleanDays);
            if (llCacheAutoCleanDays != null && tvCacheAutoCleanDays != null) {
                llCacheAutoCleanDays.setOnClickListener(view -> {
                    try {
                        if (FastClickCheckUtil.check(view)) {
                            LOG.i("ModelSettingFragment cache auto clean days click");
                            int currentDays = Hawk.get(HawkConfig.CACHE_AUTO_CLEAN_DAYS, 7);
                            ArrayList<Integer> days = new ArrayList<>();
                            days.add(1);
                            days.add(3);
                            days.add(7);
                            days.add(14);
                            days.add(30);
                            int defaultIndex = 2;
                            for (int i = 0; i < days.size(); i++) {
                                if (currentDays == days.get(i)) {
                                    defaultIndex = i;
                                    break;
                                }
                            }
                            
                            // 直接设置，不使用对话框
                            int nextIndex = (defaultIndex + 1) % days.size();
                            int nextDays = days.get(nextIndex);
                            
                            // 直接应用设置
                            tvCacheAutoCleanDays.setText(nextDays + "天");
                            Hawk.put(HawkConfig.CACHE_AUTO_CLEAN_DAYS, nextDays);
                            LOG.i("ModelSettingFragment cacheAutoCleanDays directly changed to: " + nextDays + "天");
                            showMessage("已设置自动清理天数为: " + nextDays + "天");
                            
                            if (Hawk.get(HawkConfig.CACHE_VIDEO_AUTO_CLEAN, false)) {
                                showLoading();
                                new Thread(() -> {
                                    try {
                                        long freedSpace = CacheManager.SmartCacheCleaner.cleanOldCache();
                                        mActivity.runOnUiThread(() -> {
                                            updateCacheSize();
                                            hideLoading();
                                            showMessage("自动清理完成，释放：" + (freedSpace / 1024 / 1024) + "MB");
                                        });
                                    } catch (Exception e) {
                                        LOG.e("ModelSettingFragment cleanOldCache error: " + e.getMessage());
                                        mActivity.runOnUiThread(() -> {
                                            hideLoading();
                                            showMessage("清理失败：" + e.getMessage());
                                        });
                                    }
                                }).start();
                            }
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment cacheAutoCleanDays click error: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llCacheAutoCleanDays click listener");
            }
            
            // 缓存分析按钮
            if (llCacheAnalysis != null) {
                llCacheAnalysis.setOnClickListener(view -> {
                    try {
                        if (FastClickCheckUtil.check(view)) {
                            LOG.i("ModelSettingFragment starting simplified cache analysis");
                            DebugHelper.logDebug("ModelSettingFragment 开始简化缓存分析");
                            // 使用DebugHelper替代直接Toast调用
                            showMessage("正在分析缓存...");
                            
                            new Thread(() -> {
                                try {
                                    long totalSize = CacheManager.getTotalCacheSize();
                                    int count = CacheManager.getCacheCount();
                                    
                                    final String report = "缓存统计：共" + count + "项，总大小" + (totalSize / 1024 / 1024) + "MB";
                                    LOG.i("ModelSettingFragment simplified cache analysis: " + report);
                                    DebugHelper.logDebug("ModelSettingFragment 缓存分析结果: " + report);
                                    
                                    mActivity.runOnUiThread(() -> {
                                        updateCacheSize();
                                        // 使用DebugHelper替代直接Toast调用
                                        showMessage(report);
                                    });
                                } catch (Exception e) {
                                    LOG.e("ModelSettingFragment cache analysis error: " + e.getMessage());
                                    DebugHelper.logError("ModelSettingFragment 缓存分析错误", e);
                                    mActivity.runOnUiThread(() -> {
                                        // 使用DebugHelper替代直接Toast调用
                                        showMessage("分析缓存失败：" + e.getMessage());
                                    });
                                }
                            }).start();
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment cacheAnalysis click error: " + e.getMessage());
                        DebugHelper.logError("ModelSettingFragment 缓存分析点击错误", e);
                    }
                });
                LOG.i("ModelSettingFragment set llCacheAnalysis click listener");
            }
            
            // 添加设置其他按钮的点击事件
            LinearLayout llSetOthers = getView().findViewById(R.id.llSetOthers);
            if (llSetOthers != null) {
                llSetOthers.setOnClickListener(v -> {
                    try {
                        if (FastClickCheckUtil.check(v)) {
                            Bundle bundle = new Bundle();
                            bundle.putBoolean("useCache", true);
                            openActivity(SettingActivity.class, bundle);
                            LOG.i("ModelSettingFragment clicked on settings others");
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment settings others click error: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llSetOthers click listener");
            }
            
            // 关于按钮
            LinearLayout llAbout = getView().findViewById(R.id.llAbout);
            if (llAbout != null) {
                llAbout.setOnClickListener(v -> {
                    try {
                        if (FastClickCheckUtil.check(v)) {
                            AboutDialog dialog = new AboutDialog(mActivity);
                            dialog.show();
                            LOG.i("ModelSettingFragment clicked on about");
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment about click error: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llAbout click listener");
            }
            
            // 备份/恢复按钮
            LinearLayout llBackup = getView().findViewById(R.id.llBackup);
            if (llBackup != null) {
                llBackup.setOnClickListener(v -> {
                    try {
                        if (FastClickCheckUtil.check(v)) {
                            BackupDialog dialog = new BackupDialog(mActivity);
                            dialog.show();
                            LOG.i("ModelSettingFragment clicked on backup");
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment backup click error: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llBackup click listener");
            }
            
            // 视频预览设置
            LinearLayout showPreview = getView().findViewById(R.id.showPreview);
            if (showPreview != null && tvShowPreviewText != null) {
                showPreview.setOnClickListener(v -> {
                    try {
                        if (FastClickCheckUtil.check(v)) {
                            Hawk.put(HawkConfig.SHOW_PREVIEW, !Hawk.get(HawkConfig.SHOW_PREVIEW, true));
                            tvShowPreviewText.setText(Hawk.get(HawkConfig.SHOW_PREVIEW, true) ? "开启" : "关闭");
                            LOG.i("ModelSettingFragment show preview changed to: " + (Hawk.get(HawkConfig.SHOW_PREVIEW, true) ? "开启" : "关闭"));
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment show preview click error: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set showPreview click listener");
            }
            
            // 渲染方式
            LinearLayout llRender = getView().findViewById(R.id.llRender);
            if (llRender != null && tvRender != null) {
                llRender.setOnClickListener(v -> {
                    try {
                        if (FastClickCheckUtil.check(v)) {
                            int defaultRender = Hawk.get(HawkConfig.PLAY_RENDER, 0);
                            ArrayList<Integer> renders = new ArrayList<>();
                            renders.add(0);
                            renders.add(1);
                            renders.add(2);
                            SelectDialog<Integer> dialog = new SelectDialog<>(mActivity);
                            dialog.setTip("请选择默认渲染方式");
                            dialog.setAdapter(new SelectDialogAdapter.SelectDialogInterface<Integer>() {
                                @Override
                                public void click(Integer value, int pos) {
                                    tvRender.setText(PlayerHelper.getRenderName(value));
                                    Hawk.put(HawkConfig.PLAY_RENDER, value);
                                    LOG.i("ModelSettingFragment render changed to: " + PlayerHelper.getRenderName(value));
                                }

                                @Override
                                public String getDisplay(Integer val) {
                                    return PlayerHelper.getRenderName(val);
                                }
                            }, new DiffUtil.ItemCallback<Integer>() {
                                @Override
                                public boolean areItemsTheSame(@NonNull Integer oldItem, @NonNull Integer newItem) {
                                    return oldItem.intValue() == newItem.intValue();
                                }

                                @Override
                                public boolean areContentsTheSame(@NonNull Integer oldItem, @NonNull Integer newItem) {
                                    return oldItem.intValue() == newItem.intValue();
                                }
                            }, renders, defaultRender);
                            dialog.show();
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment render click error: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llRender click listener");
            }
            
            // 首页源设置
            LinearLayout llHomeApi = getView().findViewById(R.id.llHomeApi);
            if (llHomeApi != null && tvHomeApi != null) {
                llHomeApi.setOnClickListener(v -> {
                    try {
                        if (FastClickCheckUtil.check(v)) {
                            if (ApiConfig.get().getSourceBeanList().isEmpty()) {
                                showMessage("请先加载数据源接口");
                                return;
                            }
                            
                            List<SourceBean> apis = ApiConfig.get().getSourceBeanList();
                            if (apis.isEmpty()) {
                                return;
                            }
                            
                            String homeSourceKey = ApiConfig.get().getHomeSourceBean().getKey();
                            
                            SelectDialog<SourceBean> dialog = new SelectDialog<>(mActivity);
                            dialog.setTip("请选择首页数据源");
                            dialog.setAdapter(new SelectDialogAdapter.SelectDialogInterface<SourceBean>() {
                                @Override
                                public void click(SourceBean item, int pos) {
                                    ApiConfig.get().setSourceBean(item);
                                    tvHomeApi.setText(item.getName());
                                    showMessage("设置成功，重启应用生效");
                                    LOG.i("ModelSettingFragment homeApi changed to: " + item.getName());
                                }

                                @Override
                                public String getDisplay(SourceBean item) {
                                    return item.getName();
                                }
                            }, new DiffUtil.ItemCallback<SourceBean>() {
                                @Override
                                public boolean areItemsTheSame(@NonNull SourceBean oldItem, @NonNull SourceBean newItem) {
                                    return oldItem == newItem;
                                }

                                @Override
                                public boolean areContentsTheSame(@NonNull SourceBean oldItem, @NonNull SourceBean newItem) {
                                    return oldItem.getKey().equals(newItem.getKey());
                                }
                            }, apis, getHomeSourceIndex(apis, homeSourceKey));
                            dialog.show();
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment homeApi click error: " + e.getMessage());
                        showMessage("设置首页源失败：" + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llHomeApi click listener");
            }
            
            // 搜索视图
            LinearLayout llSearchView = getView().findViewById(R.id.llSearchView);
            if (llSearchView != null && tvSearchView != null) {
                llSearchView.setOnClickListener(v -> {
                    try {
                        if (FastClickCheckUtil.check(v)) {
                            int defaultView = Hawk.get(HawkConfig.SEARCH_VIEW, 0);
                            ArrayList<Integer> views = new ArrayList<>();
                            views.add(0);
                            views.add(1);
                            SelectDialog<Integer> dialog = new SelectDialog<>(mActivity);
                            dialog.setTip("请选择搜索视图");
                            dialog.setAdapter(new SelectDialogAdapter.SelectDialogInterface<Integer>() {
                                @Override
                                public void click(Integer value, int pos) {
                                    tvSearchView.setText(getSearchView(value));
                                    Hawk.put(HawkConfig.SEARCH_VIEW, value);
                                    LOG.i("ModelSettingFragment search view changed to: " + getSearchView(value));
                                }

                                @Override
                                public String getDisplay(Integer val) {
                                    return getSearchView(val);
                                }
                            }, new DiffUtil.ItemCallback<Integer>() {
                                @Override
                                public boolean areItemsTheSame(@NonNull Integer oldItem, @NonNull Integer newItem) {
                                    return oldItem.intValue() == newItem.intValue();
                                }

                                @Override
                                public boolean areContentsTheSame(@NonNull Integer oldItem, @NonNull Integer newItem) {
                                    return oldItem.intValue() == newItem.intValue();
                                }
                            }, views, defaultView);
                            dialog.show();
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment search view click error: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llSearchView click listener");
            }
            
            // 安全DNS
            LinearLayout llDns = getView().findViewById(R.id.llDns);
            if (llDns != null && tvDns != null) {
                llDns.setOnClickListener(v -> {
                    try {
                        if (FastClickCheckUtil.check(v)) {
                            int dohUrl = Hawk.get(HawkConfig.DOH_URL, 0);
                            
                            SelectDialog<Integer> dialog = new SelectDialog<>(mActivity);
                            dialog.setTip("请选择DNS服务器");
                            dialog.setAdapter(new SelectDialogAdapter.SelectDialogInterface<Integer>() {
                                @Override
                                public void click(Integer value, int pos) {
                                    tvDns.setText(OkGoHelper.dnsHttpsList.get(value));
                                    Hawk.put(HawkConfig.DOH_URL, value);
                                    LOG.i("ModelSettingFragment DNS changed to: " + OkGoHelper.dnsHttpsList.get(value));
                                }

                                @Override
                                public String getDisplay(Integer val) {
                                    return OkGoHelper.dnsHttpsList.get(val);
                                }
                            }, new DiffUtil.ItemCallback<Integer>() {
                                @Override
                                public boolean areItemsTheSame(@NonNull Integer oldItem, @NonNull Integer newItem) {
                                    return oldItem.intValue() == newItem.intValue();
                                }

                                @Override
                                public boolean areContentsTheSame(@NonNull Integer oldItem, @NonNull Integer newItem) {
                                    return oldItem.intValue() == newItem.intValue();
                                }
                            }, createIndexList(OkGoHelper.dnsHttpsList.size()), dohUrl);
                            dialog.show();
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment dns click error: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llDns click listener");
            }
            
            // 主题
            LinearLayout llTheme = getView().findViewById(R.id.llTheme);
            if (llTheme != null && tvTheme != null) {
                llTheme.setOnClickListener(v -> {
                    try {
                        // 显示调试信息，确认点击触发
                        //处理点击事件
                        // 注释掉这行代码以避免显示调试Toast和相关堆栈跟踪
                        // DebugHelper.showToast(mActivity, "点击了主题设置按钮");
                        DebugHelper.logDebug("点击了主题设置按钮 - 开始处理");
                        
                        if (FastClickCheckUtil.check(v)) {
                            DebugHelper.logDebug("FastClickCheckUtil通过校验");
                            
                            int defaultTheme = Hawk.get(HawkConfig.THEME_SELECT, 0);
                            ArrayList<Integer> themes = new ArrayList<>();
                            themes.add(0);
                            themes.add(1);
                            themes.add(2);
                            themes.add(3);
                            themes.add(4);
                            themes.add(5);
                            themes.add(6);
                            
                            DebugHelper.logDebug("准备显示主题对话框");
                            
                            try {
                                SelectDialog<Integer> dialog = new SelectDialog<>(mActivity);
                                dialog.setTip("请选择主题");
                                dialog.setAdapter(new SelectDialogAdapter.SelectDialogInterface<Integer>() {
                                    @Override
                                    public void click(Integer value, int pos) {
                                        DebugHelper.logDebug("选择了主题: " + getThemeView(value));
                                        tvTheme.setText(getThemeView(value));
                                        Hawk.put(HawkConfig.THEME_SELECT, value);
                                        LOG.i("ModelSettingFragment theme changed to: " + getThemeView(value));
                                        
                                        // 重新加载应用以应用新主题
                                        if (mActivity != null) {
                                            mActivity.recreate();
                                        }
                                    }

                                    @Override
                                    public String getDisplay(Integer val) {
                                        return getThemeView(val);
                                    }
                                }, new DiffUtil.ItemCallback<Integer>() {
                                    @Override
                                    public boolean areItemsTheSame(@NonNull Integer oldItem, @NonNull Integer newItem) {
                                        return oldItem.intValue() == newItem.intValue();
                                    }

                                    @Override
                                    public boolean areContentsTheSame(@NonNull Integer oldItem, @NonNull Integer newItem) {
                                        return oldItem.intValue() == newItem.intValue();
                                    }
                                }, themes, defaultTheme);
                                dialog.show();
                                DebugHelper.logDebug("主题对话框已显示");
                            } catch (Exception dialogEx) {
                                DebugHelper.logError("显示主题对话框出错", dialogEx);
                                showMessage("显示主题选择对话框出错: " + dialogEx.getMessage());
                            }
                        } else {
                            DebugHelper.logDebug("FastClickCheckUtil未通过校验，点击被忽略");
                        }
                    } catch (Exception e) {
                        DebugHelper.logError("主题设置点击处理出错", e);
                        LOG.e("ModelSettingFragment theme click error: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llTheme click listener");
            } else {
                DebugHelper.logError("llTheme或tvTheme为null，无法设置点击监听器", null);
            }
            
            // 自动更新
            LinearLayout llAutoUpdate = getView().findViewById(R.id.llAutoUpdate);
            if (llAutoUpdate != null && tvAutoUpdate != null) {
                llAutoUpdate.setOnClickListener(v -> {
                    try {
                        if (FastClickCheckUtil.check(v)) {
                            boolean autoUpdateEnabled = !Hawk.get(HawkConfig.AUTO_UPDATE_ENABLED, true);
                            Hawk.put(HawkConfig.AUTO_UPDATE_ENABLED, autoUpdateEnabled);
                            tvAutoUpdate.setText(autoUpdateEnabled ? "开启" : "关闭");
                            LOG.i("ModelSettingFragment auto update changed to: " + (autoUpdateEnabled ? "开启" : "关闭"));
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment auto update click error: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llAutoUpdate click listener");
            }
            
            // 更新间隔
            LinearLayout llUpdateInterval = getView().findViewById(R.id.llUpdateInterval);
            if (llUpdateInterval != null && tvUpdateInterval != null) {
                llUpdateInterval.setOnClickListener(v -> {
                    try {
                        if (FastClickCheckUtil.check(v)) {
                            long currentInterval = Hawk.get(HawkConfig.UPDATE_INTERVAL, 24 * 60 * 60 * 1000L);
                            ArrayList<Long> intervals = new ArrayList<>();
                            intervals.add(12 * 60 * 60 * 1000L);  // 12小时
                            intervals.add(24 * 60 * 60 * 1000L);  // 24小时
                            intervals.add(48 * 60 * 60 * 1000L);  // 48小时
                            intervals.add(7 * 24 * 60 * 60 * 1000L); // 7天
                            
                            ArrayList<String> names = new ArrayList<>();
                            names.add("12小时");
                            names.add("24小时");
                            names.add("48小时");
                            names.add("7天");
                            
                            int defaultIndex = 1; // 默认24小时
                            for (int i = 0; i < intervals.size(); i++) {
                                if (currentInterval == intervals.get(i)) {
                                    defaultIndex = i;
                                    break;
                                }
                            }
                            
                            SelectDialog<Integer> dialog = new SelectDialog<>(mActivity);
                            dialog.setTip("请选择更新检查间隔");
                            dialog.setAdapter(new SelectDialogAdapter.SelectDialogInterface<Integer>() {
                                @Override
                                public void click(Integer value, int pos) {
                                    long interval = intervals.get(pos);
                                    tvUpdateInterval.setText(names.get(pos));
                                    Hawk.put(HawkConfig.UPDATE_INTERVAL, interval);
                                    LOG.i("ModelSettingFragment update interval changed to: " + names.get(pos));
                                }

                                @Override
                                public String getDisplay(Integer val) {
                                    return names.get(val);
                                }
                            }, new DiffUtil.ItemCallback<Integer>() {
                                @Override
                                public boolean areItemsTheSame(@NonNull Integer oldItem, @NonNull Integer newItem) {
                                    return oldItem.intValue() == newItem.intValue();
                                }

                                @Override
                                public boolean areContentsTheSame(@NonNull Integer oldItem, @NonNull Integer newItem) {
                                    return oldItem.intValue() == newItem.intValue();
                                }
                            }, Arrays.asList(0, 1, 2, 3), defaultIndex);
                            dialog.show();
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment update interval click error: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llUpdateInterval click listener");
            }
            
            // 性能监控点击事件
            LinearLayout llPerformanceMonitor = getView().findViewById(R.id.llPerformanceMonitor);
            if (llPerformanceMonitor != null) {
                llPerformanceMonitor.setOnClickListener(v -> {
                    try {
                        if (FastClickCheckUtil.check(v)) {
                            LOG.i("ModelSettingFragment performance monitor click");
                            Intent intent = new Intent(mActivity, PerformanceActivity.class);
                            startActivity(intent);
                        }
                    } catch (Exception e) {
                        LOG.e("ModelSettingFragment performance monitor click error: " + e.getMessage());
                        showMessage("打开性能监控失败: " + e.getMessage());
                    }
                });
                LOG.i("ModelSettingFragment set llPerformanceMonitor click listener");
            }
            
            // 视频预缓冲开关
            LinearLayout llContentPreloadEnable = getView().findViewById(R.id.llContentPreloadEnable);
            if (llContentPreloadEnable != null && tvContentPreloadEnable != null) {
                llContentPreloadEnable.setOnClickListener(v -> {
                    try {
                        if (FastClickCheckUtil.check(v)) {
                            boolean enabled = !Hawk.get(HawkConfig.CACHE_VIDEO_CONTENT_PRELOAD_ENABLE, true);
                            Hawk.put(HawkConfig.CACHE_VIDEO_CONTENT_PRELOAD_ENABLE, enabled);
                            tvContentPreloadEnable.setText(enabled ? "开启" : "关闭");
                            
                            // 在修改设置后显示状态
                            showPreloadStatus();
                            LOG.i("视频内容预缓冲已" + (enabled ? "启用" : "禁用"));
                        }
                    } catch (Exception e) {
                        LOG.e("视频内容预缓冲设置失败: " + e.getMessage());
                    }
                });
                
                // 添加长按事件，用于清空预缓冲内容
                llContentPreloadEnable.setOnLongClickListener(v -> {
                    try {
                        LOG.i("长按视频内容预缓冲按钮，清空预缓冲内容");
                        PreloadHelper.getInstance().clearAllPreloadedContent(mActivity);
                        return true;
                    } catch (Exception e) {
                        LOG.e("清空视频预缓冲内容失败: " + e.getMessage());
                        return false;
                    }
                });
                LOG.i("ModelSettingFragment set llContentPreloadEnable click listener");
            }
            
            // 删除运营商类型点击监听器的代码
            
            LOG.i("ModelSettingFragment setClickListeners completed");
        } catch (Exception e) {
            LOG.e("ModelSettingFragment setClickListeners error: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    protected void showLoading() {
        if (mLoadingDialog == null) {
            mLoadingDialog = new LoadingDialog(mActivity);
        }
        mLoadingDialog.show();
    }

    protected void hideLoading() {
        if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
            mLoadingDialog.dismiss();
        }
    }

    private void onClickClearCache(View view) {
        AlertDialog.Builder builder = new AlertDialog.Builder(mActivity);
        builder.setTitle("清理缓存");
        builder.setMessage("确定要清理全部缓存吗？");
        builder.setPositiveButton("确定", (dialog, which) -> {
            showLoading();
            
            new Thread(() -> {
                try {
                    // 1. 清理数据库缓存
                    CacheManager.getInstance().clearAllCache();
                    
                    // 2. 清理文件系统缓存
                    File cacheDir = CacheManager.getInstance().getCacheDir();
                    FileUtils.cleanDirectory(cacheDir);
                    
                    // 3. 清理播放器缓存
                    FileUtils.cleanPlayerCache();
                    
                    mActivity.runOnUiThread(() -> {
                        hideLoading();
                        updateCacheSize();
                        showMessage("缓存清理完成");
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                    mActivity.runOnUiThread(() -> {
                        hideLoading();
                        showMessage("清理缓存失败：" + e.getMessage());
                    });
                }
            }).start();
        });
        builder.setNegativeButton("取消", null);
        builder.show();
    }

    private void updateCacheSize() {
        try {
            LOG.i("ModelSettingFragment updateCacheSize start");
            View llClearCache = findViewById(R.id.llClearCache);
            if (llClearCache != null) {
                TextView tvCacheSize = llClearCache.findViewById(R.id.settingItemValue);
                if (tvCacheSize != null) {
                    try {
                        // 预设一个默认值，避免任何情况下都能显示合理值
                        long totalSize = 0;
                        
                        try {
                            // 计算总缓存大小 = 数据库缓存 + 文件系统缓存
                            long dbSize = 0;
                            try {
                                dbSize = Math.max(0, AppDataManager.get().getCacheDao().getTotalSize());
                            } catch (Exception e) {
                                LOG.e("获取数据库缓存大小出错: " + e.getMessage());
                            }
                            
                            long fileSize = 0;
                            try {
                                File cacheDir = CacheManager.getInstance().getCacheDir();
                                if (cacheDir != null && cacheDir.exists()) {
                                    fileSize = Math.max(0, FileUtils.getFolderSize(cacheDir));
                                }
                            } catch (Exception e) {
                                LOG.e("获取文件缓存大小出错: " + e.getMessage());
                            }
                            
                            // 获取播放器缓存大小
                            long playerCacheSize = 0;
                            try {
                                String ijkCachePath = FileUtils.getCachePath() + "/ijkcaches/";
                                String thunderCachePath = FileUtils.getCachePath() + "/thunder/";
                                String exoCachePath = FileUtils.getCachePath() + "/exo-video-cache/";
                                
                                if (new File(ijkCachePath).exists()) {
                                    playerCacheSize += Math.max(0, FileUtils.getFolderSize(new File(ijkCachePath)));
                                }
                                if (new File(thunderCachePath).exists()) {
                                    playerCacheSize += Math.max(0, FileUtils.getFolderSize(new File(thunderCachePath)));
                                }
                                if (new File(exoCachePath).exists()) {
                                    playerCacheSize += Math.max(0, FileUtils.getFolderSize(new File(exoCachePath)));
                                }
                            } catch (Exception e) {
                                LOG.e("获取播放器缓存大小出错: " + e.getMessage());
                            }
                            
                            // 防止各项之和溢出
                            // 先检查每一步加法是否会导致long溢出，如果会溢出，则使用Long.MAX_VALUE
                            long tmp = dbSize;
                            if (Long.MAX_VALUE - tmp < fileSize) {
                                totalSize = Long.MAX_VALUE;
                            } else {
                                tmp += fileSize;
                                if (Long.MAX_VALUE - tmp < playerCacheSize) {
                                    totalSize = Long.MAX_VALUE;
                                } else {
                                    totalSize = tmp + playerCacheSize;
                                }
                            }
                            
                            // 确保总大小非负
                            totalSize = Math.max(0, totalSize);
                            
                            LOG.i("ModelSettingFragment 缓存大小明细: DB:" + dbSize + ", File:" + fileSize + ", Player:" + playerCacheSize + ", Total:" + totalSize);
                        } catch (Exception e) {
                            LOG.e("计算缓存大小出错: " + e.getMessage());
                            e.printStackTrace();
                            // 出错时使用默认值
                            totalSize = 0;
                        }
                        
                        // 转换为MB显示
                        String sizeText;
                        if (totalSize >= 0) {
                            sizeText = String.format("%.2f MB", totalSize / 1024.0 / 1024.0);
                        } else {
                            // 如果仍然出现负数，强制使用正数显示
                            sizeText = String.format("%.2f MB", 0.0);
                            LOG.e("计算出现负数缓存大小: " + totalSize + "，已修正为0");
                        }
                        tvCacheSize.setText(sizeText);
                        LOG.i("ModelSettingFragment updateCacheSize: " + sizeText);
                    } catch (Exception e) {
                        LOG.e("显示缓存大小出错: " + e.getMessage());
                        e.printStackTrace();
                        // 出现任何错误都显示默认值
                        tvCacheSize.setText("0.00 MB");
                    }
                } else {
                    LOG.e("ModelSettingFragment tvCacheSize view missing");
                }
            } else {
                LOG.e("ModelSettingFragment llClearCache view missing");
            }
        } catch (Exception e) {
            LOG.e("ModelSettingFragment updateCacheSize error: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void initCacheSettings() {
        LOG.i("ModelSettingFragment initCacheSettings start");
        try {
            updateCacheSize();
            
            // 确保缓存相关控件按钮可用
            LinearLayout[] cacheElements = {
                findViewById(R.id.llClearCache),
                findViewById(R.id.llCacheMaxSize),
                findViewById(R.id.llCachePriorityEnable),
                findViewById(R.id.llCacheAutoClean),
                findViewById(R.id.llCacheAutoCleanDays),
                findViewById(R.id.llPreloadEnable),
                findViewById(R.id.llCacheAnalysis)
            };
            
            for (LinearLayout element : cacheElements) {
                if (element != null) {
                    element.setVisibility(View.VISIBLE);
                    element.setFocusable(true);
                    element.findViewById(R.id.settingItemRoot).setFocusableInTouchMode(true);
                    LOG.i("缓存设置项可见: " + element.getId());
                } else {
                    LOG.e("缓存设置项为空: " + element);
                }
            }
            
            LOG.i("ModelSettingFragment initCacheSettings completed");
        } catch (Exception e) {
            LOG.e("ModelSettingFragment initCacheSettings error: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void onDestroyView() {
        // 不在此处清理回调，避免冲突
        super.onDestroyView();
    }

    @Override
    public void onDestroy() {
        try {
            // 如果是最后一个Fragment，则清理回调
            if (getActivity() != null && getActivity().isFinishing()) {
                SettingActivity.callback = null;
            }
        } catch (Exception e) {
            LOG.e("清理回调异常: " + e.getMessage());
        }
        super.onDestroy();
    }

    String getThemeView(int type) {
        if (type == 0) {
            return "奈飞";
        } else if (type == 1) {
            return "哆啦";
        } else if (type == 2) {
            return "百事";
        } else if (type == 3) {
            return "鸣人";
        } else if (type == 4) {
            return "小黄";
        } else if (type == 5) {
            return "八神";
        } else {
            return "樱花";
        }
    }

    void reloadActivity() {
        Intent intent = getActivity().getApplicationContext().getPackageManager().getLaunchIntentForPackage(getActivity().getApplication().getPackageName());
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP
                | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        Bundle bundle = new Bundle();
        bundle.putBoolean("useCache", true);
        intent.putExtras(bundle);
        getActivity().getApplicationContext().startActivity(intent);
    }

    String getSearchView(int type) {
        if (type == 0) {
            return "缩略图";
        } else {
            return "文字列表";
        }
    }

    private String getIntervalName(int hours) {
        switch (hours) {
            case 12:
                return "12小时";
            case 24:
                return "24小时";
            case 48:
                return "48小时";
            case 168:
                return "7天";
            default:
                return "24小时";
        }
    }
    
    private void openActivity(Class<?> cls, Bundle bundle) {
        try {
            Intent intent = new Intent(mActivity, cls);
            if (bundle != null) {
                intent.putExtras(bundle);
            }
            startActivity(intent);
        } catch (Exception e) {
            LOG.e("跳转失败：" + e.getMessage());
        }
    }

    private List<Integer> createIndexList(int size) {
        List<Integer> indexList = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            indexList.add(i);
        }
        return indexList;
    }

    private void ensureViewsVisibility() {
        try {
            // 主要设置项
            String[] mainSettings = {"llMediaCodec", "llParseWebVew", "llHomeApi", "llHomeShow", 
                    "showPreview", "llSearchView", "llRender", "llDns", "llTheme", 
                    "llAutoUpdate", "llUpdateInterval", "llSetOthers", "llAbout", "llBackup"};
            
            // 缓存设置项
            String[] cacheSettings = {"llClearCache", "llCacheMaxSize", "llCachePriorityEnable", 
                    "llCacheAutoClean", "llCacheAutoCleanDays", "llPreloadEnable", "llCacheAnalysis"};
            
            // 确保主要设置项可见
            for (String id : mainSettings) {
                View view = findViewById(getResources().getIdentifier(id, "id", mContext.getPackageName()));
                if (view != null) {
                    view.setVisibility(View.VISIBLE);
                    view.setFocusable(true);
                    view.setClickable(true);
                    LOG.i("ModelSettingFragment made visible and clickable: " + id);
                } else {
                    LOG.e("ModelSettingFragment could not find view: " + id);
                }
            }
            
            // 确保缓存设置项可见
            for (String id : cacheSettings) {
                View view = findViewById(getResources().getIdentifier(id, "id", mContext.getPackageName()));
                if (view != null) {
                    view.setVisibility(View.VISIBLE);
                    view.setFocusable(true);
                    view.setClickable(true);
                    LOG.i("ModelSettingFragment made visible and clickable: " + id);
                } else {
                    LOG.e("ModelSettingFragment could not find view: " + id);
                }
            }
            
            // 调试模式特殊处理
            View llDebug = findViewById(R.id.llDebug);
            if (llDebug != null) {
                llDebug.setVisibility(Hawk.get(HawkConfig.DEBUG_OPEN, false) ? View.VISIBLE : View.GONE);
                LOG.i("ModelSettingFragment llDebug visibility set: " + (Hawk.get(HawkConfig.DEBUG_OPEN, false) ? "VISIBLE" : "GONE"));
            }
        } catch (Exception e) {
            LOG.e("确保视图可见性失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private int getHomeSourceIndex(List<SourceBean> sources, String key) {
        for (int i = 0; i < sources.size(); i++) {
            if (sources.get(i).getKey().equals(key)) {
                return i;
            }
        }
        return 0; // 默认返回第一个
    }

    // 新增方法：增强TV环境下的焦点处理
    private void enhanceTvFocusability() {
        try {
            LOG.i("ModelSettingFragment enhanceTvFocusability start");
            
            // 获取所有设置项布局
            LinearLayout[] settingItems = {
                findViewById(R.id.llDebug),
                findViewById(R.id.llMediaCodec),
                findViewById(R.id.llParseWebVew),
                findViewById(R.id.llRender),
                findViewById(R.id.llHomeApi),
                findViewById(R.id.llHomeShow),
                findViewById(R.id.showPreview),
                findViewById(R.id.llSearchView),
                findViewById(R.id.llTheme),
                findViewById(R.id.llDns),
                findViewById(R.id.llAutoUpdate),
                findViewById(R.id.llUpdateInterval),
                findViewById(R.id.llClearCache),
                findViewById(R.id.llCacheMaxSize),
                findViewById(R.id.llCachePriorityEnable),
                findViewById(R.id.llCacheAutoClean),
                findViewById(R.id.llCacheAutoCleanDays),
                findViewById(R.id.llPreloadEnable),
                findViewById(R.id.llCacheAnalysis),
                findViewById(R.id.llBackup),
                findViewById(R.id.llAbout),
                findViewById(R.id.llSetOthers)
            };
            
            // 为每个设置项设置焦点属性
            for (LinearLayout item : settingItems) {
                if (item != null) {
                    // 设置可聚焦
                    item.setFocusable(true);
                    item.setFocusableInTouchMode(true);
                    item.setClickable(true);
                    
                    // 设置焦点变化监听器，确保焦点状态正确显示
                    item.setOnFocusChangeListener((v, hasFocus) -> {
                        if (hasFocus) {
                            LOG.i("设置项获得焦点: " + v.getId());
                            v.setBackgroundResource(R.drawable.shape_setting_item_focus);
                        } else {
                            v.setBackgroundResource(R.drawable.shape_setting_item_normal);
                        }
                    });
                    
                    // 添加按键监听器，处理遥控器OK键
                    item.setOnKeyListener((v, keyCode, event) -> {
                        if (event.getAction() == android.view.KeyEvent.ACTION_DOWN 
                                && (keyCode == android.view.KeyEvent.KEYCODE_ENTER 
                                    || keyCode == android.view.KeyEvent.KEYCODE_DPAD_CENTER)) {
                            LOG.i("设置项接收到OK键: " + v.getId());
                            // 触发点击事件
                            v.performClick();
                            return true;
                        }
                        return false;
                    });
                    
                    LOG.i("增强了设置项的焦点处理: " + item.getId());
                }
            }
            
            // 设置默认初始焦点项
            LinearLayout firstItem = findViewById(R.id.llMediaCodec);
            if (firstItem != null) {
                firstItem.requestFocus();
                LOG.i("设置初始焦点到第一个设置项");
            }
            
            LOG.i("ModelSettingFragment enhanceTvFocusability completed");
        } catch (Exception e) {
            LOG.e("设置焦点处理失败: " + e.getMessage());
            DebugHelper.logError("设置焦点处理失败", e);
        }
    }

    /**
     * 显示预加载功能状态
     */
    private void showPreloadStatus() {
        try {
            new Thread(() -> {
                try {
                    // 获取预加载状态报告
                    String statusReport = PreloadHelper.getInstance().checkPreloadStatus();
                    
                    // 在UI线程显示
                    mActivity.runOnUiThread(() -> {
                        try {
                            // 只有在调试模式下显示详细状态
                            if (Hawk.get(HawkConfig.DEBUG_OPEN, false)) {
                                AlertDialog.Builder builder = new AlertDialog.Builder(mActivity);
                                builder.setTitle("预加载功能状态");
                                builder.setMessage(statusReport);
                                builder.setPositiveButton("确定", null);
                                builder.show();
                            } else {
                                // 非调试模式只显示简短提示
                                boolean isEnabled = Hawk.get(HawkConfig.CACHE_PLAY_URL_ENABLE, true);
                                showMessage(
                                    "预加载功能已" + (isEnabled ? "启用" : "禁用") + 
                                    "\n长按可测试预加载功能");
                            }
                        } catch (Exception e) {
                            LOG.e("显示预加载状态对话框失败: " + e.getMessage());
                        }
                    });
                } catch (Exception e) {
                    LOG.e("获取预加载状态失败: " + e.getMessage());
                }
            }).start();
        } catch (Exception e) {
            LOG.e("显示预加载状态失败: " + e.getMessage());
        }
    }

    /**
     * 自定义消息显示方法，避免Toast堆栈跟踪
     */
    private void showMessage(String message) {
        if (mActivity != null && !mActivity.isFinishing()) {
            try {
                // 使用DebugHelper替代直接Toast调用
                DebugHelper.showToast(mActivity, message);
            } catch (Exception e) {
                // 如果DebugHelper调用失败，使用标准Toast作为备选
                try {
                    Toast.makeText(mActivity, message, Toast.LENGTH_SHORT).show();
                } catch (Exception e2) {
                    LOG.e("显示消息失败: " + e2.getMessage());
                }
            }
        }
    }
    
    /**
     * 显示长时间消息
     */
    private void showLongMessage(String message) {
        if (mActivity != null && !mActivity.isFinishing()) {
            try {
                // 使用DebugHelper替代直接Toast调用
                DebugHelper.showToast(mActivity, message);
            } catch (Exception e) {
                // 如果DebugHelper调用失败，使用标准Toast作为备选
                try {
                    Toast.makeText(mActivity, message, Toast.LENGTH_LONG).show();
                } catch (Exception e2) {
                    LOG.e("显示长消息失败: " + e2.getMessage());
                }
            }
        }
    }
}