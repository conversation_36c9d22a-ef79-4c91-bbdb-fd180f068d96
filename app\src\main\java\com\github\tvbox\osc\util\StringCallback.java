package com.github.tvbox.osc.util;

import com.lzy.okgo.callback.AbsCallback;
import com.lzy.okgo.model.Response;

import java.io.IOException;

/**
 * String类型的OkGo回调
 */
public abstract class StringCallback extends AbsCallback<String> {
    @Override
    public String convertResponse(okhttp3.Response response) throws IOException {
        if (response.body() == null) return null;
        return response.body().string();
    }
    
    @Override
    public void onSuccess(Response<String> response) {
        // 实现类需要重写此方法
    }
    
    @Override
    public void onError(Response<String> response) {
        // 实现类需要重写此方法
    }
} 