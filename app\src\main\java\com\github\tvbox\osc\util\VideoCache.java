package com.github.tvbox.osc.util;

import android.text.TextUtils;
import android.util.Log;

import com.github.tvbox.osc.base.App;
import com.github.tvbox.osc.cache.Cache;
import com.github.tvbox.osc.cache.CacheManager;
import com.github.tvbox.osc.data.AppDataManager;
import com.github.tvbox.osc.util.HawkConfig;
import com.orhanobut.hawk.Hawk;

import java.io.File;
import java.io.FileOutputStream;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 视频缓存系统便捷接口
 * 作为视频缓存系统的统一入口，方便其他组件调用缓存功能
 */
public class VideoCache {
    private static final String TAG = "VideoCache";
    
    /**
     * 缓存有效性参数
     */
    private static final long CACHE_VALID_TIME = 7 * 24 * 60 * 60 * 1000L; // 缓存有效期：7天
    private static final long CACHE_MAX_VALID_TIME = 30 * 24 * 60 * 60 * 1000L; // 最大缓存有效期：30天
    private static final long CACHE_CHECK_INTERVAL = 8 * 60 * 60 * 1000L; // 缓存检查间隔：8小时

    /**
     * 缓存策略参数 
     */
    private static final boolean DEFAULT_CACHE_ENABLED = true; // 默认启用缓存
    private static final boolean DEFAULT_TS_SEGMENT_ENABLED = true; // 默认启用TS片段缓存
    private static final boolean DEFAULT_SILENT_MODE = false; // 默认非静默模式
    private static final long DEFAULT_MAX_CACHE_SIZE = 2L * 1024 * 1024 * 1024; // 默认缓存上限：2GB
    private static final float CACHE_CLEAN_THRESHOLD = 0.8f; // 缓存清理阈值：80%

    /**
     * 配置参数 - 可通过HawkConfig设置
     */
    private static boolean cacheEnabled = DEFAULT_CACHE_ENABLED;
    private static boolean tsSegmentEnabled = DEFAULT_TS_SEGMENT_ENABLED;
    private static boolean silentMode = DEFAULT_SILENT_MODE;
    private static long maxCacheSize = DEFAULT_MAX_CACHE_SIZE;

    /**
     * 内存索引 - 提高查找效率
     */
    private static final Map<String, CacheIndexEntry> memoryIndex = new ConcurrentHashMap<>();
    private static boolean memoryIndexInitialized = false;

    /**
     * 最后一次缓存检查时间
     */
    private static long lastCacheCheckTime = 0;
    
    /**
     * 缓存连接的视频加载监听器
     */
    private static final Map<String, AdaptiveVideoLoader.LoadListener> loaderListeners = new ConcurrentHashMap<>();

    // 缓存目录
    private static File cacheDir;
    
    // 线程池管理
    private static final ExecutorService THREAD_POOL = Executors.newCachedThreadPool();

    /**
     * 初始化缓存目录
     */
    private static void initCacheDir() {
        if (cacheDir != null && cacheDir.exists()) {
            return;
        }
        
        File baseDir = App.getInstance().getExternalCacheDir();
        if (baseDir == null) {
            baseDir = App.getInstance().getCacheDir();
        }
        
        cacheDir = new File(baseDir, "video_cache");
        if (!cacheDir.exists()) {
            cacheDir.mkdirs();
        }
    }
    
    /**
     * 获取缓存目录
     */
    public static File getCacheDir() {
        if (cacheDir == null) {
            initCacheDir();
        }
        return cacheDir;
    }

    /**
     * 初始化配置
     */
    public static void init() {
        // 从配置中加载设置
        cacheEnabled = Hawk.get(HawkConfig.VIDEO_CACHE_ENABLE, DEFAULT_CACHE_ENABLED);
        tsSegmentEnabled = Hawk.get(HawkConfig.VIDEO_CACHE_TS_ENABLE, DEFAULT_TS_SEGMENT_ENABLED);
        silentMode = Hawk.get(HawkConfig.VIDEO_CACHE_SILENT_ENABLE, DEFAULT_SILENT_MODE);
        maxCacheSize = Hawk.get(HawkConfig.VIDEO_CACHE_MAX_SIZE, DEFAULT_MAX_CACHE_SIZE);
        
        // 初始化缓存目录
        initCacheDir();
        
        // 构建内存索引
        buildMemoryIndex();
        
        // 后台检查缓存大小和有效性
        scheduleBackgroundCacheCheck();
    }

    /**
     * 构建内存索引
     */
    private static void buildMemoryIndex() {
        if (memoryIndexInitialized) {
            return;
        }
        
        synchronized (memoryIndex) {
            if (memoryIndexInitialized) {
                return;
            }
            
            // 清空当前索引
            memoryIndex.clear();
            
            // 在后台线程构建索引
            THREAD_POOL.execute(() -> {
                try {
                    // 获取视频相关的缓存项
                    List<Cache> cacheBeans = AppDataManager.get().getCacheDao().getCacheByType(CacheManager.CACHE_TYPE_VIDEO);
                    
                    if (cacheBeans != null && !cacheBeans.isEmpty()) {
                        for (Cache bean : cacheBeans) {
                            // 创建索引条目
                            CacheIndexEntry entry = new CacheIndexEntry();
                            entry.url = bean.key;
                            entry.filePath = getCacheFilePath(bean.key);
                            entry.lastAccessTime = bean.lastUsedTime;
                            entry.createTime = bean.createTime;
                            entry.fileSize = bean.size;
                            entry.priority = bean.priority;
                            
                            // 将条目添加到内存索引
                            memoryIndex.put(bean.key, entry);
                        }
                        
                        LOG.i("视频缓存内存索引构建完成，共 " + memoryIndex.size() + " 条记录");
                    }
                } catch (Exception e) {
                    LOG.e("构建缓存索引出错: " + e.getMessage());
                } finally {
                    memoryIndexInitialized = true;
                }
                
                // 检查缓存大小
                checkCacheSize();
            });
        }
    }

    /**
     * 获取缓存文件路径
     */
    private static String getCacheFilePath(String key) {
        if (key == null) return null;
        return new File(getCacheDir(), MD5.encode(key)).getAbsolutePath();
    }

    /**
     * 定期后台检查缓存
     */
    private static void scheduleBackgroundCacheCheck() {
        // 只在应用启动时检查一次
        long now = System.currentTimeMillis();
        if (now - lastCacheCheckTime < CACHE_CHECK_INTERVAL) {
            return;
        }
        
        // 记录检查时间
        lastCacheCheckTime = now;
        
        // 在后台线程执行清理
        THREAD_POOL.execute(() -> {
            try {
                // 清理过期缓存
                cleanExpiredCache();
                
                // 检查并清理超出大小的缓存
                checkCacheSize();
            } catch (Exception e) {
                LOG.e("后台缓存检查出错: " + e.getMessage());
            }
        });
    }

    /**
     * 清理过期缓存
     */
    private static void cleanExpiredCache() {
        if (!isEnabled()) {
            return;
        }
        
        long now = System.currentTimeMillis();
        long validTime = now - CACHE_VALID_TIME;
        long maxValidTime = now - CACHE_MAX_VALID_TIME;
        
        try {
            LOG.i("开始清理过期缓存...");
            
            // 使用CacheDao清理缓存
            AppDataManager.get().getCacheDao().deleteExpiredCache(maxValidTime);
            
            // 如果缓存接近上限，进一步清理普通过期缓存
            long cacheSize = CacheManager.getTotalCacheSize();
            if (cacheSize > maxCacheSize * CACHE_CLEAN_THRESHOLD) {
                AppDataManager.get().getCacheDao().deleteExpiredCache(validTime);
            }
        } catch (Exception e) {
            LOG.e("清理过期缓存出错: " + e.getMessage());
        }
    }

    /**
     * 检查缓存大小并清理
     */
    private static void checkCacheSize() {
        if (!isEnabled()) {
            return;
        }
        
        try {
            long cacheSize = CacheManager.getTotalCacheSize();
            LOG.i("当前缓存大小: " + formatFileSize(cacheSize) + ", 最大允许: " + formatFileSize(maxCacheSize));
            
            // 如果超出最大缓存大小，开始清理
            if (cacheSize > maxCacheSize) {
                LOG.i("缓存超过上限，开始清理...");
                
                // 计算需要清理的大小
                long toClean = cacheSize - (long)(maxCacheSize * 0.75); // 清理至75%以下
                LOG.i("计划清理: " + formatFileSize(toClean));
                
                // 使用SmartCacheCleaner清理低优先级缓存
                long cleanedSize = CacheManager.SmartCacheCleaner.smartCleanCache(toClean);
                LOG.i("已清理缓存: " + formatFileSize(cleanedSize));
                
                // 更新内存索引
                if (cleanedSize > 0) {
                    refreshMemoryIndex();
                }
            }
        } catch (Exception e) {
            LOG.e("检查缓存大小出错: " + e.getMessage());
        }
    }

    /**
     * 刷新内存索引
     */
    private static void refreshMemoryIndex() {
        synchronized (memoryIndex) {
            // 重新构建索引
            memoryIndexInitialized = false;
            buildMemoryIndex();
        }
    }

    /**
     * 格式化文件大小为可读形式
     */
    private static String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024 * 1024));
        }
    }

    /**
     * 保存视频到缓存
     */
    public static void cacheVideo(String url, byte[] data) {
        if (!isEnabled() || url == null || data == null || data.length == 0) {
            return;
        }
        
        // 处理TS片段缓存
        if (url.contains(".ts") && !isTsSegmentEnabled()) {
            return;
        }
        
        // 在后台保存
        THREAD_POOL.execute(() -> {
            try {
                // 获取文件路径
                String filePath = getCacheFilePath(url);
                
                // 保存文件
                File cacheFile = new File(filePath);
                try (FileOutputStream fos = new FileOutputStream(cacheFile)) {
                    fos.write(data);
                    fos.flush();
                }
                
                // 更新数据库记录
                long now = System.currentTimeMillis();
                
                // 确定优先级
                int priority = 0;
                if (url.contains(".ts")) {
                    priority = 1; // TS片段优先级较低
                }
                
                // 使用兼容的方法保存缓存记录
                Cache cache = new Cache();
                cache.key = url;
                cache.size = data.length;
                cache.createTime = now;
                cache.lastUsedTime = now;
                cache.accessTime = now;
                cache.expireTime = now + CACHE_VALID_TIME;
                cache.type = CacheManager.CACHE_TYPE_VIDEO;
                cache.priority = priority;
                
                AppDataManager.get().getCacheDao().save(cache);
                
                // 更新内存索引
                CacheIndexEntry entry = new CacheIndexEntry();
                entry.url = url;
                entry.filePath = filePath;
                entry.lastAccessTime = now;
                entry.createTime = now;
                entry.fileSize = data.length;
                entry.priority = priority;
                
                memoryIndex.put(url, entry);
                
                if (!silentMode) {
                    LOG.i("缓存视频成功: " + url + ", 大小: " + formatFileSize(data.length));
                }
                
                // 检查缓存大小
                checkCacheSize();
            } catch (Exception e) {
                LOG.e("缓存视频失败: " + e.getMessage());
            }
        });
    }

    /**
     * 从缓存加载视频
     * 增强了缓存命中和错误处理
     */
    public static void loadFromCache(String url, GetCallback callback) {
        if (!isEnabled() || url == null || callback == null) {
            if (callback != null) {
                callback.onNotFound(url);
            }
            return;
        }
        
        // 处理TS片段缓存
        if (url.contains(".ts") && !isTsSegmentEnabled()) {
            callback.onNotFound(url);
            return;
        }
        
        // 检查内存索引
        if (memoryIndex.containsKey(url)) {
            CacheIndexEntry entry = memoryIndex.get(url);
            if (entry != null && !TextUtils.isEmpty(entry.filePath)) {
                try {
                    File file = new File(entry.filePath);
                    if (file.exists()) {
                        // 更新访问时间
                        long now = System.currentTimeMillis();
                        CacheManager.incrementAccessCount(url);
                        
                        // 更新内存索引
                        entry.lastAccessTime = now;
                        
                        // 读取文件
                        byte[] data = FileUtils.readFile(file);
                        if (data != null && data.length > 0) {
                            if (!silentMode) {
                                LOG.i("缓存命中(内存索引): " + url);
                            }
                            callback.onSuccess(url, data);
                            return;
                        }
                    }
                } catch (Exception e) {
                    LOG.e("从缓存读取出错(内存索引): " + e.getMessage());
                }
            }
        }
        
        // 内存索引未命中，查询数据库
        THREAD_POOL.execute(() -> {
            try {
                // 从数据库获取缓存
                Cache cacheBean = AppDataManager.get().getCacheDao().getCache(url);
                if (cacheBean == null) {
                    callback.onNotFound(url);
                    return;
                }
                
                // 获取文件路径
                String filePath = getCacheFilePath(url);
                if (TextUtils.isEmpty(filePath)) {
                    callback.onNotFound(url);
                    return;
                }
                
                File file = new File(filePath);
                if (!file.exists()) {
                    // 文件不存在，删除记录
                    Cache toDelete = new Cache();
                    toDelete.key = url;
                    AppDataManager.get().getCacheDao().delete(toDelete);
                    memoryIndex.remove(url);
                    callback.onNotFound(url);
                    return;
                }
                
                // 更新访问时间和计数
                CacheManager.incrementAccessCount(url);
                
                // 更新内存索引
                CacheIndexEntry entry = new CacheIndexEntry();
                entry.url = url;
                entry.filePath = filePath;
                entry.lastAccessTime = System.currentTimeMillis();
                entry.createTime = cacheBean.createTime;
                entry.fileSize = cacheBean.size;
                entry.priority = cacheBean.priority;
                
                memoryIndex.put(url, entry);
                
                // 读取文件
                byte[] data = FileUtils.readFile(file);
                if (data != null && data.length > 0) {
                    if (!silentMode) {
                        LOG.i("缓存命中(数据库): " + url);
                    }
                    callback.onSuccess(url, data);
                } else {
                    callback.onNotFound(url);
                }
            } catch (Exception e) {
                LOG.e("从缓存读取出错: " + e.getMessage());
                callback.onError(url, "读取缓存出错: " + e.getMessage());
            }
        });
    }

    /**
     * 内存索引条目
     */
    private static class CacheIndexEntry {
        String url;
        String filePath;
        long lastAccessTime;
        long createTime;
        long fileSize;
        int priority;
    }

    /**
     * 检查视频缓存功能是否启用
     */
    public static boolean isEnabled() {
        return Hawk.get(HawkConfig.VIDEO_CACHE_ENABLE, false);
    }
    
    /**
     * 检查视频分段缓存功能是否启用
     */
    public static boolean isSegmentEnabled() {
        return Hawk.get(HawkConfig.VIDEO_CACHE_SEGMENT_ENABLE, true);
    }
    
    /**
     * 检查TS分段缓存功能是否启用
     */
    public static boolean isTsSegmentEnabled() {
        return isEnabled() && Hawk.get(HawkConfig.VIDEO_CACHE_TS_ENABLE, true);
    }
    
    /**
     * 检查静默缓存功能是否启用
     */
    public static boolean isSilentCacheEnabled() {
        return isEnabled() && Hawk.get(HawkConfig.VIDEO_CACHE_SILENT_ENABLE, true);
    }
    
    /**
     * 将下载的HLS视频与缓存系统集成
     * @param url 视频URL
     * @param listener HLS加载监听器
     */
    public static void integrateWithHlsLoader(String url, AdaptiveVideoLoader.HlsLoadListener listener) {
        if (TextUtils.isEmpty(url) || listener == null || !isEnabled() || !isTsSegmentEnabled()) {
            return;
        }
        
        // 创建包装监听器，将加载结果同时写入缓存
        AdaptiveVideoLoader.HlsLoadListener wrapperListener = new AdaptiveVideoLoader.HlsLoadListener() {
            @Override
            public void onHlsSuccess(String videoUrl, AdaptiveVideoLoader.HlsPreloadResult result) {
                // 缓存m3u8内容
                if (result.m3u8Content != null) {
                    cacheVideo(videoUrl, result.m3u8Content.getBytes());
                }
                
                // 缓存预加载的TS分片
                if (result.tsUrls != null && result.preloadedSegments != null) {
                    for (int i = 0; i < result.preloadedSegments.size(); i++) {
                        String tsUrl = result.tsUrls.get(i);
                        byte[] segmentData = result.preloadedSegments.get(i);
                        
                        if (segmentData != null && segmentData.length > 0) {
                            cacheVideo(tsUrl, segmentData);
                        }
                    }
                }
                
                // 回调给原始监听器
                listener.onHlsSuccess(videoUrl, result);
            }
            
            @Override
            public void onProgress(String videoUrl, int progress) {
                listener.onProgress(videoUrl, progress);
            }
            
            @Override
            public void onSuccess(String videoUrl, byte[] data) {
                // 这个方法不应该被直接调用，但以防万一
                if (data != null && data.length > 0) {
                    cacheVideo(videoUrl, data);
                }
                listener.onSuccess(videoUrl, data);
            }
            
            @Override
            public void onError(String videoUrl, String error) {
                listener.onError(videoUrl, error);
            }
        };
        
        // 添加到加载器
        AdaptiveVideoLoader.getInstance().addListener(wrapperListener);
    }
    
    /**
     * 静默预缓存视频
     * 在不打扰用户的情况下预先缓存视频内容
     * @param url 视频URL
     */
    public static void silentCache(String url) {
        if (!isEnabled() || !isSilentCacheEnabled() || url == null || url.isEmpty()) {
            return;
        }
        
        // 检查是否是WiFi网络
        if (NetworkUtils.getInstance().getNetworkType() != NetworkUtils.NetworkType.WIFI) {
            return; // 只在WiFi下静默缓存
        }
        
        // 使用后台线程下载
        THREAD_POOL.execute(() -> {
            try {
                boolean isHls = url.toLowerCase().contains(".m3u8") || url.toLowerCase().contains("/hls/");
                if (isHls) {
                    // HLS格式使用自适应加载器
                    AdaptiveVideoLoader.getInstance().loadHlsVideo(url, new AdaptiveVideoLoader.HlsLoadListener() {
                        @Override
                        public void onHlsSuccess(String url, AdaptiveVideoLoader.HlsPreloadResult result) {
                            cacheVideo(url, result.m3u8Content.getBytes());
                            
                            // 只缓存前几个TS片段
                            if (isTsSegmentEnabled() && result.tsUrls != null && result.preloadedSegments != null) {
                                for (int i = 0; i < result.preloadedSegments.size(); i++) {
                                    try {
                                        String tsUrl = result.tsUrls.get(i);
                                        byte[] segmentData = result.preloadedSegments.get(i);
                                        
                                        if (segmentData != null && segmentData.length > 0) {
                                            cacheVideo(tsUrl, segmentData);
                                        }
                                    } catch (Exception e) {
                                        // 忽略错误
                                    }
                                }
                            }
                        }
                        
                        @Override
                        public void onProgress(String url, int progress) {
                            // 静默操作，忽略进度
                        }
                        
                        @Override
                        public void onSuccess(String url, byte[] data) {
                            cacheVideo(url, data);
                        }
                        
                        @Override
                        public void onError(String url, String error) {
                            Log.e(TAG, "静默缓存HLS内容失败: " + error);
                        }
                    });
                } else {
                    // 对于普通视频，仅下载首段
                    AdaptiveVideoLoader.getInstance().loadVideo(url, new AdaptiveVideoLoader.LoadListener() {
                        @Override
                        public void onProgress(String url, int progress) {
                            // 静默操作，忽略进度
                        }
                        
                        @Override
                        public void onSuccess(String url, byte[] data) {
                            cacheVideo(url, data);
                        }
                        
                        @Override
                        public void onError(String url, String error) {
                            Log.e(TAG, "静默缓存视频失败: " + error);
                        }
                    });
                }
            } catch (Exception e) {
                Log.e(TAG, "静默缓存过程出错: " + e.getMessage());
            }
        });
    }
    
    /**
     * 获取视频缓存大小
     * @return 已用缓存大小（字节）
     */
    public static long getCacheSize() {
        try {
            return CacheManager.getTotalCacheSize();
        } catch (Exception e) {
            return 0;
        }
    }
    
    /**
     * 获取最大缓存大小（MB）
     */
    public static int getMaxCacheSize() {
        return Hawk.get(HawkConfig.VIDEO_CACHE_MAX_SIZE, 500); // 默认500MB
    }
    
    /**
     * 设置缓存是否启用
     */
    public static void setEnabled(boolean enabled) {
        Hawk.put(HawkConfig.VIDEO_CACHE_ENABLE, enabled);
    }

    /**
     * 获取缓存回调接口
     */
    public interface GetCallback {
        /**
         * 成功获取完整文件
         * @param url 视频URL
         * @param data 文件数据
         */
        void onSuccess(String url, byte[] data);
        
        /**
         * 获取部分缓存数据
         * @param url 视频URL
         * @param filePath 缓存文件路径
         * @param segments 已缓存片段
         */
        void onPartialData(String url, String filePath, Map<Long, Integer> segments);
        
        /**
         * 成功获取指定范围
         * @param url 视频URL
         * @param data 范围数据
         * @param startPos 起始位置
         */
        void onRangeSuccess(String url, byte[] data, long startPos);
        
        /**
         * 指定范围不在缓存中
         * @param url 视频URL
         * @param startPos 起始位置
         * @param length 长度
         */
        void onRangeNotFound(String url, long startPos, int length);
        
        /**
         * 未找到缓存
         * @param url 视频URL
         */
        void onNotFound(String url);
        
        /**
         * 发生错误
         * @param url 视频URL
         * @param error 错误信息
         */
        void onError(String url, String error);
    }
} 