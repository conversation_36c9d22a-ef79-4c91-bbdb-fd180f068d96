package com.github.tvbox.osc.util;

import android.util.Base64;

/**
 * <AUTHOR>
 * @date :2020/12/23
 * @description:
 */
public class HawkConfig {

    public static boolean FORCE_pause = false;
    public static String APP_ID = "10000"; //应用ID2

   // public static String SEN_SUS = "57287e2d24"; //百度统计
   // public static String APP_Channel = "yanshi_huikan"; //渠道随意
    public static final String CONFIG_URL = "https://api.jrong2021.com/ysxstv.properties";//这里改成你自己的

    public static String Your_app_id = ""; //TalkingData统计id  AndroidManifest.xml里面的也需要改
    public static String Your_channel_id = ""; //渠道随意
    public static String zb_vpn = "1"; //是否开启抓包 0开启 1关闭
    public static final String BASE_URL_ENC = "";//域名无用
    public static String MMM_MMM = new String(Base64.decode(HawkConfig.BASE_URL_ENC.getBytes(), Base64.DEFAULT));
    public static String API_KEY = "4f49e337c49f446a4bde53282deb9da9"; //如意后台 接口密钥
    public static final String SOURCES_FOR_SEARCH = "checked_sources_for_search";//搜索
    public static final String DOH_URL = "doh_url";
    public static final String API_URL = "api_url";
    public static final String API_URL2 = "api_url2";
    public static final String SHOW_PREVIEW = "show_preview";//视频小窗
    public static final String HOME_API = "home_api";
    public static final String JSON_URL = "json_url";
    public static final String JSON_URL2 = "json_url2";
    public static final String IJK_CODEC = "ijk_codec";
    public static final String HOME_SHOW_SOURCE = "show_source";
    public static final String PLAY_TYPE = "play_type"; //0 系统 1 ijk 2 exo 10 MXPlayer
    public static final String DEBUG_OPEN = "debug_open";
    public static final String API_HISTORY = "api_history";
    public static final String EPG_URL = "epg_url";
    public static final String LIVE_URL = "live_url";
    public static final String LIVE_HISTORY = "live_history";
    public static final String DEFAULT_PARSE = "parse_default";
    public static final String PIC_IN_PIC = "pic_in_pic";
    public static final String PARSE_WEBVIEW = "parse_webview"; // true 系统 false xwalk
    public static final String PLAY_SCALE = "play_scale"; //0 texture 2
    public static final String PLAY_RENDER = "play_render"; //0 texture 2
    public static final String PLAY_TIME_STEP = "play_time_step"; //0 texture 2
    public static final String HOME_REC = "home_rec"; // 0 豆瓣热播 1 数据源推荐 2 历史
    public static final String SEARCH_VIEW = "search_view"; // 0 缩略图  1列表
    public static final String LIVE_CHANNEL = "last_live_channel_name";
    public static final String LIVE_CHANNEL_REVERSE = "live_channel_reverse";
    public static final String LIVE_CROSS_GROUP = "live_cross_group";
    public static final String LIVE_CONNECT_TIMEOUT = "live_connect_timeout";
    public static final String LIVE_SHOW_NET_SPEED = "live_show_net_speed";
    public static final String LIVE_SHOW_TIME = "live_show_time";
    public static boolean hotVodDelete;
    public static final String SUBTITLE_TEXT_SIZE = "subtitle_text_size";
    public static final String SUBTITLE_TIME_DELAY = "subtitle_time_delay";
    public static final String THEME_SELECT = "theme_select";//主题
    public static final String REMOTE_TVBOX = "remote_tvbox_host";
    public static final String IJK_CACHE_PLAY = "ijk_cache_play";
    
    // 自动更新相关配置
    public static final String LAST_UPDATE_CHECK = "last_update_check";
    public static final String AUTO_UPDATE_ENABLED = "auto_update_enabled";
    public static final String UPDATE_INTERVAL = "update_check_interval";

    // 缓存配置常量
    public static final String CACHE_MAX_SIZE = "cache_max_size"; // 缓存最大大小，单位MB
    public static final String CACHE_VIDEO_AUTO_CLEAN = "cache_video_auto_clean"; // 是否自动清理视频缓存
    public static final String CACHE_AUTO_CLEAN_DAYS = "cache_auto_clean_days"; // 自动清理多少天前的缓存
    public static final String CACHE_PRIORITY_ENABLE = "cache_priority_enable"; // 是否启用缓存优先级
    public static final String CACHE_VIDEO_PRELOAD_ENABLE = "cache_video_preload_enable"; // 是否启用视频预加载
    public static final String CACHE_VIDEO_CONTENT_PRELOAD_ENABLE = "cache_video_content_preload_enable"; // 是否启用视频内容预缓冲
    public static final String CACHE_PLAY_URL_ENABLE = "cache_play_url_enable"; // 是否启用播放URL缓存
    
    // 用户体验相关配置
    public static final String SHOW_PRELOAD_PROGRESS = "show_preload_progress"; // 是否显示预加载进度
    public static final String SHOW_NETWORK_STATUS = "show_network_status"; // 是否显示网络状态
    public static final String SHOW_LOADING_ANIMATION = "show_loading_animation"; // 是否显示加载动画
    public static final String ENHANCED_ERROR_INFO = "enhanced_error_info"; // 是否显示增强的错误信息
    
    // 自动更新检测
    public static final String FAST_SEARCH_MODE = "fast_search_mode";
    public static final String HOME_NUM = "home_num"; // 0 默认 1 历史 2 收藏 3 推荐
    public static final String HOME_API_URLs = "home_api_urls";
    public static final String SETTING_HOME = "setting_home";
    public static final String PLAYER_SEEK_STEP = "player_seek_step";

    // 视频缓存大小设置
    public static final String PLAY_CACHE = "play_cache";

    // 视频缓存相关配置
    public static final String VIDEO_CACHE_ENABLE = "video_cache_enable"; // 是否启用视频缓存
    public static final String VIDEO_CACHE_TS_ENABLE = "video_cache_ts_enable"; // 是否启用TS分片缓存
    public static final String VIDEO_CACHE_SILENT_ENABLE = "video_cache_silent_enable"; // 是否启用静默缓存
    public static final String VIDEO_CACHE_MAX_SIZE = "video_cache_max_size"; // 最大缓存大小(MB)
    public static final String VIDEO_FILE_CACHE_MAX_SIZE = "video_file_cache_max_size"; // 单个视频缓存最大大小
    public static final String VIDEO_CACHE_SEGMENT_ENABLE = "video_cache_segment_enable"; // 是否开启分段缓存
    public static final String VIDEO_CACHE_TS_SEGMENT_ENABLE = "video_cache_ts_segment_enable"; // 是否对TS文件进行分段缓存
}