package com.github.tvbox.osc.util;

import android.text.TextUtils;
import android.util.Log;

import com.github.tvbox.osc.bean.VodInfo;
import com.github.tvbox.osc.cache.CacheManager;

import java.util.ArrayList;
import java.util.List;

/**
 * 历史记录帮助类
 * 提供历史记录的保存、读取和清理功能
 */
public class HistoryHelper {
    private static final String TAG = "HistoryHelper";
    
    // 历史记录缓存前缀
    private static final String HISTORY_CACHE_PREFIX = "history_";
    
    /**
     * 保存观看历史
     * 
     * @param sourceKey 视频源
     * @param vodInfo 视频信息
     */
    public static void saveHistory(String sourceKey, VodInfo vodInfo) {
        if (vodInfo == null || TextUtils.isEmpty(sourceKey)) {
            return;
        }
        
        try {
            // 生成缓存键
            String cacheKey = HISTORY_CACHE_PREFIX + sourceKey + "_" + vodInfo.id;
            
            // 使用CacheManager保存，过期时间较长
            CacheManager.save(cacheKey, vodInfo, 30 * 24 * 60 * 60 * 1000L, CacheManager.CACHE_TYPE_VIDEO);
            Log.d(TAG, "保存历史记录: " + vodInfo.name);
        } catch (Exception e) {
            Log.e(TAG, "保存历史记录失败", e);
        }
    }
    
    /**
     * 获取观看历史
     * 
     * @param sourceKey 视频源
     * @param vodId 视频ID
     * @return 视频信息
     */
    public static VodInfo getHistory(String sourceKey, String vodId) {
        if (TextUtils.isEmpty(sourceKey) || TextUtils.isEmpty(vodId)) {
            return null;
        }
        
        try {
            String cacheKey = HISTORY_CACHE_PREFIX + sourceKey + "_" + vodId;
            Object obj = CacheManager.getCache(cacheKey);
            if (obj instanceof VodInfo) {
                return (VodInfo) obj;
            }
        } catch (Exception e) {
            Log.e(TAG, "获取历史记录失败", e);
        }
        
        return null;
    }
    
    /**
     * 清理所有历史记录
     */
    public static void clearAllHistory() {
        try {
            CacheManager.clearCacheByKeyPrefix(HISTORY_CACHE_PREFIX);
            Log.d(TAG, "清理所有历史记录");
        } catch (Exception e) {
            Log.e(TAG, "清理历史记录失败", e);
        }
    }
} 