package com.github.tvbox.osc.util;

import android.util.Log;

/**
 * 日志工具类
 * 提供简单的Log封装，方便统一管理和控制
 */
public class LogUtils {
    // 是否启用日志
    private static boolean isLogEnabled = true;
    
    /**
     * 输出调试日志
     * @param tag 日志标签
     * @param message 日志内容
     */
    public static void d(String tag, String message) {
        if (isLogEnabled) {
            Log.d(tag, message);
        }
    }
    
    /**
     * 输出错误日志
     * @param tag 日志标签
     * @param message 日志内容
     */
    public static void e(String tag, String message) {
        if (isLogEnabled) {
            Log.e(tag, message);
        }
    }
    
    /**
     * 输出错误日志，包含异常信息
     * @param tag 日志标签
     * @param message 日志内容
     * @param throwable 异常
     */
    public static void e(String tag, String message, Throwable throwable) {
        if (isLogEnabled) {
            Log.e(tag, message, throwable);
        }
    }
    
    /**
     * 设置是否启用日志
     * @param enabled 是否启用
     */
    public static void setLogEnabled(boolean enabled) {
        isLogEnabled = enabled;
    }
} 