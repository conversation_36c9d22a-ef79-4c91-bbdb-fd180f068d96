package com.github.tvbox.osc.ui.activity;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.Transition;
import com.github.tvbox.osc.R;
import com.github.tvbox.osc.base.BaseActivity;
import com.github.tvbox.osc.beanry.ReLevelBean;
import com.github.tvbox.osc.util.MMkvUtils;
import com.github.tvbox.osc.util.ToolUtils;
import com.youth.banner.Banner;
import com.youth.banner.BannerConfig;
import com.youth.banner.Transformer;
import com.youth.banner.listener.OnBannerListener;
import com.youth.banner.loader.ImageLoader;

import java.util.ArrayList;

/**
 * 优化后的轮播广告页面
 * 添加了动态背景、过渡动画和自定义指示器
 **/
public class MyBanner extends BaseActivity implements OnBannerListener {

    private static final String TAG = "MyBanner";
    private Banner banner;
    private TextView BannerName;
    private ReLevelBean LevelBean;
    private LinearLayout ok_go_ss;
    private LinearLayout indicatorContainer;
    private ImageView blurBackground;
    private ArrayList<String> list_path;
    private ArrayList<String> list_title;
    private ArrayList<View> indicatorDots;
    private ProgressBar progressBar;
    private String vodTitle;
    private Integer searchable;
    private int currentItem = 0;
    private final int delayTime = 8000;
    private final Handler mHandler = new Handler();
    private final Runnable mRunnable = new Runnable() {
        @SuppressLint({"DefaultLocale", "SetTextI18n"})
        @Override
        public void run() {
            if (LevelBean.msg.size() > 0) {
                bb = false;
                startProgressBar(100);
                updateIndicators(currentItem);
                
                vodTitle = LevelBean.msg.get(currentItem).name;
                searchable = LevelBean.msg.get(currentItem).searchable;
                if (searchable == 1) {
                    BannerName.setText(vodTitle);
                    ok_go_ss.setVisibility(View.VISIBLE);
                } else {
                    BannerName.setText(vodTitle);
                    ok_go_ss.setVisibility(View.GONE);
                }
                
                // 加载并更新模糊背景
                if (currentItem < list_path.size()) {
                    updateBlurBackground(list_path.get(currentItem));
                }
                
                if (currentItem < LevelBean.msg.size() - 1) {
                    currentItem++;
                } else {
                    currentItem = 0;
                }
                mHandler.postDelayed(this, delayTime);
            }
        }
    };

    private boolean bb = false;

    /**
     * 更新模糊背景图
     */
    private void updateBlurBackground(String imageUrl) {
        Glide.with(this)
            .asBitmap()
            .load(imageUrl)
            .into(new CustomTarget<Bitmap>() {
                @Override
                public void onResourceReady(@NonNull Bitmap resource, @Nullable Transition<? super Bitmap> transition) {
                    try {
                        // 创建一个新的ImageView用于动画切换
                        final ImageView newBgImage = new ImageView(MyBanner.this);
                        newBgImage.setLayoutParams(new ViewGroup.LayoutParams(
                                ViewGroup.LayoutParams.MATCH_PARENT,
                                ViewGroup.LayoutParams.MATCH_PARENT));
                        newBgImage.setScaleType(ImageView.ScaleType.CENTER_CROP);
                        newBgImage.setAlpha(0f);
                        
                        // 应用模糊效果
                        Bitmap blurredBitmap = fastBlur(resource, 10);
                        newBgImage.setImageBitmap(blurredBitmap);
                        
                        // 将新ImageView添加到布局
                        ViewGroup parent = (ViewGroup) blurBackground.getParent();
                        parent.addView(newBgImage, parent.indexOfChild(blurBackground));
                        
                        // 创建淡入淡出动画
                        ObjectAnimator fadeOut = ObjectAnimator.ofFloat(blurBackground, "alpha", 0.3f, 0f);
                        ObjectAnimator fadeIn = ObjectAnimator.ofFloat(newBgImage, "alpha", 0f, 0.3f);
                        
                        AnimatorSet animatorSet = new AnimatorSet();
                        animatorSet.playTogether(fadeOut, fadeIn);
                        animatorSet.setDuration(1000);
                        animatorSet.setInterpolator(new AccelerateDecelerateInterpolator());
                        
                        animatorSet.addListener(new AnimatorListenerAdapter() {
                            @Override
                            public void onAnimationEnd(Animator animation) {
                                // 动画结束后，移除旧的ImageView并更新引用
                                parent.removeView(blurBackground);
                                blurBackground = newBgImage;
                            }
                        });
                        
                        animatorSet.start();
                    } catch (Exception e) {
                        // 发生错误时直接设置图片，不使用动画
                        Log.e(TAG, "背景动画错误: " + e.getMessage());
                        blurBackground.setImageBitmap(fastBlur(resource, 10));
                    }
                }

                @Override
                public void onLoadCleared(@Nullable Drawable placeholder) {
                    // 不处理
                }
            });
    }
    
    /**
     * 应用简单模糊效果到位图 - 替代RenderScript
     */
    private Bitmap fastBlur(Bitmap bitmap, int radius) {
        // 缩小图片以提高性能
        float ratio = 0.3f;
        int width = Math.round(bitmap.getWidth() * ratio);
        int height = Math.round(bitmap.getHeight() * ratio);
        
        Bitmap inputBitmap = Bitmap.createScaledBitmap(bitmap, width, height, false);
        Bitmap outputBitmap = Bitmap.createBitmap(inputBitmap);
        
        // 创建画布和暗色覆盖层
        Canvas canvas = new Canvas(outputBitmap);
        canvas.drawColor(Color.argb(100, 0, 0, 0)); // 半透明黑色叠加
        
        // 添加一点模糊效果 (通过重叠几个半透明的图层)
        Paint paint = new Paint();
        paint.setAlpha(150); // 设置半透明
        
        for(int i = 0; i < 3; i++) {
            canvas.drawBitmap(inputBitmap, 0, 0, paint);
        }
        
        // 降低亮度
        Paint darkPaint = new Paint();
        darkPaint.setColorFilter(null);
        darkPaint.setAlpha(180); // 降低亮度
        canvas.drawRect(0, 0, width, height, darkPaint);
        
        return outputBitmap;
    }
    
    /**
     * 更新指示器状态
     */
    private void updateIndicators(int currentPosition) {
        if (indicatorDots != null && indicatorDots.size() > 0) {
            for (int i = 0; i < indicatorDots.size(); i++) {
                View dot = indicatorDots.get(i);
                if (i == currentPosition) {
                    // 当前位置的指示器变大并高亮
                    ObjectAnimator scaleX = ObjectAnimator.ofFloat(dot, "scaleX", 1.0f, 1.5f);
                    ObjectAnimator scaleY = ObjectAnimator.ofFloat(dot, "scaleY", 1.0f, 1.5f);
                    dot.setBackgroundResource(R.drawable.indicator_selected);
                    
                    AnimatorSet animatorSet = new AnimatorSet();
                    animatorSet.playTogether(scaleX, scaleY);
                    animatorSet.setDuration(300);
                    animatorSet.setInterpolator(new DecelerateInterpolator());
                    animatorSet.start();
                } else {
                    // 其他位置的指示器恢复正常
                    ObjectAnimator scaleX = ObjectAnimator.ofFloat(dot, "scaleX", 1.5f, 1.0f);
                    ObjectAnimator scaleY = ObjectAnimator.ofFloat(dot, "scaleY", 1.5f, 1.0f);
                    dot.setBackgroundResource(R.drawable.indicator_normal);
                    
                    AnimatorSet animatorSet = new AnimatorSet();
                    animatorSet.playTogether(scaleX, scaleY);
                    animatorSet.setDuration(300);
                    animatorSet.setInterpolator(new AccelerateInterpolator());
                    animatorSet.start();
                }
            }
        }
    }

    public void startProgressBar(int b) {
        Runnable mRunnable = new Runnable() {
            @Override
            public void run() {
                for (int i = 100; i >= 0; i--) {
                    if (bb) break;
                    setProgressBar(i);
                    try {
                        Thread.sleep(75);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        };
        Thread thread = new Thread(mRunnable);
        thread.start();
    }

    public void setProgressBar(int progress) {
        progressBar.setProgress(progress);
    }

    @Override
    protected int getLayoutResID() {
        return R.layout.activity_banner;
    }

    @Override
    protected void init() {
        initView();
    }

    @Override
    protected void onResume() { //恢复
        super.onResume();
        initView();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        bb = true;
        currentItem = 0;
        setProgressBar(100);
        banner.stopAutoPlay();
        mHandler.removeCallbacksAndMessages(null);
    }

    @Override
    protected void onPause() {
        super.onPause();
        bb = true;
        currentItem = 0;
        setProgressBar(100);
        banner.stopAutoPlay();
        mHandler.removeCallbacksAndMessages(null);
    }

    @Override
    protected void onStop() {
        super.onStop();
        //结束轮播
        bb = true;
        currentItem = 0;
        setProgressBar(100);
        banner.stopAutoPlay();
        mHandler.removeCallbacksAndMessages(null);
    }

    private void initView() {
        banner = findViewById(R.id.banner);
        ok_go_ss = findViewById(R.id.ok_go_ss);
        BannerName = findViewById(R.id.ad_name);
        progressBar = findViewById(R.id.progressBar);
        blurBackground = findViewById(R.id.blurBackground);
        indicatorContainer = findViewById(R.id.indicatorContainer);
        
        list_path = new ArrayList<>();
        list_title = new ArrayList<>();
        indicatorDots = new ArrayList<>();
        
        LevelBean = MMkvUtils.loadReLevelBean("");
        if (LevelBean != null && LevelBean.msg.size() > 0) {
            for (int i = 0; i < LevelBean.msg.size(); i++) {
                if (ToolUtils.getIsEmpty(LevelBean.msg.get(i).extend)) {
                    list_path.add(LevelBean.msg.get(i).extend);
                    Log.d(TAG, "initView: " + LevelBean.msg.get(i).extend);
                    list_title.add(LevelBean.msg.get(i).name);
                    
                    // 为每个图片创建一个指示器点
                    createIndicatorDot(i);
                }
            }
        }

        //设置内置样式
        banner.setBannerStyle(BannerConfig.NOT_INDICATOR)
                //设置图片加载器
                .setImageLoader(new MyLoader())
                //设置轮播的动画效果
                .setBannerAnimation(Transformer.Default)
                //设置轮播图的标题集合
                .setBannerTitles(list_title)
                //设置图片网址或地址的集合
                .setImages(list_path)
                //设置轮播间隔时间
                .setDelayTime(delayTime)
                //设置是否为自动轮播
                .isAutoPlay(true)
                //设置指示器的位置
                .setIndicatorGravity(BannerConfig.CENTER)
                //轮播图的监听
                .setOnBannerListener(MyBanner.this)
                //必须最后调用的方法，启动轮播图
                .start();
        
        // 加载第一张图片作为背景
        if (list_path.size() > 0) {
            updateBlurBackground(list_path.get(0));
        }
        
        mHandler.postDelayed(mRunnable, 1);
    }
    
    /**
     * 创建指示器圆点
     */
    private void createIndicatorDot(int position) {
        View dot = new View(this);
        int dotSize = (int) getResources().getDimension(R.dimen.vs_10);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(dotSize, dotSize);
        params.setMargins(10, 0, 10, 0);
        dot.setLayoutParams(params);
        
        // 设置初始状态
        if (position == 0) {
            dot.setBackgroundResource(R.drawable.indicator_selected);
            dot.setScaleX(1.5f);
            dot.setScaleY(1.5f);
        } else {
            dot.setBackgroundResource(R.drawable.indicator_normal);
        }
        
        indicatorContainer.addView(dot);
        indicatorDots.add(dot);
    }

    //按下OK键时获取当前幻灯片位置
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_DPAD_CENTER) {
            goSearch();
        }
        return super.onKeyDown(keyCode, event);
    }

    private void goSearch() {
        if (searchable != 1) {
            finish();
        } else {
            //Intent newIntent = new Intent(mContext, SearchActivity.class);
            //聚合搜索不知道哪里
            Intent newIntent = new Intent(mContext, FastSearchActivity.class);
            newIntent.putExtra("title", vodTitle);
            newIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(newIntent);
        }
    }

    @Override
    public void OnBannerClick(int position) {
        goSearch();
        Log.d(TAG, "OnBannerClick: " + position);
    }

    //自定义的图片加载器
    private static class MyLoader extends ImageLoader {
        @Override
        public void displayImage(Context context, Object path, ImageView imageView) {
            Log.d(TAG, "displayImage: " + path);
            Glide.with(context)
                .load((String) path)
                .into(imageView);
        }
    }
}