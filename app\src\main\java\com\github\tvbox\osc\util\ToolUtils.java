package com.github.tvbox.osc.util;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.core.content.FileProvider;

import com.github.tvbox.osc.R;
import com.github.tvbox.osc.beanry.NoticeBean;
import com.github.tvbox.osc.beanry.ReLevelBean;
import com.github.tvbox.osc.util.DebugHelper;
import com.github.tvbox.osc.view.HomeDialog;
import com.github.tvbox.osc.view.LiveLoadingDialog;
import com.google.gson.Gson;
import com.lzy.okgo.OkGo;
import com.lzy.okgo.callback.AbsCallback;
import com.lzy.okgo.model.Response;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

import cz.msebera.android.httpclient.HttpResponse;
import cz.msebera.android.httpclient.client.methods.HttpGet;
import cz.msebera.android.httpclient.impl.client.DefaultHttpClient;

public class ToolUtils {
    private static final String TAG = "ToolUtils";
    /**
     * @brief 对话框。
     */
    private static LiveLoadingDialog Loadingdialog = null;
//零熙唯一QQ：1007713299//零熙唯一QQ：1007713299//零熙唯一QQ：1007713299
    /**
     * @brief 显示函数。
     * <AUTHOR>
     * @param[in] context 上下文。
     * @param[in] title   标题文字。
     * @param[in] message 内容文字。
     * @note 显示加载对话框处理。
     */
    public static void loadingShow_tv(Context context, int message) {
        if (Loadingdialog != null && Loadingdialog.isShowing()) {
            Loadingdialog.dismiss();
        }
        //使用以下方式显示对话框，按返回键可关闭对话框
        Loadingdialog = new LiveLoadingDialog(context);
        Loadingdialog.setLoadingMsg(message);
        Loadingdialog.setCanceledOnTouchOutside(false);
        Loadingdialog.show();
    }

    /**
     * @brief 关闭函数。
     * <AUTHOR>
     * @note 关闭加载对话框处理。
     */
    public static void loadingClose_Tv() {
        if (Loadingdialog != null) {
            Loadingdialog.cancel();
            Loadingdialog = null;
        } else {
            Log.w(TAG, "close(): mDialog is not showing");
        }

        Log.d(TAG, "close() end");
    }

    /**
     * 获取当前应用包名
     *
     * @return
     */
    public static String getCurrentPackageName(Context context) {
        PackageManager pm = context.getPackageManager();
        try {
            PackageInfo info = pm.getPackageInfo(context.getPackageName(), 0);
            return info.packageName;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 下载apk文件进行安装
     *
     * @param context
     * @param mHandler 更新显示进度的handler
     * @param url
     * <AUTHOR>
     */
    public static void startDownloadApk(final Context context, final String url, final Handler mHandler) {
        Log.d(TAG, "startDownloadApk: "+url);
        showToast(context, "正在后台下载，完成后提示安装...", R.drawable.toast_smile);
        new Thread(new Runnable() {
            @Override
            public void run() {
                String sdPath = Environment.getExternalStorageDirectory() + "/";
                String mSavePath = sdPath + "loudTV";
                File dir = new File(mSavePath);
                Looper.prepare();
                String apkName = url.substring(url.lastIndexOf("/") + 1);
                Log.d("zhouchuan", "文件名称" + apkName);
                if (!dir.exists()) {
                    dir.mkdirs();
                }
                FileOutputStream fos = null;
                try {
                    HttpGet hGet = null;
                    Log.d(TAG, "run111: "+url);
                    if (url.contains(" ")){
                        hGet = new HttpGet(url.replaceAll(" ", "%20"));//替换掉空格字符串，不然下载不成功
                    }else{
                        hGet = new HttpGet(url);
                    }
                    HttpResponse hResponse = new DefaultHttpClient().execute(hGet);
                    if (hResponse.getStatusLine().getStatusCode() == 200) {
                        InputStream is = hResponse.getEntity().getContent();
                        float downsize = 0;
                        if (mHandler != null) {
                            //获取下载的文件大小
                            float size = hResponse.getEntity().getContentLength();
                            mHandler.obtainMessage(1001, size).sendToTarget();//发消息给handler处理更新信息
                        }
                        fos = context.openFileOutput(apkName, Context.MODE_PRIVATE);
                        byte[] buffer = new byte[8192];
                        int count = 0;
                        while ((count = is.read(buffer)) != -1) {
                            if (mHandler != null) {
                                downsize += count;
                                mHandler.obtainMessage(1002, downsize).sendToTarget();//发消息给handler处理更新信息
                            }
                            fos.write(buffer, 0, count);
                        }
                        fos.close();
                        is.close();
                        
                        // 发送下载完成的消息，显示通知
                        if (mHandler != null) {
                            mHandler.obtainMessage(1003).sendToTarget();
                        }
                        
                        // 下载完成后安装APK
                        installApk(context, "/data/data/" + getCurrentPackageName(context) + "/files/" + apkName);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    // 发送下载失败的消息
                    if (mHandler != null) {
                        mHandler.obtainMessage(1004).sendToTarget();
                    }
                }
                Looper.loop();
            }
        }).start();
    }

    /**
     * 判断apk文件是否可以安装
     *
     * @param context
     * @param filePath
     * @return
     */
    public static boolean getUninatllApkInfo(Context context, String filePath) {
        boolean result = false;
        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo info = pm.getPackageArchiveInfo(filePath, PackageManager.GET_ACTIVITIES);
            if (info != null) {
                result = true;
            }
        } catch (Exception e) {
            result = false;
            Log.e("zhouchuan", "*****  解析未安装的 apk 出现异常 *****" + e.getMessage(), e);
        }
        return result;
    }

    /**
     * 安装apk文件
     *
     * @param fileName
     * <AUTHOR>
     */
    public static void installApk(Context context, String fileName) {
        if (getUninatllApkInfo(context, fileName)) {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            File apkFile = new File(fileName);
            try {
                String[] args2 = {"chmod", "604", apkFile.getPath()};
                Runtime.getRuntime().exec(args2);
            } catch (IOException e) {
                e.printStackTrace();
            }
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                Uri uri = FileProvider.getUriForFile(context, context.getPackageName() + ".fileprovider", apkFile);
                context.grantUriPermission(getCurrentPackageName(context), uri, Intent.FLAG_GRANT_READ_URI_PERMISSION);
                intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                intent.setDataAndType(uri, "application/vnd.android.package-archive");
                context.startActivity(intent);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    boolean hasInstallPermission = context.getPackageManager().canRequestPackageInstalls();
                    if (!hasInstallPermission) {
                        Intent intent1 = new Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES);
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        context.startActivity(intent1);
                        return;
                    }
                }
            } else {
                Uri uri = Uri.fromFile(apkFile);
                intent.addFlags(Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION);
                intent.setDataAndType(uri, "application/vnd.android.package-archive");
                try {
                    String[] args2 = {"chmod", "604", apkFile.getPath()};
                    Runtime.getRuntime().exec(args2);
                } catch (IOException e) {
                    e.printStackTrace();
                }
                context.startActivity(intent);
            }
        } else {
            Toast.makeText(context, "文件还没下载完成，请耐心等待。", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 获取当前应用版本号
     *
     * @return
     */
    @SuppressWarnings("unused")
    public static String getVersion(Context context) {
        PackageManager pm = context.getPackageManager();
        try {
            PackageInfo info = pm.getPackageInfo(context.getPackageName(), 0);
            return info.versionName;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 自定义Toast，无堆栈跟踪版本
     *
     * @param context
     * @param text
     * @param image
     */
    @SuppressLint("InflateParams")
    public static void showToast(Context context, String text, int image) {
        // Check if the text is null or empty
        if (text == null || text.trim().isEmpty()) {
            // 如果消息为空则直接返回
            return;
        }
        
        try {
            // 使用DebugHelper.showToast避免堆栈跟踪
            DebugHelper.showToast(context, text);
            
            // 记录图标，以后可能需要支持不同类型的提示
            Log.d(TAG, "显示带图标Toast: " + text + ", 图标: " + image);
        } catch (Exception e) {
            // 记录异常但不崩溃
            Log.e(TAG, "显示Toast出错: " + e.getMessage());
            
            // 如果DebugHelper失败，使用原始方法作为备选方案
            try {
                View view = LayoutInflater.from(context).inflate(R.layout.tv_toast, null);
                TextView tv_toast = view.findViewById(R.id.tv_smtv_toast);
                ImageView iv_toast = view.findViewById(R.id.iv_smtv_toast);
                tv_toast.setText(text);
                iv_toast.setBackgroundResource(image);
                Toast toast = new Toast(context);
                toast.setView(view);
                toast.setDuration(Toast.LENGTH_SHORT);
                toast.show();
            } catch (Exception e2) {
                // 记录异常但不崩溃
                Log.e(TAG, "备选Toast也显示出错: " + e2.getMessage());
            }
        }
    }

    public static boolean getIsEmpty(String text) {
        return text != null && !text.equals("") && text.length() >= 1; //true
    }

    public static String getAndroidId(Context context) {
        return Settings.System.getString(context.getContentResolver(), Settings.System.ANDROID_ID);
    }

    /*
     * 将时间转换为时间戳
     */
    @SuppressLint("SimpleDateFormat")
    public static int dateToStamp(String s) {
        int res = 0;
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = simpleDateFormat.parse(s);
            assert date != null;
            long ts = date.getTime() / 1000;
            res = Integer.parseInt(String.valueOf(ts));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return res;
    }

    /*
     * 将时间戳转换为时间 by茶茶qq205888578
     * 增强版本支持更多会员类型
     */
    public static String stampToDate(int s) {
        String res;
        @SuppressLint("SimpleDateFormat") SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long lt = (long) s * 1000;

        // 特殊会员类型识别
        if (lt == 999999999000L) {
            return "永久会员";
        }

        if (lt == 888888888000L) {
            return "免费会员";
        }
        
        if (lt == 777777777000L) {
            return "VIP尊享会员";
        }
        
        if (lt == 666666666000L) {
            return "超级会员";
        }
        
        // 时间戳大于当前时间，返回时间格式，否则返回未开通会员
        if (lt > System.currentTimeMillis()) {
            Date date = new Date(lt);
            res = simpleDateFormat.format(date);
            return res;
        }

        return "未开通会员";
    }

    /**
     * 处理加密
     **/
    public static String setSign(String data) {
        String signData;
        if (data.equals("null")) {
            signData = "t=" + System.currentTimeMillis() / 1000 + "&" + HawkConfig.API_KEY;
        } else {
            signData = data + "&t=" + System.currentTimeMillis() / 1000 + "&" + HawkConfig.API_KEY;
        }
        return MD5.encode(signData);
    }

    /**
     * 处理请求接口
     **/
    public static String setApi(String act) {
        String apiUrl = HawkConfig.MMM_MMM;
        // 确保URL包含协议前缀
        if (apiUrl == null || apiUrl.isEmpty()) {
            Log.e(TAG, "API基础URL为空，请检查配置");
            return ""; // 返回空字符串，调用处需要检查
        }
        
        // 确保URL包含协议前缀
        if (!apiUrl.startsWith("http://") && !apiUrl.startsWith("https://")) {
            apiUrl = "http://" + apiUrl; // 默认添加http://前缀
        }
        
        // 确保URL末尾没有多余的斜杠
        if (apiUrl.endsWith("/")) {
            apiUrl = apiUrl.substring(0, apiUrl.length() - 1);
        }
        
        String fullUrl = apiUrl + "/api.php?act=" + act + "&app=" + HawkConfig.APP_ID;
        Log.d(TAG, "生成API请求URL: " + fullUrl);
        return fullUrl;
    }

    /**
     * 初始化数据
     **/
    public static boolean iniData(Response<String> response, Context context) {
        try {
            if (response == null || response.body() == null) {
                return false;
            }
            
            String decryptedResponse = BaseR.decry_R(response.body());
            if (decryptedResponse == null || decryptedResponse.isEmpty()) {
                return false;
            }
            
            JSONObject jo = new JSONObject(decryptedResponse);
            Log.d(TAG, "请求结果：" + context + "====code: " + jo.getInt("code") + "===data:" + response.body());
            
            if (jo.getInt("code") == 200) {
                return true;
            } else {
                // Make sure we have a non-empty message to show
                String message = "操作失败";
                if (jo.has("msg")) {
                    String msgKey = "msg";
                    // The original code had a context variable concatenated after "msg"
                    // which seems like a mistake, so we'll check for both formats
                    try {
                        message = jo.getString(msgKey);
                    } catch (JSONException e) {
                        try {
                            msgKey = "msg" + context;
                            message = jo.getString(msgKey);
                        } catch (JSONException e2) {
                            // Keep the default message
                        }
                    }
                    
                    // If we got an empty message, use default
                    if (message == null || message.trim().isEmpty()) {
                        message = "操作失败";
                    }
                }
                
                // Use our showToast method which already has empty-check
                showToast(context, message, R.drawable.toast_err);
            }
        } catch (JSONException e) {
            Log.e(TAG, "JSON parsing error in iniData: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            Log.e(TAG, "Error in iniData: " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 初始化多线路数据
     **/
    public static boolean iniData2(Response<String> response, Context context) {
        try {
            if (response == null || response.body() == null) {
                return false;
            }
            
            String responseBody = response.body(); // 获取响应体的字符串
            if (responseBody == null || responseBody.isEmpty()) {
                return false;
            }
            
            Log.d(TAG, "请求结果：" + context + "====data:" + responseBody);

            JSONObject jo = new JSONObject(responseBody); // 创建 JSON 对象
            
            // Check for errors and display message if needed
            if (jo.has("code") && jo.getInt("code") != 200) {
                String message = "操作失败";
                if (jo.has("msg")) {
                    message = jo.getString("msg");
                    if (message == null || message.trim().isEmpty()) {
                        message = "操作失败";
                    }
                }
                showToast(context, message, R.drawable.toast_err);
                return false;
            }

            return true;

        } catch (JSONException e) {
            Log.e(TAG, "JSON parsing error in iniData2: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            Log.e(TAG, "Error in iniData2: " + e.getMessage());
            e.printStackTrace();
        }

        return false;
    }

    /**
     * 显示升级提示的对话框
     */
    public static void HomeDialog(Context context, String text) {

        StringBuilder str = new StringBuilder();
        HomeDialog.Builder builder = new HomeDialog.Builder(context);
        builder.setTitle("公告");
        String[] remarks = text.split(";");
        for (int i = 0; i < remarks.length; i++) {
            if (i == remarks.length - 1) {
                str.append(remarks[i]);
            } else {
                str.append(remarks[i]).append("\n");
            }
        }
        builder.setMessage(str.toString());
        builder.setPositiveButton("我知道了", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        builder.create().show();
    }

    /**
     * 检查服务器是否可用
     * @param serverUrl 服务器URL
     * @return 服务器是否可用
     */
    public static boolean isServerAvailable(String serverUrl) {
        if (serverUrl == null || serverUrl.isEmpty()) {
            return false;
        }
        
        try {
            // 使用简单的HTTP GET请求测试服务器连接
            HttpGet httpGet = new HttpGet(serverUrl);
            httpGet.setHeader("Connection", "close");
            httpGet.setHeader("User-Agent", "Mozilla/5.0");
            
            // 设置连接和读取超时
            DefaultHttpClient httpClient = new DefaultHttpClient();
            httpClient.getParams().setParameter("http.connection.timeout", 3000);
            httpClient.getParams().setParameter("http.socket.timeout", 5000);
            
            HttpResponse response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            
            // 2xx和3xx状态码表示服务器可访问
            return statusCode >= 200 && statusCode < 400;
        } catch (Exception e) {
            Log.e(TAG, "服务器连接测试失败: " + serverUrl, e);
            return false;
        }
    }
    
    /**
     * 根据服务器URL获取服务器名称
     * @param serverUrl 服务器URL
     * @return 服务器友好名称
     */
    public static String getServerName(String serverUrl) {
        if (serverUrl == null || serverUrl.isEmpty()) {
            return "未知服务器";
        }
        
        try {
            java.net.URL url = new java.net.URL(serverUrl);
            String host = url.getHost();
            
            // 由于运营商服务器URL相关常量已被移除，使用直接判断
            // 根据URL特征判断服务器类型
            if (serverUrl.toLowerCase().contains("mobile")) {
                return "移动网络服务器";
            } else if (serverUrl.toLowerCase().contains("unicom")) {
                return "联通网络服务器";
            } else if (serverUrl.toLowerCase().contains("telecom")) {
                return "电信网络服务器";
            } else if (serverUrl.toLowerCase().contains("backup")) {
                return "备用服务器";
            }
            
            // 返回主机名作为默认名称
            return host;
        } catch (Exception e) {
            Log.e(TAG, "解析服务器URL失败: " + serverUrl, e);
            return "未知服务器";
        }
    }

}
