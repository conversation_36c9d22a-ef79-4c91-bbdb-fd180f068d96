package com.github.tvbox.osc.beanry;

import static com.github.tvbox.osc.server.ControlManager.mContext;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.github.tvbox.osc.R;
import com.github.tvbox.osc.ui.activity.FastSearchActivity;
import com.github.tvbox.osc.util.BaseR;
import com.github.tvbox.osc.util.MMkvUtils;
import com.github.tvbox.osc.util.ToolUtils;
import com.github.tvbox.quickjs.JSObject;
import com.lzy.okgo.OkGo;
import com.lzy.okgo.callback.AbsCallback;
import com.lzy.okgo.model.Response;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.view.animation.BounceInterpolator;


public class ExchangeAdapter extends RecyclerView.Adapter<ExchangeAdapter.ViewHolder> {

    private List<ExchangeBean.MsgDTO> msg;
    private OnExchangeListener exchangeListener;

    // 兑换结果监听接口
    public interface OnExchangeListener {
        void onExchangeResult(boolean success, String message);
    }
    
    public void setOnExchangeListener(OnExchangeListener listener) {
        this.exchangeListener = listener;
    }

    public ExchangeAdapter(List<ExchangeBean.MsgDTO> msg) {
        this.msg = msg;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_user_shop, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, @SuppressLint("RecyclerView") int position) {
        holder.textView.setText(msg.get(position).name);
        //holder.fenNumTextView.setText(msg.get(position).fen_num + " 积分");//积分
        holder.fenNumTextView.setText(Math.abs(Integer.parseInt(msg.get(position).fen_num)) + " 积分");
        
        // 判断是否为VIP会员商品，如果是，则设置金色VIP卡背景
        boolean isVip = msg.get(position).name.contains("VIP") || 
                       msg.get(position).name.contains("会员") || 
                       msg.get(position).name.contains("vip");
        
        if (isVip) {
            // 设置整个卡片的背景为VIP金卡
            holder.group.setBackgroundResource(R.drawable.vip_card_activity_bg);
            
            // 尝试通过ID查找内部布局
            View innerLayout = null;
            try {
                innerLayout = holder.group.findViewById(R.id.item_bg);
            } catch (Exception e) {
                // 忽略可能的错误
            }
            
            if (innerLayout != null) {
                innerLayout.setBackgroundResource(android.R.color.transparent);
            }
            
            // 调整文字颜色以适应金色背景
            holder.textView.setTextColor(0xFF8B4513); // 深棕色
            holder.fenNumTextView.setTextColor(0xFF8B4513); // 深棕色
            
            // 尝试查找并调整兑换按钮的样式
            TextView exchangeBtn = null;
            try {
                exchangeBtn = holder.itemView.findViewById(R.id.tv_exchange_btn);
            } catch (Exception e) {
                // 忽略可能的错误
            }
            
            if (exchangeBtn != null) {
                exchangeBtn.setBackgroundResource(android.R.color.transparent);
                exchangeBtn.setTextColor(0xFF8B4513); // 深棕色
            }
        } else {
            // 非VIP卡片使用默认样式
            holder.group.setBackgroundResource(0); // 清除FrameLayout背景
            
            // 尝试通过ID查找内部布局
            View innerLayout = null;
            try {
                innerLayout = holder.group.findViewById(R.id.item_bg);
            } catch (Exception e) {
                // 忽略可能的错误
            }
            
            if (innerLayout != null) {
                innerLayout.setBackgroundResource(R.drawable.shape_user_search);
            }
            
            // 恢复默认文字颜色
            holder.textView.setTextColor(0xFFF9C690);
            holder.fenNumTextView.setTextColor(0xFFFFFFFF);
            
            // 尝试恢复兑换按钮默认样式
            TextView exchangeBtn = null;
            try {
                exchangeBtn = holder.itemView.findViewById(R.id.tv_exchange_btn);
            } catch (Exception e) {
                // 忽略可能的错误
            }
            
            if (exchangeBtn != null) {
                exchangeBtn.setBackgroundResource(R.drawable.shape_user_search);
                exchangeBtn.setTextColor(0xFFF9C690);
            }
        }
        
        //holder.jifensj.setText(msg.get(position).vip_num);//增加会员时间
        holder.group.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //商品列表点击事件
                //  Toast.makeText(v.getContext(), "兑换功能暂未开放", Toast.LENGTH_SHORT).show();

                ReUserBean userBean = MMkvUtils.loadReUserBean("");
                if (userBean != null && ToolUtils.getIsEmpty(userBean.msg.token)) {
                    // ToolUtils.HomeDialog(v.getContext(), userBean.msg.token);
                    recHarGe(userBean.msg.token, msg.get(position).id, v.getContext());
                } else {
                    Toast.makeText(v.getContext(), "请先登录哦", Toast.LENGTH_SHORT).show();
                    if (exchangeListener != null) {
                        exchangeListener.onExchangeResult(false, "请先登录哦");
                    }
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return msg.size();
    }

    public List<ExchangeBean.MsgDTO> getMsg() {
        return msg;
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView textView;
        TextView fenNumTextView;
        TextView jifensj;
        TextView jifenid;
        TextView jfappid;
        ViewGroup group;
        View selectedOverlay;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            group = (ViewGroup) itemView;
            textView = itemView.findViewById(R.id.tv_shop_title);//名字
            fenNumTextView = itemView.findViewById(R.id.tv_shop_price);//积分
            
            // 通过ID获取遮罩层视图
            selectedOverlay = itemView.findViewById(R.id.selected_overlay);
            if (selectedOverlay == null) {
                // 如果未找到ID，则使用原来的方法获取
                selectedOverlay = ((ViewGroup)itemView).getChildAt(1);
            }

            // 控件获取焦点缩放特效
            group.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View view, boolean hasFocus) {
                    AnimatorSet animatorSet = new AnimatorSet();
                    if (hasFocus) {
                        // 显示选中效果
                        selectedOverlay.setVisibility(View.VISIBLE);
                        
                        // 检查是否为VIP卡片，是的话给予更明显的发光效果
                        boolean isVip = textView.getText().toString().contains("VIP") || 
                                       textView.getText().toString().contains("会员") || 
                                       textView.getText().toString().contains("vip");
                        
                        // 缩放动画
                        float scale = isVip ? 1.08f : 1.05f; // VIP卡片放大更明显
                        ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(group, "scaleX", 1.0f, scale);
                        ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(group, "scaleY", 1.0f, scale);
                        // 设置动画持续时间为300毫秒
                        scaleXAnimator.setDuration(300);
                        scaleYAnimator.setDuration(300);
                        // 设置弹跳效果的插值器
                        scaleXAnimator.setInterpolator(new BounceInterpolator());
                        scaleYAnimator.setInterpolator(new BounceInterpolator());
                        // 添加动画到动画集合中
                        animatorSet.playTogether(scaleXAnimator, scaleYAnimator);
                        
                        // 为VIP卡片添加轻微旋转效果
                        if (isVip) {
                            ObjectAnimator rotationAnimator = ObjectAnimator.ofFloat(group, "rotation", 0f, 1f, 0f, -1f, 0f);
                            rotationAnimator.setDuration(300);
                            rotationAnimator.setInterpolator(new BounceInterpolator());
                            animatorSet.play(rotationAnimator).with(scaleXAnimator);
                        }
                    } else {
                        // 隐藏选中效果
                        selectedOverlay.setVisibility(View.INVISIBLE);
                        
                        boolean isVip = textView.getText().toString().contains("VIP") || 
                                       textView.getText().toString().contains("会员") || 
                                       textView.getText().toString().contains("vip");
                        
                        float scale = isVip ? 1.08f : 1.05f;
                        // 缩放动画
                        ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(group, "scaleX", scale, 1.0f);
                        ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(group, "scaleY", scale, 1.0f);
                        // 设置动画持续时间为300毫秒
                        scaleXAnimator.setDuration(300);
                        scaleYAnimator.setDuration(300);
                        // 设置弹跳效果的插值器
                        scaleXAnimator.setInterpolator(new BounceInterpolator());
                        scaleYAnimator.setInterpolator(new BounceInterpolator());
                        // 添加动画到动画集合中
                        animatorSet.playTogether(scaleXAnimator, scaleYAnimator);
                        
                        // 恢复旋转角度
                        if (isVip) {
                            ObjectAnimator rotationAnimator = ObjectAnimator.ofFloat(group, "rotation", 0f);
                            rotationAnimator.setDuration(150);
                            animatorSet.play(rotationAnimator).with(scaleXAnimator);
                        }
                    }
                    // 启动动画
                    animatorSet.start();
                }
            });
        }
    }

    //积分兑换茶茶QQ205888578
    private void recHarGe(String token, String id, Context context) {
        Log.d("token", "recHarGe: " + token);
        Log.d("id", "recHarGe: " + id);
        new Thread(() -> {
            String apiUrl = ToolUtils.setApi("get_fen");
            if (apiUrl == null || apiUrl.isEmpty()) {
                // API URL为空，显示错误信息
                if (context != null) {
                    ((android.app.Activity) context).runOnUiThread(() -> {
                        Toast.makeText(context, "服务器连接失败，请检查网络设置", Toast.LENGTH_SHORT).show();
                        if (exchangeListener != null) {
                            exchangeListener.onExchangeResult(false, "服务器连接失败，请检查网络设置");
                        }
                    });
                }
                return;
            }
            
            OkGo.<String>post(apiUrl)
                    .params("token", token)
                    .params("fid", id)  // 将fid修改为传入的id值
                    .params("t", System.currentTimeMillis() / 1000)  //
                    .params("sign", ToolUtils.setSign("token=" + token + "&fid="+id))
                    .execute(new AbsCallback<String>() {
                        @Override
                        public void onSuccess(Response<String> response) {
                            try {
                                JSONObject jo = new JSONObject(BaseR.decry_R(response.body()));
                                final boolean success = jo.getInt("code") == 200;
                                final String message = jo.has("msg") ? jo.getString("msg") : (success ? "兑换成功" : "兑换失败");
                                
                                if (context != null) {
                                    ((android.app.Activity) context).runOnUiThread(() -> {
                                        // 通过接口回调通知结果
                                        if (exchangeListener != null) {
                                            exchangeListener.onExchangeResult(success, message);
                                        }
                                        
                                        // 显示提示
                                        if (success) {
                                            ToolUtils.showToast(mContext, message, R.drawable.toast_smile);
                                        } else {
                                            ToolUtils.showToast(mContext, message, R.drawable.toast_err);
                                        }
                                    });
                                }
                            } catch (JSONException e) {
                                e.printStackTrace();
                                if (context != null) {
                                    ((android.app.Activity) context).runOnUiThread(() -> {
                                        if (exchangeListener != null) {
                                            exchangeListener.onExchangeResult(false, "数据解析错误");
                                        }
                                    });
                                }
                            }
                        }

                        @Override
                        public void onError(Response<String> error) {
                            if (context != null) {
                                ((android.app.Activity) context).runOnUiThread(() -> {
                                    Toast.makeText(context, "网络请求错误", Toast.LENGTH_SHORT).show();
                                    if (exchangeListener != null) {
                                        exchangeListener.onExchangeResult(false, "网络请求错误");
                                    }
                                });
                            }
                        }

                        @Override
                        public String convertResponse(okhttp3.Response response) throws Throwable {
                            assert response.body() != null;
                            return response.body().string();
                        }
                    });
        }).start();
    }
}