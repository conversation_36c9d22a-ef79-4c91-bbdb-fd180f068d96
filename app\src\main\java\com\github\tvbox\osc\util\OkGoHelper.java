package com.github.tvbox.osc.util;

import android.graphics.Bitmap;

import com.github.tvbox.osc.base.App;
import com.github.tvbox.osc.picasso.MyOkhttpDownLoader;
import com.github.tvbox.osc.util.SSL.SSLSocketFactoryCompat;
import com.github.tvbox.osc.picasso.CustomImageDownloader;
import com.lzy.okgo.OkGo;
import com.lzy.okgo.https.HttpsUtils;
import com.lzy.okgo.interceptor.HttpLoggingInterceptor;
import com.lzy.okgo.model.HttpHeaders;
import com.orhanobut.hawk.Hawk;
import com.squareup.picasso.OkHttp3Downloader;
import com.squareup.picasso.Picasso;

import java.io.File;
import java.io.IOException;
import java.security.cert.CertificateException;
import java.util.ArrayList;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;

import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.X509TrustManager;

import okhttp3.Cache;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import xyz.doikki.videoplayer.exo.ExoMediaSourceHelper;

/**
 * OkHttp客户端辅助类
 * 提供统一的OkHttp客户端实例，确保应用中使用一致的网络配置
 */
public class OkGoHelper {
    public static final long DEFAULT_MILLISECONDS = 8000;      //默认的超时时间
    // 视频流请求超时时间，较长
    public static final long VIDEO_TIMEOUT_MILLISECONDS = 30000; // 30秒
    // 连接池大小
    private static final int MAX_CONNECTIONS = 32;
    // 最大缓存大小 (200MB)
    private static final int MAX_CACHE_SIZE = 200 * 1024 * 1024;

    private static final int DEFAULT_CONNECT_TIMEOUT = 10; // 10秒连接超时
    private static final int DEFAULT_READ_TIMEOUT = 15;    // 15秒读取超时
    private static final int DEFAULT_WRITE_TIMEOUT = 15;   // 15秒写入超时
    
    private static OkHttpClient defaultClient;
    static OkHttpClient noRedirectClient = null;
    
    // 为直播流特别优化的客户端
    static OkHttpClient liveClient = null;
    
    /**
     * 获取默认配置的OkHttpClient实例
     * 单例模式，确保应用中使用同一个客户端实例
     */
    public static synchronized OkHttpClient getDefaultClient() {
        if (defaultClient == null) {
            // 创建并配置默认OkHttpClient
            defaultClient = defaultClient();
        }
        return defaultClient;
    }
    
    /**
     * 获取禁止重定向的OkHttpClient实例
     * 用于某些特殊场景需要手动处理重定向的情况
     */
    public static synchronized OkHttpClient getNoRedirectClient() {
        if (noRedirectClient == null) {
            noRedirectClient = new OkHttpClient.Builder()
                    .connectTimeout(DEFAULT_CONNECT_TIMEOUT, TimeUnit.SECONDS)
                    .readTimeout(DEFAULT_READ_TIMEOUT, TimeUnit.SECONDS)
                    .writeTimeout(DEFAULT_WRITE_TIMEOUT, TimeUnit.SECONDS)
                    .followRedirects(false)
                    .followSslRedirects(false)
                    .retryOnConnectionFailure(true)
                    .build();
        }
        return noRedirectClient;
    }
    
    /**
     * 创建自定义超时设置的OkHttpClient实例
     * 用于特殊情况下需要不同超时设置的请求
     */
    public static OkHttpClient getClientWithTimeout(int connectTimeout, int readTimeout, int writeTimeout) {
        return new OkHttpClient.Builder()
                .connectTimeout(connectTimeout, TimeUnit.SECONDS)
                .readTimeout(readTimeout, TimeUnit.SECONDS)
                .writeTimeout(writeTimeout, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }
    
    /**
     * 获取为大文件下载优化的OkHttpClient
     * 设置较长的超时和更多的连接重试
     */
    public static OkHttpClient getDownloadClient() {
        return new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }
    
    /**
     * 获取为直播流特别优化的OkHttpClient实例
     * 使用更长的超时和特殊的流媒体配置
     */
    public static synchronized OkHttpClient getLiveClient() {
        if (liveClient == null) {
            liveClient = new OkHttpClient.Builder()
                    .connectTimeout(10, TimeUnit.SECONDS)     // 连接超时适中
                    .readTimeout(60, TimeUnit.SECONDS)       // 读取超时较长，适合直播流
                    .writeTimeout(10, TimeUnit.SECONDS)
                    .retryOnConnectionFailure(true)          // 允许连接失败时重试
                    .followRedirects(true)
                    .followSslRedirects(true)
                    .sslSocketFactory(new SSLSocketFactoryCompat(SSLSocketFactoryCompat.trustAllCert), SSLSocketFactoryCompat.trustAllCert)
                    // 不设置缓存，避免直播流缓存占用空间
                    .connectionPool(new ConnectionPool(16, 5, TimeUnit.MINUTES))  // 连接池配置
                    .build();
        }
        return liveClient;
    }

    static void initExoOkHttpClient() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor("OkExoPlayer");

        if (Hawk.get(HawkConfig.DEBUG_OPEN, false)) {
            loggingInterceptor.setPrintLevel(HttpLoggingInterceptor.Level.BODY);
            loggingInterceptor.setColorLevel(Level.INFO);
        } else {
            loggingInterceptor.setPrintLevel(HttpLoggingInterceptor.Level.NONE);
            loggingInterceptor.setColorLevel(Level.OFF);
        }
        builder.addInterceptor(loggingInterceptor);

        builder.retryOnConnectionFailure(true);
        builder.followRedirects(true);
        builder.followSslRedirects(true);


        try {
            setOkHttpSsl(builder);
        } catch (Throwable th) {
            th.printStackTrace();
        }

        ExoMediaSourceHelper.getInstance(App.getInstance()).setOkClient(builder.build());
    }

    public static OkHttpClient dnsClient = null;

    public static ArrayList<String> dnsHttpsList = new ArrayList<>();


    public static String getDohUrl(int type) {
        switch (type) {
            case 1: {
                return "https://doh.pub/dns-query";
            }
            case 2: {
                return "https://dns.alidns.com/dns-query";
            }
            case 3: {
                return "https://doh.360.cn/dns-query";
            }
            case 4: {
                return "https://dns.adguard.com/dns-query";
            }
            case 5: {
                return "https://dns.quad9.net/dns-query";
            }
        }
        return "";
    }

    static void initDnsOverHttps() {
        dnsHttpsList.add("关闭");
        dnsHttpsList.add("腾讯");
        dnsHttpsList.add("阿里");
        dnsHttpsList.add("360");
        dnsHttpsList.add("AdGuard");
        dnsHttpsList.add("Quad9");
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor("OkExoPlayer");
        if (Hawk.get(HawkConfig.DEBUG_OPEN, false)) {
            loggingInterceptor.setPrintLevel(HttpLoggingInterceptor.Level.BODY);
            loggingInterceptor.setColorLevel(Level.INFO);
        } else {
            loggingInterceptor.setPrintLevel(HttpLoggingInterceptor.Level.NONE);
            loggingInterceptor.setColorLevel(Level.OFF);
        }
        builder.addInterceptor(loggingInterceptor);
        try {
            setOkHttpSsl(builder);
        } catch (Throwable th) {
            th.printStackTrace();
        }
        builder.cache(new Cache(new File(App.getInstance().getCacheDir().getAbsolutePath(), "dohcache"), 10 * 1024 * 1024));
        dnsClient = builder.build();
    }

    public static void init() {
        initDnsOverHttps();

        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor("OkGo");

        if (Hawk.get(HawkConfig.DEBUG_OPEN, false)) {
            loggingInterceptor.setPrintLevel(HttpLoggingInterceptor.Level.BODY);
            loggingInterceptor.setColorLevel(Level.INFO);
        } else {
            loggingInterceptor.setPrintLevel(HttpLoggingInterceptor.Level.NONE);
            loggingInterceptor.setColorLevel(Level.OFF);
        }

        builder.addInterceptor(loggingInterceptor);

        // 增加视频播放相关的超时时间
        builder.readTimeout(VIDEO_TIMEOUT_MILLISECONDS, TimeUnit.MILLISECONDS); // 30秒
        builder.writeTimeout(15000, TimeUnit.MILLISECONDS); // 15秒
        builder.connectTimeout(10000, TimeUnit.MILLISECONDS); // 10秒
        
        // 添加连接池配置，增加并发连接数
        builder.connectionPool(new ConnectionPool(MAX_CONNECTIONS, 5, TimeUnit.MINUTES));
        
        // 添加并发下载支持
        Dispatcher dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(64); // 增加最大请求数
        dispatcher.setMaxRequestsPerHost(16); // 增加单主机最大请求数
        builder.dispatcher(dispatcher);
        
        // 增加响应缓存大小
        File cacheDir = new File(App.getInstance().getCacheDir(), "httpCache");
        if (!cacheDir.exists()) {
            cacheDir.mkdirs();
        }
        builder.cache(new Cache(cacheDir, MAX_CACHE_SIZE));
        
        // 添加视频流特殊处理拦截器
        builder.addInterceptor(new VideoStreamInterceptor());

        try {
            setOkHttpSsl(builder);
        } catch (Throwable th) {
            th.printStackTrace();
        }

        HttpHeaders.setUserAgent("iTVBox/1.0 OkHttp/3.14.9");

        OkHttpClient okHttpClient = builder.build();
        OkGo.getInstance().setOkHttpClient(okHttpClient);
        
        // 初始化默认客户端
        defaultClient = okHttpClient;
        
        // 初始化无重定向客户端
        OkHttpClient.Builder noRedirectBuilder = new OkHttpClient.Builder(okHttpClient);
        noRedirectBuilder.followRedirects(false);
        noRedirectBuilder.followSslRedirects(false);
        noRedirectClient = noRedirectBuilder.build();

        initExoOkHttpClient();
        initPicasso(okHttpClient);
    }

    static void initPicasso(OkHttpClient client) {
        client.dispatcher().setMaxRequestsPerHost(10);
        MyOkhttpDownLoader downloader = new MyOkhttpDownLoader(client);
        Picasso picasso = new Picasso.Builder(App.getInstance())
                .downloader(downloader)
                .defaultBitmapConfig(Bitmap.Config.RGB_565)
                .build();
        Picasso.setSingletonInstance(picasso);
    }

    private static synchronized void setOkHttpSsl(OkHttpClient.Builder builder) {
        try {
            final X509TrustManager trustAllCert =
                    new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) throws CertificateException {
                        }

                        @Override
                        public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) throws CertificateException {
                        }

                        @Override
                        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                            return new java.security.cert.X509Certificate[]{};
                        }
                    };
            final SSLSocketFactory sslSocketFactory = new SSLSocketFactoryCompat(trustAllCert);
            builder.sslSocketFactory(sslSocketFactory, trustAllCert);
            builder.hostnameVerifier(HttpsUtils.UnSafeHostnameVerifier);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    static OkHttpClient defaultClient() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .readTimeout(15, TimeUnit.SECONDS)
                .writeTimeout(15, TimeUnit.SECONDS)
                .connectTimeout(15, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true) // 启用连接失败重试
                .followRedirects(true)
                .followSslRedirects(true)
                .sslSocketFactory(new SSLSocketFactoryCompat(SSLSocketFactoryCompat.trustAllCert), SSLSocketFactoryCompat.trustAllCert);
        
        // 添加更多重试机制
        builder.addInterceptor(chain -> {
            Request request = chain.request();
            int tryCount = 0;
            int maxRetry = 3; // 最大重试次数
            okhttp3.Response response = null;
            while (tryCount < maxRetry) {
                try {
                    if (tryCount > 0) {
                        System.out.println("OkHttp retrying... " + tryCount + "/" + maxRetry + " for url: " + request.url().toString());
                    }
                    response = chain.proceed(request);
                    if (response.isSuccessful()) {
                        break;
                    } else {
                        if (response.body() != null) {
                            response.body().close();
                        }
                        Thread.sleep(1000); // 重试前等待1秒
                    }
                } catch (Exception e) {
                    System.out.println("OkHttp retry error: " + e.getMessage());
                    if (response != null && response.body() != null) {
                        response.body().close();
                    }
                    try {
                        Thread.sleep(1000); // 重试前等待1秒
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("Interrupted during backoff", ie);
                    }
                }
                tryCount++;
            }
            
            if (response == null) {
                throw new IOException("Failed after " + maxRetry + " retries");
            }
            return response;
        });
        
        return builder.build();
    }
}
