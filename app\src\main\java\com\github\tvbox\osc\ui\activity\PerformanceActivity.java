package com.github.tvbox.osc.ui.activity;

import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.github.tvbox.osc.R;
import com.github.tvbox.osc.base.App;
import com.github.tvbox.osc.util.LOG;
import com.github.tvbox.osc.util.NetworkRequestManager;
import com.github.tvbox.osc.util.PerformanceMonitor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 性能监控界面
 * 显示各种性能数据，包括内存、网络、播放性能等
 */
public class PerformanceActivity extends AppCompatActivity {
    private static final String TAG = "PerformanceActivity";
    
    private ScrollView scrollView;
    private LinearLayout contentLayout;
    
    // 数据区域
    private LinearLayout memorySection;
    private LinearLayout networkSection;
    private LinearLayout playerSection;
    private LinearLayout appSection;
    
    // 刷新定时器
    private final Handler handler = new Handler();
    private Runnable refreshRunnable;
    private static final int REFRESH_INTERVAL = 5000; // 5秒刷新一次
    
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_performance);
        
        initView();
        initData();
    }
    
    private void initView() {
        LOG.i("初始化性能监控界面");
        
        // 主容器
        scrollView = findViewById(R.id.scrollView);
        contentLayout = findViewById(R.id.contentLayout);
        
        // 初始化各部分
        memorySection = findViewById(R.id.memorySection);
        networkSection = findViewById(R.id.networkSection);
        playerSection = findViewById(R.id.playerSection);
        appSection = findViewById(R.id.appSection);
        
        // 设置返回按钮
        findViewById(R.id.btnBack).setOnClickListener(v -> finish());
    }
    
    private void initData() {
        // 初始化刷新定时任务
        refreshRunnable = new Runnable() {
            @Override
            public void run() {
                refreshPerformanceData();
                handler.postDelayed(this, REFRESH_INTERVAL);
            }
        };
        
        // 立即更新一次数据
        refreshPerformanceData();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 开始定时刷新
        handler.postDelayed(refreshRunnable, REFRESH_INTERVAL);
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        // 停止定时刷新
        handler.removeCallbacks(refreshRunnable);
    }
    
    /**
     * 刷新性能数据
     */
    private void refreshPerformanceData() {
        // 使用Android自带的Log替代
        android.util.Log.d(TAG, "刷新性能数据");
        try {
            // 获取性能数据
            Map<String, Object> report = PerformanceMonitor.getInstance().getPerformanceReport();
            
            // 更新UI
            runOnUiThread(() -> {
                try {
                    // 清空之前的内容
                    memorySection.removeAllViews();
                    networkSection.removeAllViews();
                    playerSection.removeAllViews();
                    appSection.removeAllViews();
                    
                    // 更新内存数据
                    updateMemorySection(report);
                    
                    // 更新网络数据
                    updateNetworkSection(report);
                    
                    // 更新播放性能
                    updatePlayerSection(report);
                    
                    // 更新应用性能
                    updateAppSection(report);
                    
                    // 滚动到顶部
                    scrollView.smoothScrollTo(0, 0);
                } catch (Exception e) {
                    LOG.e("更新性能数据UI出错: " + e.getMessage());
                }
            });
        } catch (Exception e) {
            LOG.e("获取性能数据出错: " + e.getMessage());
        }
    }
    
    /**
     * 更新内存部分
     */
    @SuppressWarnings("unchecked")
    private void updateMemorySection(Map<String, Object> report) {
        Map<String, Object> memoryPerf = (Map<String, Object>) report.get("memoryPerformance");
        if (memoryPerf == null || memoryPerf.isEmpty()) {
            addTextToSection(memorySection, "暂无内存数据", true);
            return;
        }
        
        // 添加内存数据
        addTextToSection(memorySection, "总内存: " + memoryPerf.get("totalMemory"), false);
        addTextToSection(memorySection, "可用内存: " + memoryPerf.get("availableMemory"), false);
        addTextToSection(memorySection, "已用堆内存: " + memoryPerf.get("usedHeapMemory"), false);
        addTextToSection(memorySection, "最大堆内存: " + memoryPerf.get("maxHeapMemory"), false);
        
        boolean isLowMemory = (boolean) memoryPerf.getOrDefault("isLowMemory", false);
        String memoryStatus = isLowMemory ? "内存不足" : "内存充足";
        addTextToSection(memorySection, "内存状态: " + memoryStatus, false, isLowMemory ? Color.RED : Color.GREEN);
        
        // 内存趋势
        List<Map<String, Object>> trend = (List<Map<String, Object>>) memoryPerf.get("trend");
        if (trend != null && !trend.isEmpty()) {
            addTextToSection(memorySection, "内存趋势 (最近10次记录):", true);
            
            StringBuilder trendText = new StringBuilder();
            for (Map<String, Object> point : trend) {
                long time = (long) point.get("time");
                long usedMem = ((Number) point.get("usedMem")).longValue();
                long availMem = ((Number) point.get("availMem")).longValue();
                
                // 简化时间显示，只显示时:分
                String timeStr = String.format("%tR", time);
                
                trendText.append(timeStr).append(" - 已用: ").append(usedMem).append("MB, 可用: ")
                        .append(availMem).append("MB\n");
            }
            
            addTextToSection(memorySection, trendText.toString(), false);
        }
    }
    
    /**
     * 更新网络部分
     */
    @SuppressWarnings("unchecked")
    private void updateNetworkSection(Map<String, Object> report) {
        Map<String, Object> networkPerf = (Map<String, Object>) report.get("networkPerformance");
        if (networkPerf == null || networkPerf.isEmpty()) {
            addTextToSection(networkSection, "暂无网络数据", true);
            return;
        }
        
        // 添加基本网络统计
        long totalRequests = ((Number) networkPerf.getOrDefault("totalRequests", 0)).longValue();
        long successRequests = ((Number) networkPerf.getOrDefault("successRequests", 0)).longValue();
        long failedRequests = ((Number) networkPerf.getOrDefault("failedRequests", 0)).longValue();
        double averageTime = ((Number) networkPerf.getOrDefault("averageRequestTime", 0)).doubleValue();
        
        addTextToSection(networkSection, "总请求数: " + totalRequests, false);
        addTextToSection(networkSection, "成功请求数: " + successRequests, false);
        addTextToSection(networkSection, "失败请求数: " + failedRequests, false, failedRequests > 0 ? Color.RED : Color.BLACK);
        addTextToSection(networkSection, "平均请求时间: " + String.format("%.2f", averageTime) + " 毫秒", false);
        
        // 请求队列数据
        int queueSize = ((Number) networkPerf.getOrDefault("queueSize", 0)).intValue();
        int highPriorityRequests = ((Number) networkPerf.getOrDefault("highPriorityCount", 0)).intValue();
        int cacheMissRate = ((Number) networkPerf.getOrDefault("cacheMissRate", 0)).intValue();
        
        addTextToSection(networkSection, "当前请求队列: " + queueSize + " 个请求", false);
        addTextToSection(networkSection, "高优先级请求: " + highPriorityRequests + " 个", false);
        addTextToSection(networkSection, "缓存命中率: " + (100 - cacheMissRate) + "%", false);
        
        // 最近失败的请求
        List<String> recentFailures = (List<String>) networkPerf.get("recentFailures");
        if (recentFailures != null && !recentFailures.isEmpty()) {
            addTextToSection(networkSection, "最近失败的请求:", true);
            
            StringBuilder failuresText = new StringBuilder();
            for (String failure : recentFailures) {
                failuresText.append("• ").append(failure).append("\n");
            }
            
            addTextToSection(networkSection, failuresText.toString(), false, Color.RED);
        }
    }
    
    /**
     * 更新播放性能部分
     */
    @SuppressWarnings("unchecked")
    private void updatePlayerSection(Map<String, Object> report) {
        Map<String, Object> playerPerf = (Map<String, Object>) report.get("playerPerformance");
        if (playerPerf == null || playerPerf.isEmpty()) {
            addTextToSection(playerSection, "暂无播放性能数据", true);
            return;
        }
        
        boolean isPlaying = (boolean) playerPerf.getOrDefault("currentlyPlaying", false);
        int bufferingCount = ((Number) playerPerf.getOrDefault("totalBufferingCount", 0)).intValue();
        String bufferingTime = (String) playerPerf.getOrDefault("totalBufferingTime", "0 秒");
        
        addTextToSection(playerSection, "当前播放状态: " + (isPlaying ? "正在播放" : "未播放"), false);
        addTextToSection(playerSection, "总缓冲次数: " + bufferingCount, false);
        addTextToSection(playerSection, "总缓冲时间: " + bufferingTime, false);
        
        // 最近播放的视频
        List<Map<String, Object>> recentPlays = (List<Map<String, Object>>) playerPerf.get("recentPlays");
        if (recentPlays != null && !recentPlays.isEmpty()) {
            addTextToSection(playerSection, "最近播放的视频:", true);
            
            for (Map<String, Object> playInfo : recentPlays) {
                String url = (String) playInfo.get("url");
                String duration = (String) playInfo.get("duration");
                int playBufferingCount = ((Number) playInfo.getOrDefault("bufferingCount", 0)).intValue();
                String playBufferingTime = (String) playInfo.get("bufferingTime");
                String bufferingRatio = (String) playInfo.get("bufferingRatio");
                
                // 显示播放URL的一部分（避免太长）
                String displayUrl = url;
                if (url != null && url.length() > 50) {
                    displayUrl = url.substring(0, 20) + "..." + url.substring(url.length() - 20);
                }
                
                StringBuilder playDetails = new StringBuilder();
                playDetails.append("• 视频: ").append(displayUrl).append("\n");
                playDetails.append("  ◦ 播放时长: ").append(duration).append("\n");
                playDetails.append("  ◦ 缓冲次数: ").append(playBufferingCount).append("\n");
                playDetails.append("  ◦ 缓冲时间: ").append(playBufferingTime).append("\n");
                playDetails.append("  ◦ 缓冲比例: ").append(bufferingRatio).append("\n");
                
                addTextToSection(playerSection, playDetails.toString(), false);
            }
        }
    }
    
    /**
     * 更新应用性能部分
     */
    @SuppressWarnings("unchecked")
    private void updateAppSection(Map<String, Object> report) {
        Map<String, Object> appPerf = (Map<String, Object>) report.get("appPerformance");
        if (appPerf == null || appPerf.isEmpty()) {
            addTextToSection(appSection, "暂无应用性能数据", true);
            return;
        }
        
        float frameRate = ((Number) appPerf.getOrDefault("averageFrameRate", 0f)).floatValue();
        addTextToSection(appSection, "平均帧率: " + String.format("%.1f", frameRate) + " FPS", false);
        
        // 活动渲染时间
        Map<String, Long> activityRenderTimes = (Map<String, Long>) appPerf.get("activityRenderTimes");
        if (activityRenderTimes != null && !activityRenderTimes.isEmpty()) {
            addTextToSection(appSection, "活动渲染时间:", true);
            
            StringBuilder renderTimes = new StringBuilder();
            for (Map.Entry<String, Long> entry : activityRenderTimes.entrySet()) {
                String activityName = entry.getKey();
                long renderTime = entry.getValue();
                
                // 简化Activity名称，只显示类名
                String simpleName = activityName;
                int lastDot = activityName.lastIndexOf(".");
                if (lastDot >= 0 && lastDot < activityName.length() - 1) {
                    simpleName = activityName.substring(lastDot + 1);
                }
                
                renderTimes.append("• ").append(simpleName).append(": ")
                        .append(renderTime).append(" 毫秒\n");
            }
            
            addTextToSection(appSection, renderTimes.toString(), false);
        }
    }
    
    /**
     * 向部分添加文本
     */
    private void addTextToSection(LinearLayout section, String text, boolean isHeader) {
        addTextToSection(section, text, isHeader, Color.BLACK);
    }
    
    /**
     * 向部分添加文本（带颜色）
     */
    private void addTextToSection(LinearLayout section, String text, boolean isHeader, int textColor) {
        TextView textView = new TextView(this);
        textView.setText(text);
        textView.setTextColor(textColor);
        
        if (isHeader) {
            textView.setTextSize(16);
            textView.setPadding(0, 16, 0, 8);
        } else {
            textView.setTextSize(14);
            textView.setPadding(16, 4, 0, 4);
        }
        
        section.addView(textView);
    }
} 