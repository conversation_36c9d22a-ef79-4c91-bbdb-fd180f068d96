package com.github.tvbox.osc.util;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import com.github.tvbox.osc.base.App;
import com.github.tvbox.osc.bean.VodInfo;
import com.orhanobut.hawk.Hawk;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.lang.reflect.Field;

/**
 * 预加载功能助手
 * 用于检查预加载功能状态并提供诊断信息
 */
public class PreloadHelper {
    private static final String TAG = "TVBox-PreloadHelper";
    private static volatile PreloadHelper instance;
    private Handler handler;
    
    // 预加载监听器列表
    private List<SimplePreloadListener> preloadListeners = new ArrayList<>();
    
    private PreloadHelper() {
        handler = new Handler(Looper.getMainLooper());
    }
    
    public static PreloadHelper getInstance() {
        if (instance == null) {
            synchronized (PreloadHelper.class) {
                if (instance == null) {
                    instance = new PreloadHelper();
                }
            }
        }
        return instance;
    }
    
    /**
     * 预加载事件监听器接口
     */
    public interface SimplePreloadListener {
        /**
         * 预加载开始
         * @param url 预加载的URL
         * @param name 预加载内容名称
         */
        void onPreloadStarted(String url, String name);
        
        /**
         * 预加载进度更新
         * @param url 预加载的URL
         * @param name 预加载内容名称
         * @param progress 进度(0-100)
         */
        void onPreloadProgress(String url, String name, int progress);
        
        /**
         * 预加载完成
         * @param url 预加载的URL
         * @param name 预加载内容名称
         */
        void onPreloadCompleted(String url, String name);
        
        /**
         * 预加载失败
         * @param url 预加载的URL
         * @param name 预加载内容名称
         * @param errorMsg 错误信息
         */
        void onPreloadFailed(String url, String name, String errorMsg);
    }
    
    /**
     * 添加预加载监听器
     * @param listener 监听器实例
     */
    public void addPreloadListener(SimplePreloadListener listener) {
        if (listener != null && !preloadListeners.contains(listener)) {
            preloadListeners.add(listener);
            Log.d(TAG, "添加预加载监听器，当前监听器数量: " + preloadListeners.size());
        }
    }
    
    /**
     * 移除预加载监听器
     * @param listener 监听器实例
     */
    public void removePreloadListener(SimplePreloadListener listener) {
        if (listener != null) {
            preloadListeners.remove(listener);
            Log.d(TAG, "移除预加载监听器，当前监听器数量: " + preloadListeners.size());
        }
    }
    
    /**
     * 清除所有预加载监听器
     */
    public void clearPreloadListeners() {
        preloadListeners.clear();
        Log.d(TAG, "清除所有预加载监听器");
    }
    
    /**
     * 通知预加载开始
     */
    void notifyPreloadStarted(String url, String name) {
        Log.d(TAG, "🚀 预加载开始: " + name + " (" + url + ")");
        
        for (SimplePreloadListener listener : preloadListeners) {
            try {
                listener.onPreloadStarted(url, name);
            } catch (Exception e) {
                Log.e(TAG, "通知预加载开始异常", e);
            }
        }
    }
    
    /**
     * 通知预加载进度
     */
    void notifyPreloadProgress(String url, String name, int progress) {
        if (progress % 20 == 0) {
            Log.d(TAG, "⏳ 预加载进度: " + name + " - " + progress + "%");
        }
        
        for (SimplePreloadListener listener : preloadListeners) {
            try {
                listener.onPreloadProgress(url, name, progress);
            } catch (Exception e) {
                Log.e(TAG, "通知预加载进度异常", e);
            }
        }
    }
    
    /**
     * 通知预加载完成
     */
    void notifyPreloadCompleted(String url, String name) {
        Log.d(TAG, "✅ 预加载完成: " + name + " (" + url + ")");
        
        for (SimplePreloadListener listener : preloadListeners) {
            try {
                listener.onPreloadCompleted(url, name);
            } catch (Exception e) {
                Log.e(TAG, "通知预加载完成异常", e);
            }
        }
    }
    
    /**
     * 通知预加载失败
     */
    void notifyPreloadFailed(String url, String name, String errorMsg) {
        Log.e(TAG, "❌ 预加载失败: " + name + " - " + errorMsg);
        
        for (SimplePreloadListener listener : preloadListeners) {
            try {
                listener.onPreloadFailed(url, name, errorMsg);
            } catch (Exception e) {
                Log.e(TAG, "通知预加载失败异常", e);
            }
        }
    }
    
    /**
     * 检查预载功能的状态
     * 返回包含详细预载状态的报告字符串
     * @return 包含预载状态的报告字符串
     */
    public String checkPreloadStatus() {
        StringBuilder report = new StringBuilder();
        
        try {
            boolean isPreloadEnabled = isPreloadEnabled();
            report.append("预载功能状态: ").append(isPreloadEnabled ? "已启用" : "未启用").append("\n");
            
            if (!isPreloadEnabled) {
                return report.toString();
            }
            
            // 获取PreloadManager实例
            PreloadManager preloadManager = PreloadManager.getInstance();
            
            // 获取基本状态
            report.append("任务配置: 最大").append(preloadManager.getMaxTaskCount()).append("个并行任务\n");
            
            // 获取缓存状态
            long currentCacheSize = 0;
            long maxCacheSize = 0;
            try {
                currentCacheSize = com.github.tvbox.osc.cache.CacheManager.getCacheSize();
                maxCacheSize = com.github.tvbox.osc.cache.CacheManager.getMaxCacheSize();
                report.append("缓存状态: 当前大小").append(String.format("%.1f", currentCacheSize / 1024.0 / 1024.0))
                    .append("MB / 最大").append(String.format("%.1f", maxCacheSize / 1024.0 / 1024.0)).append("MB\n");
            } catch (Exception e) {
                report.append("获取缓存状态失败: ").append(e.getMessage()).append("\n");
            }
            
            // 获取队列状态
            try {
                Field runningTasksField = PreloadManager.class.getDeclaredField("runningTasks");
                runningTasksField.setAccessible(true);
                int runningTasks = runningTasksField.getInt(preloadManager);
                
                Field failedCountField = PreloadManager.class.getDeclaredField("failedCount");
                failedCountField.setAccessible(true);
                int failedCount = failedCountField.getInt(preloadManager);
                
                Field successCountField = PreloadManager.class.getDeclaredField("successCount");
                successCountField.setAccessible(true);
                int successCount = successCountField.getInt(preloadManager);
                
                report.append("预载任务: 运行中").append(runningTasks)
                    .append("个, 成功").append(successCount)
                    .append("个, 失败").append(failedCount).append("个\n");
            } catch (Exception e) {
                report.append("获取任务状态失败: ").append(e.getMessage()).append("\n");
            }
            
            // 视频内容预缓冲状态
            boolean isContentPreloadEnabled = preloadManager.isContentPreloadEnabled();
            report.append("\n视频内容预缓冲: ").append(isContentPreloadEnabled ? "已启用" : "未启用").append("\n");
            
            if (isContentPreloadEnabled) {
                // 安全地获取配置信息
                int preloadSegments = 3; // 默认值
                int maxPreloadSizeMB = 5; // 默认值，以MB为单位
                
                try {
                    // 使用反射获取这些私有常量
                    Field segmentsField = PreloadManager.class.getDeclaredField("PRELOAD_SEGMENTS");
                    segmentsField.setAccessible(true);
                    preloadSegments = segmentsField.getInt(null);
                    
                    Field sizeField = PreloadManager.class.getDeclaredField("MAX_PRELOAD_SIZE");
                    sizeField.setAccessible(true);
                    long maxSize = sizeField.getInt(null);
                    maxPreloadSizeMB = (int) (maxSize / 1024 / 1024);
                } catch (Exception e) {
                    Log.e(TAG, "获取预缓冲配置失败", e);
                }
                
                report.append("缓冲配置: 最大").append(maxPreloadSizeMB)
                    .append("MB, ").append(preloadSegments).append("个分片\n");
                
                // 获取预缓冲内容状态
                String contentStatus = getPreloadedContentStatus();
                if (!contentStatus.isEmpty()) {
                    report.append("\n").append(contentStatus);
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "检查预载状态时出错", e);
            report.append("检查预载状态时出错: ").append(e.getMessage());
        }
        
        return report.toString();
    }
    
    /**
     * 执行预加载功能测试
     * @param context 上下文
     */
    public void testPreloadFunction(Context context) {
        boolean isEnabled = Hawk.get(HawkConfig.CACHE_VIDEO_PRELOAD_ENABLE, true);
        
        if (!isEnabled) {
            showToast(context, "预加载功能已禁用，请先在设置中启用");
            return;
        }
        
        // 模拟创建一个VodInfo进行测试
        VodInfo testInfo = createTestVodInfo();
        
        if (testInfo != null) {
            showToast(context, "开始测试预加载功能...");
            
            // 测试通知
            String testUrl = "https://example.com/test/ep1";
            String testName = "测试剧集";
            
            // 通知预加载开始
            notifyPreloadStarted(testUrl, testName);
            
            // 执行预加载
            try {
                PreloadManager.getInstance().preloadNextSeries(testInfo, 1);
                
                // 模拟进度更新
                new Thread(() -> {
                    try {
                        for (int i = 0; i <= 100; i += 10) {
                            final int progress = i;
                            handler.post(() -> {
                                notifyPreloadProgress(testUrl, testName, progress);
                            });
                            Thread.sleep(300);
                        }
                        
                        // 通知预加载完成
                        handler.post(() -> {
                            notifyPreloadCompleted(testUrl, testName);
                        });
                    } catch (Exception e) {
                        handler.post(() -> {
                            notifyPreloadFailed(testUrl, testName, e.getMessage());
                        });
                    }
                }).start();
                
                // 延迟检查结果
                handler.postDelayed(() -> {
                    PreloadManager.getInstance().printStatistics();
                    showToast(context, "预加载测试完成，请查看日志");
                }, 5000);
            } catch (Exception e) {
                notifyPreloadFailed(testUrl, testName, e.getMessage());
                showToast(context, "预加载测试失败: " + e.getMessage());
                Log.e(TAG, "预加载测试失败", e);
            }
        } else {
            showToast(context, "创建测试数据失败");
        }
    }
    
    /**
     * 创建测试用的VodInfo
     */
    private VodInfo createTestVodInfo() {
        try {
            VodInfo vodInfo = new VodInfo();
            vodInfo.name = "预加载测试视频";
            vodInfo.id = "test_id";
            vodInfo.sourceKey = "test_source";
            vodInfo.playFlag = "测试线路";
            vodInfo.playIndex = 0;
            
            // 创建测试剧集
            VodInfo.VodSeries series1 = new VodInfo.VodSeries();
            series1.name = "第1集";
            series1.url = "https://example.com/test/ep1";
            
            VodInfo.VodSeries series2 = new VodInfo.VodSeries();
            series2.name = "第2集";
            series2.url = "https://example.com/test/ep2";
            
            // 添加剧集
            vodInfo.seriesMap.put(vodInfo.playFlag, java.util.Arrays.asList(series1, series2));
            
            return vodInfo;
        } catch (Exception e) {
            Log.e(TAG, "创建测试数据失败", e);
            return null;
        }
    }
    
    /**
     * 预加载第二集
     */
    public void preloadNextSeries(VodInfo vodInfo, int playIndex) {
        if (vodInfo == null) {
            Log.w(TAG, "预加载取消: 视频信息为空");
            return;
        }
        
        boolean isEnabled = Hawk.get(HawkConfig.CACHE_VIDEO_PRELOAD_ENABLE, true);
        if (!isEnabled) {
            Log.w(TAG, "预加载取消: 功能已禁用");
            return;
        }
        
        vodInfo.playIndex = playIndex;
        
        // 获取要预加载的下一集信息
        try {
            if (vodInfo.seriesMap != null && vodInfo.seriesMap.containsKey(vodInfo.playFlag)) {
                List<VodInfo.VodSeries> seriesList = vodInfo.seriesMap.get(vodInfo.playFlag);
                if (seriesList != null && !seriesList.isEmpty() && playIndex + 1 < seriesList.size()) {
                    VodInfo.VodSeries nextSeries = seriesList.get(playIndex + 1);
                    
                    // 通知预加载开始
                    notifyPreloadStarted(nextSeries.url, nextSeries.name);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "获取下一集信息失败", e);
        }
        
        // 调用预加载管理器执行预加载
        PreloadManager.getInstance().preloadNextSeries(vodInfo, 2);
        
        Log.i(TAG, "预加载请求已发送: " + vodInfo.name + ", 当前播放第" + (playIndex + 1) + "集");
    }
    
    /**
     * 显示Toast消息
     */
    private void showToast(Context context, String message) {
        handler.post(() -> {
            Toast.makeText(context != null ? context : App.getInstance(), 
                          message, Toast.LENGTH_SHORT).show();
        });
    }
    
    /**
     * 获取预缓冲视频内容的状态信息
     * @return 包含预缓冲内容状态的字符串
     */
    public String getPreloadedContentStatus() {
        StringBuilder report = new StringBuilder();
        
        try {
            if (!isPreloadEnabled()) {
                report.append("预载功能未启用，无法使用视频预缓冲\n");
                return report.toString();
            }
            
            if (!PreloadManager.getInstance().isContentPreloadEnabled()) {
                report.append("视频内容预缓冲功能未启用\n");
                return report.toString();
            }
            
            // 获取并打印预缓冲统计信息
            PreloadManager.getInstance().printContentStatistics();
            
            report.append("视频预缓冲状态:\n");
            
            int totalItems = 0;
            long totalSize = 0;
            
            // 使用反射获取预缓冲内容Map
            Field preloadedContentsField = PreloadManager.class.getDeclaredField("preloadedContents");
            preloadedContentsField.setAccessible(true);
            Object preloadedContentsObj = preloadedContentsField.get(PreloadManager.getInstance());
            
            if (preloadedContentsObj instanceof Map) {
                Map<String, PreloadManager.VideoPreloadInfo> preloadedContents = 
                    (Map<String, PreloadManager.VideoPreloadInfo>) preloadedContentsObj;
                
                totalItems = preloadedContents.size();
                
                if (totalItems == 0) {
                    report.append("当前没有视频预缓冲内容\n");
                } else {
                    // 显示前5个预缓冲项的详情
                    int count = 0;
                    for (PreloadManager.VideoPreloadInfo info : preloadedContents.values()) {
                        totalSize += info.bufferSize;
                        
                        if (count < 5) { // 只显示前5个
                            report.append(String.format("- %s: %d分片, %dKB, 进度%d%%\n", 
                                info.name, 
                                info.segmentCount,
                                info.bufferSize / 1024,
                                info.getProgress()));
                        }
                        count++;
                    }
                    
                    if (count > 5) {
                        report.append("...(还有").append(count - 5).append("项)\n");
                    }
                    
                    report.append(String.format("\n总计: %d项, 总大小: %.2fMB", 
                        totalItems, totalSize / 1024.0 / 1024.0));
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "获取预缓冲内容状态出错", e);
            report.append("获取预缓冲内容状态出错: ").append(e.getMessage());
        }
        
        return report.toString();
    }
    
    /**
     * 清空所有预缓冲内容
     * @param context 上下文
     */
    public void clearAllPreloadedContent(Context context) {
        try {
            if (!isPreloadEnabled()) {
                Toast.makeText(context, "预载功能未启用", Toast.LENGTH_SHORT).show();
                return;
            }
            
            if (!PreloadManager.getInstance().isContentPreloadEnabled()) {
                Toast.makeText(context, "视频内容预缓冲功能未启用", Toast.LENGTH_SHORT).show();
                return;
            }
            
            PreloadManager.getInstance().clearPreloadedContents();
            Toast.makeText(context, "已清空所有视频预缓冲内容", Toast.LENGTH_SHORT).show();
            
        } catch (Exception e) {
            Log.e(TAG, "清空预缓冲内容出错", e);
            Toast.makeText(context, "清空预缓冲内容出错: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * 在预载状态报告中包含预缓冲内容状态
     * @param report 现有报告字符串
     * @return 包含预缓冲内容状态的完整报告
     */
    public String appendPreloadedContentStatus(String report) {
        if (!isPreloadEnabled() || 
            !PreloadManager.getInstance().isContentPreloadEnabled()) {
            return report;
        }
        
        return report + "\n\n" + getPreloadedContentStatus();
    }
    
    /**
     * 检查预加载功能是否启用
     * @return 是否启用
     */
    public boolean isPreloadEnabled() {
        return Hawk.get(HawkConfig.CACHE_PLAY_URL_ENABLE, true);
    }
} 