package com.github.tvbox.osc.cache;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;

import java.io.Serializable;

/**
 * 缓存实体类
 * 增加了过期时间、创建时间、访问时间和大小字段，支持缓存过期和LRU策略
 */
@Entity(tableName = "cache", indices = {@Index("expireTime"), @Index("accessTime")})
public class Cache implements Serializable {
    @PrimaryKey(autoGenerate = false)
    @NonNull
    public String key;
    
    // 缓存数据
    public byte[] data;
    
    // 数据大小(字节)
    public long size;
    
    // 创建时间
    public long createTime;
    
    // 最后访问时间
    public long accessTime;
    
    // 最近使用时间（用于LRU）
    @ColumnInfo(name = "lastUsedTime")
    public long lastUsedTime;
    
    // 过期时间，0表示永不过期
    public long expireTime;
    
    // 缓存类型 (可用于区分不同类型的缓存数据)
    public int type;
    
    // 缓存优先级 (0-10，值越大优先级越高)
    public int priority;
    
    // 访问计数，记录缓存被访问的次数
    public int accessCount;
    
    @Ignore
    public Cache() {
        this.createTime = System.currentTimeMillis();
        this.accessTime = this.createTime;
        this.lastUsedTime = this.createTime;
        this.expireTime = 0; // 默认永不过期
        this.type = 0;
        this.priority = 0; // 默认优先级最低
        this.accessCount = 0; // 初始访问次数为0
    }
    
    public Cache(@NonNull String key, byte[] data) {
        this();
        this.key = key;
        this.data = data;
        this.size = data != null ? data.length : 0;
    }
    
    /**
     * 获取数据大小（兼容旧代码）
     * @return 数据大小
     */
    public long getSize() {
        return this.size;
    }
    
    /**
     * 获取缓存键（兼容旧代码）
     * @return 缓存键
     */
    public String getKey() {
        return this.key;
    }
    
    /**
     * 设置过期时间
     * @param expireMillis 过期时间(毫秒)，从当前时间算起
     * @return 当前缓存对象
     */
    public Cache setExpireTime(long expireMillis) {
        if (expireMillis > 0) {
            this.expireTime = System.currentTimeMillis() + expireMillis;
        } else {
            this.expireTime = 0; // 永不过期
        }
        return this;
    }
    
    /**
     * 更新访问时间
     */
    public void updateAccessTime() {
        long now = System.currentTimeMillis();
        this.accessTime = now;
        this.lastUsedTime = now;
        this.accessCount++;
    }
    
    /**
     * 判断缓存是否过期
     * @return 是否过期
     */
    public boolean isExpired() {
        return expireTime > 0 && System.currentTimeMillis() > expireTime;
    }
}
